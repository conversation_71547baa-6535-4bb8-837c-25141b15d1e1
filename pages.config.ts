import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  pages: [],
  globalStyle: {
    'enablePullDownRefresh': false,
    'onReachBottomDistance': 100, // https://www.cnblogs.com/e0yu/p/17962549
    'backgroundColor': '@bgColor',
    'backgroundColorBottom': '@bgColorBottom',
    'backgroundColorTop': '@bgColorTop',
    'backgroundTextStyle': '@bgTxtStyle',
    'navigationBarBackgroundColor': '#000000',
    'navigationBarTextStyle': '@navTxtStyle',
    'navigationBarTitleText': '省赚客',
    'navigationStyle': 'custom',
    'app-plus': {
      scrollIndicator: 'none',
      nvue: {
        animation: {
          duration: 0,
          timingFunc: 'linear',
        },
      },
    },
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^uni-(.*)': '@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)': 'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  condition: { // 模式配置，仅开发期间生效
    current: 0, // 当前激活的模式(list 的索引项)
    list: [
      {
        name: '11111111', // 模式名称
        path: 'pages/home/<USER>', // 启动页面，必选
        query: '', // 启动参数，在页面的onLoad函数里面得到
      },
    ],
  },
  // https://uniapp.dcloud.net.cn/collocation/pages.html#tabbar
  tabBar: {
    color: '#7A7E83',
    selectedColor: '#fa4126',
    borderStyle: 'white',
    iconWidth: '18px',
    backgroundColor: '#fff',
    list: [{
      pagePath: 'pages/home/<USER>',
      iconPath: 'static/shop.png',
      selectedIconPath: 'static/shop2.png',
      text: '首页',
    }, {
      pagePath: 'pages/rank/index',
      iconPath: 'static/cat.png',
      selectedIconPath: 'static/cat2.png',
      text: '榜单',
    }, {
      pagePath: 'pages/life/index',
      iconPath: 'static/life.png',
      selectedIconPath: 'static/life2.png',
      text: '吃喝玩乐',
    }, {
      pagePath: 'pages/circle/index',
      iconPath: 'static/cart.png',
      selectedIconPath: 'static/cart2.png',
      text: '分享',
    }, {
      pagePath: 'pages/personal/index',
      iconPath: 'static/mine.png',
      selectedIconPath: 'static/mine2.png',
      text: '我的',
    }],
  },
})
