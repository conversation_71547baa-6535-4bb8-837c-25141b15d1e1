import * as path from 'node:path'

import Components, { kebabCase } from '@uni-helper/vite-plugin-uni-components'

import AutoImport from 'unplugin-auto-import/vite'
import Uni from '@dcloudio/vite-plugin-uni'
import UniHelperComponents from '@uni-helper/vite-plugin-uni-components'
import UniHelperLayouts from '@uni-helper/vite-plugin-uni-layouts'
import UniHelperManifest from '@uni-helper/vite-plugin-uni-manifest'
import UniHelperPages from '@uni-helper/vite-plugin-uni-pages'
import { defineConfig } from 'vite'
import { viteVConsole } from 'vite-plugin-vconsole'
import uniPolyfill from 'vite-plugin-uni-polyfill'
import { appInfo } from './src/common/appInfo'

// https://vitejs.dev/config/
export default async () => {
  const UnoCSS = (await import('unocss/vite')).default

  return defineConfig({
    base: process.env.BATH_PATH || './',
    server: {
      port: 5320,
      proxy: {
        '/app': {
          target: appInfo.serverApi,
          changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/app/, ''),
        },
      },
    },
    plugins: [
    // eslint-disable-next-line style/multiline-ternary
      process.env.UNI_PLATFORM === 'h6' ? viteVConsole({
        entry: path.resolve('src/main.ts'),
        enabled: true,
        config: {
          maxLogNumber: 1000,
        // theme: 'dark',
        },
      }) : null,
      // https://github.com/uni-helper/vite-plugin-uni-manifest
      UniHelperManifest(),
      // https://github.com/uni-helper/vite-plugin-uni-pages
      UniHelperPages({
        dts: 'src/uni-pages.d.ts',
      }),
      // https://github.com/uni-helper/vite-plugin-uni-layouts
      UniHelperLayouts(),
      // https://github.com/uni-helper/vite-plugin-uni-components
      UniHelperComponents({
        dts: 'src/components.d.ts',
        directoryAsNamespace: true,
      }),
      Components({
        resolvers: [
          {
            type: 'component',
            resolve: (name: string) => {
              if (name.match(/^Wd[A-Z]/)) {
                const compName = kebabCase(name)
                return {
                  name,
                  from: `wot-design-uni/components/${compName}/${compName}.vue`,
                }
              }
            },
          },
        ],
      }),
      Uni(),
      // https://github.com/antfu/unplugin-auto-import
      AutoImport({
        imports: ['vue', '@vueuse/core', 'uni-app', {
          from: 'uni-mini-router',
          imports: ['createRouter', 'useRouter', 'useRoute'],
        }],
        dts: 'src/auto-imports.d.ts',
        dirs: ['src/composables', 'src/stores', 'src/utils'],
        vueTemplate: true,
      }),
      uniPolyfill(),
      // https://github.com/antfu/unocss
      // see unocss.config.ts for config
      UnoCSS(),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern',
        },
      },
    },
  })
}
