{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"ignoreDeprecations": "5.0", "lib": ["esnext", "dom"], "baseUrl": ".", "moduleResolution": "node", "paths": {"@/*": ["./src/*"]}, "types": ["@dcloudio/types", "@uni-helper/uni-app-types", "wot-design-uni/global.d.ts"], "sourceMap": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "vueCompilerOptions": {"experimentalRuntimeMode": "runtime-uni-app", "nativeTags": ["block", "component", "template", "slot"]}}