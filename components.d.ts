/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActivityCard: typeof import('./src/components/activity-card/activity-card.vue')['default']
    ActivityList: typeof import('./src/components/activity-list/activity-list.vue')['default']
    AdvertView: typeof import('./src/components/advert-view/advert-view.vue')['default']
    AppFooter: typeof import('./src/components/AppFooter.vue')['default']
    AppLogos: typeof import('./src/components/AppLogos.vue')['default']
    Carousel: typeof import('./src/components/Carousel/Carousel.vue')['default']
    Curtain: typeof import('./src/components/curtain/curtain.vue')['default']
    Filter: typeof import('./src/components/filter/filter.vue')['default']
    FilterPopup: typeof import('./src/components/filter-popup/filter-popup.vue')['default']
    GoodsCard: typeof import('./src/components/goods-card/goods-card.vue')['default']
    GoodsCard2: typeof import('./src/components/goods-card/goods-card2.vue')['default']
    Goodsinfo: typeof import('./src/components/goodsinfo/goodsinfo.vue')['default']
    GoodsList: typeof import('./src/components/goods-list/goods-list.vue')['default']
    GoodsOverlay: typeof import('./src/components/goods-overlay/goods-overlay.vue')['default']
    HiCounter: typeof import('./src/components/HiCounter.vue')['default']
    HotListGoods: typeof import('./src/components/hot-list-goods/hot-list-goods.vue')['default']
    IframeWraper: typeof import('./src/components/iframe-wraper/iframe-wraper.vue')['default']
    IkunQrcode: typeof import('./src/components/ikun-qrcode/ikun-qrcode.vue')['default']
    InputEntry: typeof import('./src/components/InputEntry.vue')['default']
    LoginTabs: typeof import('./src/components/login-tabs/login-tabs.vue')['default']
    NavSwiper: typeof import('./src/components/nav-swiper/nav-swiper.vue')['default']
    OverlaySearch: typeof import('./src/components/overlay-search/overlay-search.vue')['default']
    OverlaySearchWraper: typeof import('./src/components/overlay-search-wraper/overlay-search-wraper.vue')['default']
    PageWraper: typeof import('./src/components/page-wraper/page-wraper.vue')['default']
    Price: typeof import('./src/components/price/price.vue')['default']
    PrivacyPopup: typeof import('./src/components/privacy-popup/privacy-popup.vue')['default']
    ProtocolBox: typeof import('./src/components/protocol-box/protocol-box.vue')['default']
    SearchOverlay: typeof import('./src/components/search-overlay/search-overlay.vue')['default']
    SimpleModal: typeof import('./src/components/simpleModal/simpleModal.vue')['default']
    TkiQrcode: typeof import('./src/components/tkiQrcode/tkiQrcode.vue')['default']
    UniBackTop: typeof import('./src/components/uni-back-top/uni-back-top.vue')['default']
    UniGrid: typeof import('./src/components/uni-grid/uni-grid.vue')['default']
    UniGridItem: typeof import('./src/components/uni-grid-item/uni-grid-item.vue')['default']
    WmPoster: typeof import('./src/components/wmPoster/wmPoster.vue')['default']
  }
}
