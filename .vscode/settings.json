{
  // Enable the ESlint flat config support
  "eslint.experimental.useFlatConfig": true,
  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": false,
  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.organizeImports": "never"
  },
  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    {
      "rule": "style/*",
      "severity": "off"
    },
    {
      "rule": "*-indent",
      "severity": "off"
    },
    {
      "rule": "*-spacing",
      "severity": "off"
    },
    {
      "rule": "*-spaces",
      "severity": "off"
    },
    {
      "rule": "*-order",
      "severity": "off"
    },
    {
      "rule": "*-dangle",
      "severity": "off"
    },
    {
      "rule": "*-newline",
      "severity": "off"
    },
    {
      "rule": "*quotes",
      "severity": "off"
    },
    {
      "rule": "*semi",
      "severity": "off"
    }
  ],
  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml"
  ],
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "vite.config.*": "pages.config.*, manifest.config.*, uno.config.*, volar.config.*, *.env, .env.*"
  },
  "swaggerToTypes.savePath": "src/types/swagger-interfaces",
  "swaggerToTypes.swaggerJsonUrl": [
    {
      "title": "省赚客",
      "url": "http://127.0.0.1:9999/swagger.json"
    }
  ],
  "typescript.tsdk": "node_modules/typescript/lib"
}
