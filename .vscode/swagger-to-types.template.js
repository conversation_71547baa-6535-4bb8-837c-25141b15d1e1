/**
 * 首字母小写
 * @param {string} str
 */
function toUp(str) {
  if (typeof str !== 'string')
    return ''
  return str.slice(0, 1).toLowerCase() + str.slice(1)
}

function paramsItem(item, params) {
  // 项目标题(swaggerToTypes.swaggerJsonUrl[number].title) 为 demo-1 时忽略定制方案
  if (params.groupName === 'demo-1')
    return

  return `${toUp(item.name)}${item.required ? ':' : '?:'} ${item.type}`
}

module.exports = { paramsItem }
