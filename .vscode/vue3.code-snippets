{"Print unibest Vue3 SFC": {"scope": "vue", "prefix": "v3", "body": ["<template>", "  <view class=\"$1\">$1</view>", "</template>\n", "<script lang=\"ts\" setup>", "//$3", "</script>\n", "<style lang=\"scss\" scoped>", "//", "</style>\n", "<route lang=\"json5\" type=\"page\">", "{", "  style: { navigationBarTitleText: '$1' },", "}", "</route>\n"]}, "Print unibest style": {"scope": "vue", "prefix": "st", "body": ["<style lang=\"scss\" scoped>", "//", "</style>\n"]}, "Print unibest script": {"scope": "vue", "prefix": "sc", "body": ["<script lang=\"ts\" setup>", "//$3", "</script>\n"]}, "Print unibest template": {"scope": "vue", "prefix": "te", "body": ["<template>", "  <view class=\"\">$1</view>", "</template>\n"]}}