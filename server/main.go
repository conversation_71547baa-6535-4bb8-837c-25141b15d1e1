package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"runtime"
	"strings"
	"syscall"
	"time"
)

func main() {
	currentDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("获取当前目录失败：%v", err)
	}
	fmt.Println("当前目录:", currentDir)

	certDir := filepath.Join(currentDir, "server")
	certFile := filepath.Join(certDir, "cert.pem")
	keyFile := filepath.Join(certDir, "key.pem")

	if _, err := os.Stat(certFile); os.IsNotExist(err) {
		if err := generateCertAndKey(certFile, keyFile); err != nil {
			log.Fatalf("生成证书和密钥文件时出错：%v", err)
		}
	}

	if len(os.Args) > 1 && os.Args[1] == "build" {
		if err := runBuild(); err != nil {
			log.Printf("运行 'pnpm build' 时出错：%v", err)
		}
	}

	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "./dist/build/h5/"+r.URL.Path[1:])
	})

	go func() {
		fmt.Println("正在启动 HTTPS 服务器，监听端口 9999 ...")
		if err := http.ListenAndServeTLS(":9999", certFile, keyFile, nil); err != nil {
			log.Printf("启动 HTTPS 服务器时出错：%v", err)
		}
	}()

	openBrowser("https://127.0.0.1:9999")
	fmt.Println("正在运行 devtunnel host -p 9999 ...")

	go startDevtunnel()

	signalCh := make(chan os.Signal, 1)
	signal.Notify(signalCh, os.Interrupt, syscall.SIGTERM)

	sig := <-signalCh
	fmt.Printf("收到信号：%v\n", sig)

	fmt.Println("关闭浏览器...")
	closeBrowser("https://127.0.0.1:9999")

	os.Exit(0)
}

func generateCertAndKey(certFile, keyFile string) error {
	cmd := exec.Command("openssl", "req", "-x509", "-nodes", "-days", "365", "-newkey", "rsa:2048", "-keyout", keyFile, "-out", certFile)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("生成证书和密钥文件时出错：%v", err)
	}
	return nil
}

func runBuild() error {
	fmt.Println("执行 'pnpm run build' ...")
	cmd := exec.Command("pnpm", "run", "build")
	if err := cmd.Run(); err != nil {
		return err
	}
	return nil
}

func openBrowser(url string) {
	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "linux":
		cmd = exec.Command("xdg-open", url)
	case "windows":
		cmd = exec.Command("cmd", "/c", "start", url)
	case "darwin":
		cmd = exec.Command("open", url)
	default:
		log.Printf("不支持的操作系统：%s", runtime.GOOS)
		return
	}
	if err := cmd.Run(); err != nil {
		log.Printf("打开浏览器时出错：%v", err)
	}
}

func closeBrowser(url string) {
	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "linux", "darwin":
		cmd = exec.Command("killall", "Safari") // 修改为相应的浏览器进程名
	case "windows":
		cmd = exec.Command("taskkill", "/IM", "chrome.exe", "/F") // 修改为相应的浏览器进程名
	default:
		log.Println("不支持关闭浏览器的操作系统")
		return
	}
	if err := cmd.Run(); err != nil {
		log.Printf("关闭浏览器时出错：%v", err)
	}
}

func startDevtunnel() {
	time.Sleep(2 * time.Second) // 等待 HTTPS 服务器启动
	pathEnv := os.Getenv("PATH")
	paths := strings.Split(pathEnv, string(os.PathListSeparator))
	var devtunnelPath string
	for _, p := range paths {
		potentialPath := filepath.Join(p, "devtunnel.exe")
		if _, err := os.Stat(potentialPath); err == nil {
			devtunnelPath = potentialPath
			break
		}
	}
	if devtunnelPath != "" {
		devTunnelCmd := exec.Command(devtunnelPath, "host", "-p", "9999")
		if err := devTunnelCmd.Start(); err != nil {
			log.Printf("启动 devtunnel host 时出错：%v", err)
		}
	} else {
		log.Println("未找到 devtunnel 可执行文件")
	}
}
