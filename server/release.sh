#!/bin/bash

# 判断 dist/release/apk 文件夹是否存在
if [ ! -d "dist/release/apk" ]; then
  echo "错误：未找到 dist/release/apk 目录。请先在 HBuilderX 中构建 APK。"
  exit 1
fi

rm -rf release
cp -r dist/release release

# 读取 release/apk 目录下的文件，并格式化为 JSON 写入 release.json 文件
read_apk_files() {
  local apk_dir="release/apk"
  local json_file="server/release.json"
  local apk_files=()

  if [ ! -d "$apk_dir" ]; then
    echo "错误：未找到 release/apk 目录。"
    exit 1
  fi

  # 获取目录中的 APK 文件列表
  apk_files=("$apk_dir"/*)

  # 格式化数据并写入 JSON 文件
  echo "[" >"$json_file"
  for ((i = 0; i < ${#apk_files[@]}; i++)); do
    filename=$(basename "${apk_files[$i]}")
    platform=$(extract_platform "$filename")

    if [ $i -eq $((${#apk_files[@]} - 1)) ]; then
      echo "  { \"name\": \"$filename\", \"platform\": \"$platform\", \"downloadUrl\": \"/apk/$filename\" }" >>"$json_file"
    else
      echo "  { \"name\": \"$filename\", \"platform\": \"$platform\", \"downloadUrl\": \"/apk/$filename\" }," >>"$json_file"
    fi
  done
  echo "]" >>"$json_file"

  echo "APK 文件信息已写入到 $json_file"

  echo "release.json 的内容已替换到 release.html 文件中的 release.json 部分。"
}

# 从文件名中提取平台信息
extract_platform() {
  local filename="$1"
  platform=$(echo "$filename" | sed -n 's/.*_\([0-9A-Za-z]*\)_[0-9]*\.apk/\1/p') # 使用正则表达式提取平台信息
  if [ -z "$platform" ]; then
    platform="全平台通用"
  fi
  echo "$platform"
}

read_apk_files
