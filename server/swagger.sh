#!/bin/bash

# 打印第一步的进度信息
echo "正在下载Swagger文档..."

# 设置Swagger文档的URL和Cookie
SWAGGER_URL='https://szk.juwatech.cn/v2/api-docs?group=%E7%9C%81%E8%B5%9A%E5%AE%A2App%E5%BC%80%E5%8F%91%E7%BB%84'
COOKIE='SESSION=MWUzODFkZjEtZTg2Yi00OTdhLTg3YzUtZTdkYmIwNjE3YmRm'

# 发送GET请求下载Swagger文档，并保存为swagger.json文件
curl --location --request GET "$SWAGGER_URL" --header "Cookie: $COOKIE" -o swagger.json

# 下载完成后打印信息
echo "Swagger文档下载完成！"

# 打印第二步的进度信息
echo "正在启动HTTP服务器..."

# 检查端口是否被占用
check_port() {
    local port="$1"

    # 尝试使用 lsof 检查端口
    if command -v lsof >/dev/null 2>&1; then
        lsof -i ":$port" >/dev/null 2>&1
        return $?
    fi

    # 如果没有 lsof，尝试使用 netstat
    if command -v netstat >/dev/null 2>&1; then
        netstat -ln | grep ":$port " >/dev/null 2>&1
        return $?
    fi

    # 如果都没有，返回未占用
    return 1
}

# 杀死占用指定端口的进程
kill_port_process() {
    local port="$1"

    if command -v lsof >/dev/null 2>&1; then
        local pid=$(lsof -t -i ":$port" 2>/dev/null)
        if [ -n "$pid" ]; then
            echo "正在停止占用端口 $port 的进程 (PID: $pid)..."
            kill "$pid" 2>/dev/null
            sleep 1
            # 如果进程仍然存在，强制杀死
            if kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid" 2>/dev/null
            fi
            return 0
        fi
    fi

    return 1
}

# 跨平台浏览器启动函数
open_browser() {
    local url="$1"

    # 检测操作系统并使用相应的命令打开浏览器
    case "$(uname -s)" in
        Linux*)
            if command -v xdg-open >/dev/null 2>&1; then
                xdg-open "$url"
                return 0
            fi
            ;;
        Darwin*)
            if command -v open >/dev/null 2>&1; then
                open "$url"
                return 0
            fi
            ;;
        CYGWIN*|MINGW*|MSYS*)
            if command -v start >/dev/null 2>&1; then
                start "$url"
                return 0
            fi
            ;;
    esac

    # 如果无法自动打开浏览器，返回错误
    return 1
}

# 检查端口 9999 是否被占用
if check_port 9999; then
    echo "端口 9999 已被占用，正在尝试释放..."
    if kill_port_process 9999; then
        echo "端口已释放，继续启动服务器..."
        sleep 1
    else
        echo "警告: 无法自动释放端口，尝试继续启动..."
    fi
fi

# 启动HTTP服务器以提供Swagger文档的访问
npx http-serve -p 9999 &

# 等待服务器启动一小段时间
sleep 2

# 使用系统默认浏览器打开Swagger文档页面
if open_browser "http://localhost:9999/swagger.json"; then
    echo "HTTP服务器已启动并打开Swagger文档！"
else
    echo "HTTP服务器已启动！"
    echo "无法自动打开浏览器，请手动访问: http://localhost:9999/swagger.json"
fi
