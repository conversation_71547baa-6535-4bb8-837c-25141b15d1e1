<!DOCTYPE html>
<html lang="zh-CN"> <!-- 将语言设置为中文 -->

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>省赚客APK发布页面-by @h7ml</title>
  <script src="https://cdn.jsdelivr.net/npm/moment@2.30.1/moment.min.js"></script> <!-- 引入 Moment.js -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui/lib/theme-chalk/index.css"> <!-- 引入 Element UI 样式 -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script> <!-- 添加Three.js库 -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script> <!-- 引入axios -->
  <style>
    body {
      margin: 0;
      overflow: hidden;
      /* 设置body为全屏并隐藏溢出内容 */
    }

    #three-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      /* 将Three.js粒子效果设置为背景 */
    }

    #app {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      padding: 20px;
      z-index: 1;
      /* 使内容位于粒子效果之上 */
      position: relative;
      height: auto;
      width: auto;
    }

    .card {
      width: 300px;
      margin-bottom: 20px;
    }

    .h100 {
      height: 100vh;
      overflow: auto;
    }
  </style>
</head>

<body class="bg-gray-100">
  <div id="three-container"></div>
  <div id="app" class="container text-center m-auto md:mt-10">
    <div :class="{ 'h100': isMobile }" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      <div v-for="file in apkFiles" :key="file.name">
        <div class="bg-white rounded-lg shadow-md p-4">
          <div class="text-sm text-blue-600 mb-2">安装包适用平台: {{ file.platform }}</div>
          <div class="text-sm text-blue-600 mb-4">安装包构建时间: {{
            moment(file.name.split('.')[0].split('_')[6], "YYYYMMDDHHmmss").format("YYYY年MM月DD日 HH:mm:ss")}}</div>
            <a :href="file.downloadUrl" target="_blank"
            class="bg-blue-500 text-white px-3 py-1 rounded-lg hover:bg-blue-600 transition-colors duration-300">下载</a>
          <button @click="copyDownloadUrl(file.downloadUrl)" class="bg-gray-300 text-gray-700 px-3 py-1 rounded-lg ml-2 hover:bg-gray-400 transition-colors duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-6 h-6 fill-current inline-block mr-1">
              <path fill="none" d="M0 0h24v24H0z" />
              <path
                d="M17 18v2H7v-2H5V8h2V6h10v2h2v10h-2zm-2 0H9v-4h6v4zm0-6H9V6h6v6z" />
            </svg>
            复制地址
          </button>
        </div>
      </div>
    </div>


  </div>
  <div id="footer"
    class="cursor-pointer z-999 text-white absolute bottom-10 left-1/2 transform -translate-x-1/2 text-xl text-center md:text-left text-blue-500  hidden md:block">
    <a href="https://h5.juwatech.cn/release.html">省赚客Apk发布页</a>
    <br class="md:hidden" />
    <div class="flex justify-center items-center space-x-4 absolute bottom-20 left-1/2 transform -translate-x-1/2">
      <a href="https://github.com/h7ml" target="_blank" rel="noopener noreferrer">
        <svg class="w-8 h-8 fill-current text-white hover:text-blue-500 transition-colors duration-300"
          viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M12 2C6.47 2 2 6.47 2 12c0 4.42 2.86 8.17 6.84 9.49.5.09.68-.22.68-.48 0-.24-.01-.88-.01-1.73-2.5.46-3.03-1.2-3.03-1.2-.41-1.04-1-1.32-1-1.32-.81-.55.06-.54.06-.54.9.06 1.37.93 1.37.93.8 1.36 2.09.97 2.6.74.08-.57.31-.97.56-1.19-1.97-.22-4.04-.99-4.04-4.39 0-.97.35-1.77.93-2.39-.09-.23-.4-1.13.09-2.35 0 0 .74-.24 2.43.91.7-.2 1.45-.3 2.2-.3.75 0 1.51.1 2.2.3 1.69-1.15 2.43-.91 2.43-.91.49 1.22.18 2.12.09 2.35.58.62.93 1.42.93 2.39 0 3.41-2.08 4.17-4.06 4.39.32.27.61.82.61 1.66 0 1.2-.01 2.17-.01 2.47 0 .27.18.58.69.48A10.013 10.013 0 0 0 22 12c0-5.53-4.47-10-10-10z" />
        </svg>
      </a>
      <a href="https://app.juwatech.cn" class="text-white hover:text-blue-500 transition-colors duration-300">
        <svg class="w-8 h-8 fill-current text-white hover:text-blue-500 transition-colors duration-300"
          viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <!-- Home icon path -->
          <!-- 这里是 Home 图标的路径 -->
          <path d="M12 2L2 12h3v8h6v-6h4v6h6v-8h3L12 2zM14 18v-4h-4v4H8l4-4 4 4h-2z" />
        </svg>
      </a>
    </div>
    <script src="https://unpkg.com/vue@2/dist/vue.min.js"></script>
    <script>
      new Vue({
        el: '#app',
        data: {
          isMobile: false,
          apkFiles: []
        },
        mounted() {
          this.checkMobile();
          this.fetchReleaseData();
          this.renderThree();
        },
        methods: {
          copyDownloadUrl(url) {
            // 复制下载地址到剪贴板
            const textarea = document.createElement('textarea');
            textarea.value = "https://app.juwatech.cn/"+url;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            alert('下载地址已复制到剪贴板！');
          },
          fetchReleaseData() {
            axios.get('release.json') // 发起GET请求获取release.json数据
            .then(response => {
              this.apkFiles = response.data; // 将获取的数据赋值给apkFiles变量
            })
            .catch(error => {
              console.error('Error fetching release data:', error);
            });
          },
          checkMobile() {
            this.isMobile = window.innerWidth <= 768; // 假设移动端宽度为768px
          },
          renderThree() {
            // 在mounted生命周期中添加Three.js场景和粒子效果
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(
              75,
              window.innerWidth / window.innerHeight,
              1,
              1000
            );
            const renderer = new THREE.WebGLRenderer();
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('three-container').appendChild(renderer.domElement);

            const particleCount = 5000;
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);

            for (let i = 0; i < particleCount; i++) {
              positions[i * 3] = (Math.random() - 0.5) * 2000;
              positions[i * 3 + 1] = (Math.random() - 0.5) * 2000;
              positions[i * 3 + 2] = (Math.random() - 0.5) * 2000;

              colors[i * 3] = Math.random();
              colors[i * 3 + 1] = Math.random();
              colors[i * 3 + 2] = Math.random();
            }

            const geometry = new THREE.BufferGeometry();
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const particleMaterial = new THREE.PointsMaterial({
              size: 1,
              vertexColors: true
            });

            const particleSystem = new THREE.Points(geometry, particleMaterial);
            scene.add(particleSystem);

            camera.position.z = 1000;

            function animate() {
              requestAnimationFrame(animate);
              particleSystem.rotation.x += 0.001;
              particleSystem.rotation.y += 0.001;
              renderer.render(scene, camera);
            }

            animate();
          }
        },
        beforeDestroy() {
          window.removeEventListener('resize', this.checkMobile);
        }
      });
    </script>

</html>
