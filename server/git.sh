#!/bin/bash

# 从远程仓库拉取最新的代码到本地
git pull

# 使用 eslint 对代码进行 lint 检查，并修复可能存在的问题
# 这部分代码先注释掉，因为你暂时不需要执行 lint 检查
# if [[ "$OSTYPE" == "msys" ]]; then
#   # Windows 下的 NUL 用于丢弃输出
#   pnpm run lint > NUL
#   pnpm run lint:fix > NUL
# else
#   # *nix 系统下的 /dev/null 用于丢弃输出
#   pnpm run lint > /dev/null
#   pnpm run lint:fix > /dev/null
# fi

# 将所有修改添加到暂存区
git add .

# 提示用户输入提交信息
echo "请输入提交信息："
read commit_message

# 如果用户没有输入提交信息，则使用默认提交信息
if [ -z "$commit_message" ]; then
  commit_message="feat: "  # 这里可以修改为你想要的默认提交信息
fi

# 提交代码到本地仓库，提交信息为用户输入的内容
git commit -m "feat(autoCommit): $commit_message"

# 提交到远程仓库
git push origin dev


