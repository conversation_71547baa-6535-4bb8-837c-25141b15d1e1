#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传工具
支持上传IPA文件到指定API端点，返回markdown格式的文件链接

使用方法:
    python upload.py [目录路径] [选项]

示例:
    python upload.py ./ipa_files
    python upload.py ./ipa_files --auth-code your_code --upload-folder apps/
"""

import os
import sys
import glob
import argparse
import requests
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import json


class FileUploader:
    """文件上传器类"""

    def __init__(self,
                 api_url: str = "https://file.jhun.edu.kg/upload",
                 auth_code: str = "root",
                 base_domain: str = "https://file.jhun.edu.kg"):
        """
        初始化上传器

        Args:
            api_url: API上传地址
            auth_code: 认证码
            base_domain: 基础域名，用于构建完整链接
        """
        self.api_url = api_url
        self.auth_code = auth_code
        self.base_domain = base_domain

    def upload_file(self,
                   file_path: str,
                   server_compress: bool = True,
                   upload_channel: str = "telegram",
                   auto_retry: bool = True,
                   upload_name_type: str = "default",
                   return_format: str = "full",
                   upload_folder: str = "ipa/") -> Tuple[bool, str, Optional[Dict]]:
        """
        上传单个文件

        Args:
            file_path: 文件路径
            server_compress: 是否开启服务端压缩
            upload_channel: 上传渠道 (telegram, cfr2, s3)
            auto_retry: 是否开启自动重试
            upload_name_type: 文件命名方式 (default, index, origin, short)
            return_format: 返回格式 (default, full)
            upload_folder: 上传目录

        Returns:
            (成功状态, 消息, 响应数据)
        """
        if not os.path.exists(file_path):
            return False, f"文件不存在: {file_path}", None

        if not os.path.isfile(file_path):
            return False, f"路径不是文件: {file_path}", None

        # 构建请求参数
        params = {
            'authCode': self.auth_code,
            'serverCompress': str(server_compress).lower(),
            'uploadChannel': upload_channel,
            'autoRetry': str(auto_retry).lower(),
            'uploadNameType': upload_name_type,
            'returnFormat': return_format,
            'uploadFolder': upload_folder
        }

        # 构建请求头
        headers = {
            'User-Agent': 'FileUploader/1.0.0'
        }

        try:
            # 打开文件并上传
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f)}

                print(f"正在上传: {os.path.basename(file_path)}")
                response = requests.post(
                    self.api_url,
                    params=params,
                    files=files,
                    headers=headers,
                    timeout=300  # 5分钟超时
                )

                if response.status_code == 200:
                    try:
                        data = response.json()
                        if isinstance(data, list) and len(data) > 0 and 'src' in data[0]:
                            src = data[0]['src']
                            # 如果返回格式是default，需要添加域名
                            if return_format == 'default' and not src.startswith('http'):
                                full_url = f"{self.base_domain}{src}"
                            else:
                                full_url = src
                            return True, f"上传成功: {full_url}", data
                        else:
                            return False, f"响应格式错误: {data}", data
                    except json.JSONDecodeError:
                        return False, f"响应解析失败: {response.text}", None
                else:
                    return False, f"上传失败 (HTTP {response.status_code}): {response.text}", None

        except requests.exceptions.Timeout:
            return False, "上传超时", None
        except requests.exceptions.RequestException as e:
            return False, f"网络错误: {str(e)}", None
        except Exception as e:
            return False, f"未知错误: {str(e)}", None

    def find_ipa_files(self, directory: str) -> List[str]:
        """
        查找目录下的所有IPA文件

        Args:
            directory: 搜索目录

        Returns:
            IPA文件路径列表
        """
        if not os.path.exists(directory):
            print(f"错误: 目录不存在 - {directory}")
            return []

        if not os.path.isdir(directory):
            print(f"错误: 路径不是目录 - {directory}")
            return []

        # 搜索IPA文件
        pattern = os.path.join(directory, "**/*.ipa")
        ipa_files = glob.glob(pattern, recursive=True)

        return sorted(ipa_files)

    def upload_ipa_files(self,
                        directory: str,
                        upload_folder: str = "ipa/",
                        **kwargs) -> List[Dict]:
        """
        批量上传目录下的IPA文件

        Args:
            directory: IPA文件目录
            upload_folder: 上传到的目录
            **kwargs: 其他上传参数

        Returns:
            上传结果列表
        """
        ipa_files = self.find_ipa_files(directory)

        if not ipa_files:
            print(f"在目录 {directory} 中未找到IPA文件")
            return []

        print(f"找到 {len(ipa_files)} 个IPA文件")

        results = []
        successful_uploads = []
        failed_uploads = []

        for i, file_path in enumerate(ipa_files, 1):
            print(f"\n[{i}/{len(ipa_files)}] 处理文件: {os.path.basename(file_path)}")

            success, message, data = self.upload_file(
                file_path,
                upload_folder=upload_folder,
                **kwargs
            )

            result = {
                'file': os.path.basename(file_path),
                'path': file_path,
                'success': success,
                'message': message,
                'data': data
            }

            results.append(result)

            if success:
                successful_uploads.append(result)
                print(f"✅ {message}")
            else:
                failed_uploads.append(result)
                print(f"❌ {message}")

        # 输出汇总
        print(f"\n{'='*50}")
        print(f"上传完成汇总:")
        print(f"总文件数: {len(ipa_files)}")
        print(f"成功上传: {len(successful_uploads)}")
        print(f"上传失败: {len(failed_uploads)}")

        # 输出markdown格式的链接
        if successful_uploads:
            print(f"\n{'='*50}")
            print("成功上传的文件 (Markdown格式):")
            print("```markdown")
            for result in successful_uploads:
                # 从message中提取URL
                url = result['message'].replace('上传成功: ', '')
                filename = result['file']
                print(f"- [{filename}]({url})")
            print("```")

        if failed_uploads:
            print(f"\n{'='*50}")
            print("上传失败的文件:")
            for result in failed_uploads:
                print(f"❌ {result['file']}: {result['message']}")

        return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="IPA文件上传工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python upload.py ./ipa_files
  python upload.py ./ipa_files --auth-code your_code
  python upload.py ./ipa_files --upload-folder apps/ --return-format default
        """
    )

    parser.add_argument('directory',
                       help='包含IPA文件的目录路径')
    parser.add_argument('--auth-code',
                       default='root',
                       help='API认证码 (默认: root)')
    parser.add_argument('--api-url',
                       default='https://file.jhun.edu.kg/upload',
                       help='API上传地址')
    parser.add_argument('--base-domain',
                       default='https://file.jhun.edu.kg',
                       help='基础域名')
    parser.add_argument('--upload-folder',
                       default='ipa/',
                       help='上传目录 (默认: ipa/)')
    parser.add_argument('--upload-channel',
                       choices=['telegram', 'cfr2', 's3'],
                       default='telegram',
                       help='上传渠道 (默认: telegram)')
    parser.add_argument('--upload-name-type',
                       choices=['default', 'index', 'origin', 'short'],
                       default='default',
                       help='文件命名方式 (默认: default)')
    parser.add_argument('--return-format',
                       choices=['default', 'full'],
                       default='full',
                       help='返回格式 (默认: full)')
    parser.add_argument('--no-server-compress',
                       action='store_true',
                       help='禁用服务端压缩')
    parser.add_argument('--no-auto-retry',
                       action='store_true',
                       help='禁用自动重试')

    args = parser.parse_args()

    # 检查目录
    if not os.path.exists(args.directory):
        print(f"错误: 目录不存在 - {args.directory}")
        sys.exit(1)

    # 创建上传器
    uploader = FileUploader(
        api_url=args.api_url,
        auth_code=args.auth_code,
        base_domain=args.base_domain
    )

    # 执行上传
    results = uploader.upload_ipa_files(
        directory=args.directory,
        server_compress=not args.no_server_compress,
        upload_channel=args.upload_channel,
        auto_retry=not args.no_auto_retry,
        upload_name_type=args.upload_name_type,
        return_format=args.return_format,
        upload_folder=args.upload_folder
    )

    # 根据结果设置退出码
    failed_count = sum(1 for r in results if not r['success'])
    if failed_count > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
