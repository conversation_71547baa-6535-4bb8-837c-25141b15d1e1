# IPA文件上传工具使用说明

## 概述

这是一个Python脚本，用于批量上传IPA文件到指定的API端点，并返回markdown格式的文件链接。

## 功能特性

- ✅ 支持批量上传目录下的所有IPA文件
- ✅ 支持所有API参数配置
- ✅ 自动生成markdown格式的文件链接
- ✅ 详细的上传进度和结果反馈
- ✅ 完善的错误处理和重试机制
- ✅ 命令行参数支持

## 依赖要求

```bash
pip install requests
```

## 基本使用

### 1. 简单上传
```bash
python upload.py ./ipa_files
```

### 2. 自定义认证码
```bash
python upload.py ./ipa_files --auth-code your_auth_code
```

### 3. 指定上传目录
```bash
python upload.py ./ipa_files --upload-folder apps/
```

### 4. 完整参数示例
```bash
python upload.py ./ipa_files \
  --auth-code your_code \
  --upload-folder apps/ \
  --upload-channel telegram \
  --return-format full \
  --upload-name-type default
```

## 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `directory` | - | 包含IPA文件的目录路径（必需） |
| `--auth-code` | `root` | API认证码 |
| `--api-url` | `https://file.jhun.edu.kg/upload` | API上传地址 |
| `--base-domain` | `https://file.jhun.edu.kg` | 基础域名 |
| `--upload-folder` | `ipa/` | 上传目录 |
| `--upload-channel` | `telegram` | 上传渠道 (telegram/cfr2/s3) |
| `--upload-name-type` | `default` | 文件命名方式 (default/index/origin/short) |
| `--return-format` | `full` | 返回格式 (default/full) |
| `--no-server-compress` | - | 禁用服务端压缩 |
| `--no-auto-retry` | - | 禁用自动重试 |

## 输出格式

### 上传过程
```
找到 3 个IPA文件

[1/3] 处理文件: app1.ipa
正在上传: app1.ipa
✅ 上传成功: https://file.jhun.edu.kg/file/xxx.ipa

[2/3] 处理文件: app2.ipa
正在上传: app2.ipa
✅ 上传成功: https://file.jhun.edu.kg/file/yyy.ipa

[3/3] 处理文件: app3.ipa
正在上传: app3.ipa
❌ 上传失败 (HTTP 400): 文件格式不支持
```

### 汇总结果
```
==================================================
上传完成汇总:
总文件数: 3
成功上传: 2
上传失败: 1

==================================================
成功上传的文件 (Markdown格式):
```markdown
- [app1.ipa](https://file.jhun.edu.kg/file/xxx.ipa)
- [app2.ipa](https://file.jhun.edu.kg/file/yyy.ipa)
```

==================================================
上传失败的文件:
❌ app3.ipa: 上传失败 (HTTP 400): 文件格式不支持
```

## API参数说明

### 上传渠道 (uploadChannel)
- `telegram`: Telegram Bot 渠道（默认）
- `cfr2`: Cloudflare R2 渠道
- `s3`: S3 渠道

### 文件命名方式 (uploadNameType)
- `default`: 默认前缀_原名命名（默认）
- `index`: 仅前缀命名
- `origin`: 仅原名命名
- `short`: 短链接命名法

### 返回格式 (returnFormat)
- `full`: 完整链接格式（默认）
- `default`: /file/id格式

## 错误处理

脚本包含完善的错误处理机制：

- 文件不存在检查
- 网络超时处理（5分钟）
- HTTP状态码检查
- JSON响应解析
- 自动重试机制（可配置）

## 退出码

- `0`: 所有文件上传成功
- `1`: 有文件上传失败或发生错误

## 注意事项

1. 确保目录中包含`.ipa`文件
2. 检查网络连接和API可用性
3. 验证认证码的有效性
4. 大文件上传可能需要较长时间
5. 建议在上传前备份重要文件

## 故障排除

### 常见问题

1. **"目录不存在"错误**
   - 检查目录路径是否正确
   - 使用绝对路径或相对路径

2. **"未找到IPA文件"**
   - 确认目录中包含`.ipa`文件
   - 检查文件扩展名是否正确

3. **"上传失败"错误**
   - 检查网络连接
   - 验证API地址和认证码
   - 查看具体错误信息

4. **"网络超时"错误**
   - 检查网络稳定性
   - 尝试较小的文件
   - 增加超时时间（修改代码中的timeout参数）

## 开发者信息

- 支持Python 3.6+
- 使用requests库进行HTTP请求
- 遵循PEP 8代码规范
- 包含完整的类型注解
