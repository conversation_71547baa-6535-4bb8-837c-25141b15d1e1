# 应用证书文件说明

本目录包含 JuwaTech 应用程序的证书文件，用于 Android 和 iOS 应用的签名和发布。

## 文件说明

### juwatech.keystore

这是 Android 应用签名所使用的密钥库文件。在构建 Android 应用的发布版本时，需要使用此文件进行签名，以确保应用的完整性和来源可靠性。

### juwatech.keystore.csr

证书签名请求文件 (Certificate Signing Request)，用于向证书颁发机构申请数字证书。这个文件包含了申请者的信息，如：

- 国家：CN (中国)
- 省份：zhejiang
- 城市：hangzhou
- 组织：juwatech
- 部门：develop team
- 域名：juwatech.cn
- 邮箱：<<EMAIL>>

## 使用方法

### Android 应用签名

在使用 Android Studio 或命令行工具构建发布版本时，可以按照以下步骤使用 keystore 文件：

1. 在 uni-app 项目的 manifest.json 中配置应用签名信息：

```json
{
  "app-plus": {
    "distribute": {
      "android": {
        "keystore": "证书文件路径",
        "password": "证书密码",
        "aliasname": "别名",
        "schemes": ["应用自定义协议"]
      }
    }
  }
}
```

2. 或使用命令行进行签名：

```bash
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore juwatech.keystore -storepass 密码 应用包.apk 别名
```

### iOS 应用签名

iOS 应用签名需要使用从 Apple Developer Portal 下载的证书和配置文件，该目录中可能不直接包含这些文件，请按以下步骤操作：

1. 在 Apple Developer 账户中生成并下载开发证书和配置文件
2. 在 Xcode 中导入这些证书和配置文件
3. 在 uni-app 项目配置中指定相关配置

## 注意事项

- **安全警告**: 证书文件包含敏感信息，请勿将其公开分享或上传到公共代码仓库
- **备份建议**: 请妥善保管并备份这些证书文件，一旦丢失将无法更新已发布的应用
- **密码管理**: 密钥库密码和别名密码应妥善保管，建议使用安全的密码管理工具
- **有效期**: 注意检查证书的有效期，确保在过期前更新证书

## 证书更新

当证书过期或需要更换时，请按照以下步骤操作：

### Android 证书更新

1. 生成新的密钥库文件：

```bash
keytool -genkey -v -keystore juwatech.keystore -alias 别名 -keyalg RSA -keysize 2048 -validity 10000
```

2. 生成新的CSR文件 (如需要)：

```bash
keytool -certreq -keyalg RSA -alias 别名 -file juwatech.keystore.csr -keystore juwatech.keystore
```

### iOS 证书更新

在 Apple Developer Portal 重新生成并下载证书和配置文件，然后在 Xcode 中更新。

## 其他资源

- [uni-app 应用发布文档](https://uniapp.dcloud.io/tutorial/app-base-sdk.html)
- [Android 官方签名文档](https://developer.android.com/studio/publish/app-signing)
- [iOS 开发者计划文档](https://developer.apple.com/programs/)
