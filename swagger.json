{"swagger": "2.0", "info": {"description": "接口文档", "version": "1.0.0", "title": "省赚客"}, "host": "szk.juwatech.cn", "basePath": "/", "tags": [{"name": "APP更新", "description": "App Update Controller"}, {"name": "CPS活动", "description": "App Activity Controller"}, {"name": "分享海报模块", "description": "App Invitation Poster Controller"}, {"name": "发圈素材模块", "description": "App Circle Material Controller"}, {"name": "商品收藏", "description": "App Collect Controller"}, {"name": "商品模块", "description": "App Goods Controller"}, {"name": "团购模块", "description": "App Group Buy Controller"}, {"name": "提现模块", "description": "App Cash Controller"}, {"name": "淘宝客私域备案", "description": "App Tbk Publisher Controller"}, {"name": "用户模块", "description": "App User Controller"}, {"name": "营销位模块", "description": "App Banner Controller"}, {"name": "订单模块", "description": "App Order Controller"}, {"name": "资源模块", "description": "App Material Controller"}], "paths": {"/app/activity/chainActivity": {"get": {"tags": ["CPS活动"], "summary": "联盟CPS活动转链", "operationId": "chainActivityUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "活动id(主键ID)", "required": true, "type": "string", "x-example": "1"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«商品转链»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/activity/queryActivityList": {"get": {"tags": ["CPS活动"], "summary": "CPS活动集合列表", "operationId": "queryActivityListUsingGET", "produces": ["*/*"], "parameters": [{"name": "activeid", "in": "query", "description": "活动集合ID:dstop:电商会场榜集合;bybt:百亿补贴集合;cjhb:超级红包集合;dcjy:打车加油集合;", "required": true, "type": "string", "x-example": "dstop", "enum": ["bybt", "cjhb", "dcjy", "dstop"]}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«List«CpsActivity»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/banner/bannerList": {"get": {"tags": ["营销位模块"], "summary": "广告位信息列表", "operationId": "listUsingGET", "produces": ["*/*"], "parameters": [{"name": "position", "in": "query", "description": "banner广告位位置", "required": true, "type": "string", "x-example": "home_top", "enum": ["bendi_top", "home_center", "home_center_left", "home_center_right", "home_top", "personalCenter_down", "personalCenter_top"]}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«List«BannerVo»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/banner/jinGangList": {"get": {"tags": ["营销位模块"], "summary": "首页/频道页金刚区", "operationId": "channelJinGangListUsingGET", "produces": ["*/*"], "parameters": [{"name": "marketChannelType", "in": "query", "description": "频道页类型", "required": false, "type": "string", "x-example": "home", "enum": ["bendiChannel", "home"]}, {"name": "marketType", "in": "query", "description": "金刚区类型", "required": true, "type": "string", "x-example": "<PERSON><PERSON><PERSON><PERSON>", "enum": ["<PERSON><PERSON><PERSON><PERSON>"]}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«List«BannerVo»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/cash/appCashData": {"get": {"tags": ["提现模块"], "summary": "提现记录查询", "operationId": "appCashDataUsingGET", "produces": ["*/*"], "parameters": [{"name": "limit", "in": "query", "description": "分页条数", "required": true, "type": "integer", "format": "int32", "x-example": 10}, {"name": "pageNo", "in": "query", "description": "当前页数", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«提现记录»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/cash/apply": {"post": {"tags": ["提现模块"], "summary": "申请提现", "operationId": "applyUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "cash_money", "in": "query", "description": "1", "required": true, "type": "string", "x-example": "1"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«object»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/circleMaterial/queryHotKey": {"get": {"tags": ["发圈素材模块"], "summary": "获取热搜词", "operationId": "queryHotKeyUsingGET", "produces": ["*/*"], "parameters": [{"name": "minId", "in": "query", "description": "平台类型，默认1", "required": true, "type": "string", "x-example": "1"}, {"name": "tag", "in": "query", "description": "标识符：cycle用于搜索框轮流；search用于展示热门搜索", "required": false, "type": "string", "x-example": "cycle"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«List«string»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/circleMaterial/queryPlatformFriendsMaterial": {"get": {"tags": ["发圈素材模块"], "summary": "分享发圈素材接口", "operationId": "queryPlatformFriendsMaterialUsingGET", "produces": ["*/*"], "parameters": [{"name": "limit", "in": "query", "description": "分页条数", "required": true, "type": "integer", "format": "int32", "x-example": 10}, {"name": "pageNo", "in": "query", "description": "当前页数", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "platformCode", "in": "query", "description": "平台类型", "required": true, "type": "string", "x-example": "TB"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«CircleMaterialVo»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/circleMaterial/queryTelegram": {"get": {"tags": ["发圈素材模块"], "summary": "每日线报", "operationId": "queryTelegramUsingGET", "produces": ["*/*"], "parameters": [{"name": "keyword", "in": "query", "description": "关键词", "required": false, "type": "string", "x-example": "衣服"}, {"name": "limit", "in": "query", "description": "分类", "required": true, "type": "string", "x-example": "10"}, {"name": "pageNo", "in": "query", "description": "分页", "required": true, "type": "string", "x-example": "1"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}, {"name": "type", "in": "query", "description": "默认0全部、10000券优惠、10001天猫超市、10002整点抢购、10003淘宝实时线报、10004京东实时线报", "required": true, "type": "string", "x-example": "0"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«TelegramVo»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goods/chainTransfer": {"get": {"tags": ["商品模块"], "summary": "文案转链", "operationId": "chainTransferUsingGET", "produces": ["*/*"], "parameters": [{"name": "keyword", "in": "query", "description": "转链文案", "required": true, "type": "string", "x-example": "https://chaoshi.detail.tmall.com/item.htm?id=687224264818"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«商品转链结果»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goods/chainTurning": {"get": {"tags": ["商品模块"], "summary": "高佣转链", "operationId": "chainTurningUsingGET", "produces": ["*/*"], "parameters": [{"name": "goodsId", "in": "query", "description": "商品ID（商品转链必填）", "required": true, "type": "string", "x-example": "666866452821"}, {"name": "platformCode", "in": "query", "description": "平台编号", "required": true, "type": "string", "x-example": "TB", "enum": ["DY", "JD", "KL", "PDD", "TB", "WPH"]}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«商品转链»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goods/clipBoardSearch": {"get": {"tags": ["商品模块"], "summary": "剪切板搜索", "operationId": "clipBoardSearchUsingGET", "produces": ["*/*"], "parameters": [{"name": "keyword", "in": "query", "description": "搜索关键字", "required": true, "type": "string", "x-example": "https://chaoshi.detail.tmall.com/item.htm?id=687224264818"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«商品列表»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goods/goodsDetail": {"get": {"tags": ["商品模块"], "summary": "商品详情", "operationId": "goodsDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "goodsId", "in": "query", "description": "商品ID", "required": true, "type": "string", "x-example": "r3ra0kpcOtDgbXyi6VrSrCZ-wXmWb03tQP9KbwRaTD"}, {"name": "platformCode", "in": "query", "description": "平台编号", "required": true, "type": "string", "x-example": "TB", "enum": ["DY", "JD", "PDD", "TB", "WPH"]}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«商品列表»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goods/list": {"get": {"tags": ["商品模块"], "summary": "商品列表（榜单）", "operationId": "listUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "limit", "in": "query", "description": "分页条数", "required": true, "type": "string", "x-example": "10"}, {"name": "pageNo", "in": "query", "description": "当前页数", "required": true, "type": "string", "x-example": "1"}, {"name": "platformCode", "in": "query", "description": "平台编号", "required": true, "type": "string", "x-example": "TB", "enum": ["DY", "JD", "KL", "PDD", "TB", "WPH"]}, {"name": "priceType", "in": "query", "description": "新增价格类型(1.精选专区；2.9.9专区；3.6.9专区；4.3.9专区（默认1）)", "required": false, "type": "string", "x-example": "1"}, {"name": "queryType", "in": "query", "description": "查询类型：list-榜单，tmall-天猫，lowprice - 精选低价，tmallchaoshi - 天猫超市", "required": false, "type": "string", "x-example": "choice"}, {"name": "queryValue", "in": "query", "description": "查询值：榜单（1(品牌榜)、5(高佣榜)、6(品牌实时榜)、7(定向计划)、8(细分类目榜-待使用)、11(热销榜)、12(今日榜)）", "required": false, "type": "string", "x-example": "1"}, {"name": "subQuery", "in": "query", "description": "子查询的值(子类目)", "required": false, "type": "string", "x-example": "0"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«商品列表»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goods/search": {"get": {"tags": ["商品模块"], "summary": "商品搜索", "operationId": "searchUsingGET", "produces": ["*/*"], "parameters": [{"name": "keyword", "in": "query", "description": "搜索关键字", "required": true, "type": "string", "x-example": "小米手机"}, {"name": "limit", "in": "query", "description": "分页条数", "required": true, "type": "string", "x-example": "10"}, {"name": "pageNo", "in": "query", "description": "当前页数", "required": true, "type": "string", "x-example": "1"}, {"name": "platformCode", "in": "query", "description": "平台编号", "required": true, "type": "string", "x-example": "TB", "enum": ["DY", "JD", "PDD", "QW", "TB", "WPH"]}, {"name": "priceRange", "in": "query", "description": "价格区间如：1-2  0.1-9.9", "required": false, "type": "string", "x-example": "0.1-9.9"}, {"name": "sortAsc", "in": "query", "description": "是否正向排序，默认true", "required": true, "type": "boolean", "x-example": "false"}, {"name": "sortColumn", "in": "query", "description": "排序字段：销量-sales，价格-price，佣金比例-rate", "required": false, "type": "string", "x-example": "sales"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«商品列表»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goodsCollect/batchDeleteCollect": {"get": {"tags": ["商品收藏"], "summary": "批量删除收藏的商品", "operationId": "batchDeleteCollectUsingGET", "produces": ["*/*"], "parameters": [{"name": "ids", "in": "query", "description": "商品渠道id", "required": true, "type": "string", "x-example": "多个ChannelGoodsId使用,分隔"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goodsCollect/batchSearchCollect": {"get": {"tags": ["商品收藏"], "summary": "批量查询收藏的商品", "operationId": "batchSearchCollectUsingGET", "produces": ["*/*"], "parameters": [{"name": "limit", "in": "query", "description": "分页条数", "required": true, "type": "integer", "format": "int32", "x-example": 10}, {"name": "pageNo", "in": "query", "description": "当前页数", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«商品列表»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goodsCollect/cancelCollect": {"get": {"tags": ["商品收藏"], "summary": "取消收藏的商品", "operationId": "cancelCollectUsingGET", "produces": ["*/*"], "parameters": [{"name": "goodsId", "in": "query", "description": "商品Id", "required": true, "type": "string", "x-example": "id"}, {"name": "platformCode", "in": "query", "description": "平台类型", "required": true, "type": "string", "x-example": "TB", "enum": ["DY", "JD", "PDD", "TB", "WPH"]}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goodsCollect/enabledCollect": {"get": {"tags": ["商品收藏"], "summary": "判断是否收藏", "operationId": "enabledCollectUsingGET", "produces": ["*/*"], "parameters": [{"name": "goodsId", "in": "query", "description": "商品Id", "required": true, "type": "string", "x-example": "id"}, {"name": "platformCode", "in": "query", "description": "平台类型", "required": true, "type": "string", "x-example": "TB", "enum": ["DY", "JD", "PDD", "TB", "WPH"]}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/goodsCollect/saveCollect": {"get": {"tags": ["商品收藏"], "summary": "收藏商品信息", "operationId": "saveCollectUsingGET", "produces": ["*/*"], "parameters": [{"name": "goodsId", "in": "query", "description": "商品Id", "required": true, "type": "string", "x-example": "id"}, {"name": "platformCode", "in": "query", "description": "平台类型", "required": true, "type": "string", "x-example": "TB", "enum": ["DY", "JD", "PDD", "TB", "WPH"]}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/groupBuy/chainGroupBuy": {"get": {"tags": ["团购模块"], "summary": "团购CPS转链&外卖红包转链", "operationId": "chainGroupBuyUsingGET", "produces": ["*/*"], "parameters": [{"name": "actId", "in": "query", "description": "活动物料ID(与商品ID二选一)；7(美团红包，channel传MTCPS)，10144(饿了么天天领红包，channel传ELMCPS)，10607(饿了么消费日红包，channel传ELMCPS)，10817(饿了么特价外卖红包，channel传ELMCPS)", "required": false, "type": "string", "x-example": "7"}, {"name": "bizLine", "in": "query", "description": "只有输入skuViewId时才需要传本字段；当字段platform为1，选择到家及其他业务类型时：5 医药，不填则默认null，表示外卖商品券；当字段platform为2，选择到店业务类型时：1、美食，2、休闲生活 ，3、酒店 ，4、门票", "required": false, "type": "string", "x-example": "1"}, {"name": "channel", "in": "query", "description": "渠道：MTCPS,ELMCPS,DYCPS", "required": true, "type": "string", "x-example": "MTCPS"}, {"name": "linkType", "in": "query", "description": "1、 H5长链接；2、 H5短链接；3、 deeplink(唤起)链接；4、 微信小程序唤起路径", "required": true, "type": "integer", "format": "int32", "x-example": 2}, {"name": "platform", "in": "query", "description": "输入skuViewId时才需要传本字段：1 到家及其他业务类型，2 到店业务类型", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "skuViewId", "in": "query", "description": "商品id(与物料ID二选一)", "required": false, "type": "string", "x-example": "1"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«商品转链»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/groupBuy/queryGoodsList": {"get": {"tags": ["团购模块"], "summary": "团购cps商品列表&搜索", "operationId": "queryGoodsListUsingGET", "produces": ["*/*"], "parameters": [{"name": "bizLine", "in": "query", "description": "当字段platform为1，到家及其他业务类型；当字段platform为2，到店业务类型时：1、美食，2、休闲生活，3、酒店，4、门票 不填则默认1", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "channel", "in": "query", "description": "渠道：MTCPS,DYCPS,ELMCPS", "required": true, "type": "string", "x-example": "MTCPS"}, {"name": "keyword", "in": "query", "description": "搜索关键词", "required": false, "type": "string"}, {"name": "latitude", "in": "query", "description": "纬度(保留6位小数)；针对到店、到家业务类型", "required": true, "type": "string"}, {"name": "limit", "in": "query", "description": "分页大小,默认20", "required": true, "type": "integer", "format": "int32", "x-example": 20}, {"name": "longitude", "in": "query", "description": "经度(保留6位小数)；针对到店、到家业务类型", "required": true, "type": "string"}, {"name": "pageNo", "in": "query", "description": "页数，默认页数1", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "platform", "in": "query", "description": "1 到家及其他业务类型，2 到店业务类型", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«商品列表»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/material/list": {"get": {"tags": ["资源模块"], "summary": "资源类型列表", "operationId": "listUsingGET_2", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}, {"name": "type", "in": "query", "description": "资源类型：4、常见问题", "required": true, "type": "string", "x-example": "4", "enum": ["4"]}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«List«资源信息»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/material/queryById": {"get": {"tags": ["资源模块"], "summary": "单个资源详情", "operationId": "queryUsingGET", "produces": ["*/*"], "parameters": [{"name": "id", "in": "query", "description": "167、推广规范；168、商务合作；169、专属客服；170、关于我们；171、隐私政策(对应分享链接https://szk.juwatech.cn/material/detail/171)", "required": true, "type": "integer", "format": "int64", "x-example": 171}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«资源信息»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/order/orderList": {"get": {"tags": ["订单模块"], "summary": "订单列表", "operationId": "orderListUsingGET", "produces": ["*/*"], "parameters": [{"name": "limit", "in": "query", "description": "分页条数", "required": true, "type": "integer", "format": "int32", "x-example": 10}, {"name": "orderType", "in": "query", "description": "订单类型,0全部订单,1我的订单,2团队订单(推广订单)", "required": true, "type": "integer", "format": "int32", "x-example": 1, "enum": [0, 1, 2]}, {"name": "pageNo", "in": "query", "description": "当前页数", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "status", "in": "query", "description": "订单状态,0全部、3结算、12付款、13失效、16收货", "required": true, "type": "integer", "format": "int32", "x-example": 0}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«用户订单»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/poster/posterList": {"get": {"tags": ["分享海报模块"], "summary": "分享海报列表", "operationId": "posterListUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«InvitationPosterVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/poster/upload": {"post": {"tags": ["分享海报模块"], "summary": "分享的海报图片上传", "operationId": "uploadFileUsingPOST", "consumes": ["multipart/form-data"], "produces": ["*/*"], "parameters": [{"name": "img<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "海报ID(海报完整路径)", "required": true, "type": "string", "x-example": "https://szk-001.oss-cn-hangzhou.aliyuncs.com/poster/1.jpg"}, {"in": "body", "name": "myfile", "description": "二维码海报的本地路径", "required": true, "schema": {"type": "string", "format": "binary"}}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«string»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/privatePublisher/bind": {"get": {"tags": ["淘宝客私域备案"], "summary": "私域用户备案", "operationId": "bindUsingGET", "produces": ["*/*"], "parameters": [{"name": "code", "in": "query", "description": "用户授权登录后返回的code(与session二选一)", "required": false, "type": "string"}, {"name": "session", "in": "query", "description": "用户授权登录的session((与code二选一))", "required": false, "type": "string"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/privatePublisher/bindInfo": {"get": {"tags": ["淘宝客私域备案"], "summary": "查询用户备案信息", "operationId": "bindInfoUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«TkAppPublisher»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/update/check": {"get": {"tags": ["APP更新"], "summary": "检查新版本(仅用于安卓)", "operationId": "queryPlatformActivityUsingGET", "produces": ["*/*"], "parameters": [{"name": "current_version", "in": "query", "description": "app当前版本号(版本号必须为x.x.x格式)", "required": true, "type": "string", "x-example": "1.0.0"}, {"name": "device_id", "in": "query", "description": "当前设备号", "required": true, "type": "string", "x-example": "847395738478847"}, {"name": "package_name", "in": "query", "description": "包名", "required": true, "type": "string", "x-example": "cn.juwatech.szk"}, {"name": "system_type", "in": "query", "description": "手机操作系统类型", "required": true, "type": "string", "x-example": "android", "enum": ["android"]}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«更新信息»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/update/hideTeam": {"get": {"tags": ["APP更新"], "summary": "是否隐藏团队粉丝信息", "operationId": "hideTeamUsingGET", "produces": ["*/*"], "parameters": [{"name": "current_version", "in": "query", "description": "app当前版本号(版本号必须为x.x.x格式)", "required": true, "type": "string", "x-example": "1.0.0"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«string»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/feedback": {"post": {"tags": ["用户模块"], "summary": "用户意见建议反馈", "operationId": "feedbackUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "content", "in": "query", "description": "反馈的内容", "required": true, "type": "string"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«AppUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/login/accountPwd": {"post": {"tags": ["用户模块"], "summary": "手机号+密码登录", "operationId": "loginAccountPwdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "account", "in": "query", "description": "手机号", "required": true, "type": "string", "x-example": "***********"}, {"name": "pwd", "in": "query", "description": "密码", "required": true, "type": "string", "x-example": "******"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«AppUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/login/accountSmsCode": {"post": {"tags": ["用户模块"], "summary": "手机号+验证码登录", "operationId": "accountSmsCodeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "account", "in": "query", "description": "手机号", "required": true, "type": "string", "x-example": "***********"}, {"name": "smsCode", "in": "query", "description": "验证码", "required": true, "type": "string", "x-example": "******"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«AppUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/login/getSmsCode": {"post": {"tags": ["用户模块"], "summary": "申请验证码", "operationId": "getSmsCodeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "account", "in": "query", "description": "手机号", "required": true, "type": "string", "x-example": "***********"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«Map«string,object»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/logout": {"post": {"tags": ["用户模块"], "summary": "注销账号", "operationId": "logoutUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "account", "in": "query", "description": "账号", "required": true, "type": "string"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«AppUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/myfans": {"get": {"tags": ["用户模块"], "summary": "我的粉丝", "operationId": "myfansUsingGET", "produces": ["*/*"], "parameters": [{"name": "limit", "in": "query", "description": "分页条数", "required": true, "type": "integer", "format": "int32", "x-example": 10}, {"name": "pageNo", "in": "query", "description": "当前页数", "required": true, "type": "integer", "format": "int32", "x-example": 1}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«PageInfo«粉丝信息»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/setAliPayAccount": {"post": {"tags": ["用户模块"], "summary": "设置提现支付宝信息", "operationId": "setAliPayAccountUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "in": "query", "description": "支付宝账号", "required": true, "type": "string"}, {"name": "realName", "in": "query", "description": "真实姓名", "required": true, "type": "string"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«AppUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/setUserInfo": {"post": {"tags": ["用户模块"], "summary": "设置用户信息", "operationId": "setUserInfoUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "data", "in": "query", "description": "需要设置的数据", "required": true, "type": "string"}, {"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}, {"name": "type", "in": "query", "description": "类型", "required": true, "type": "string", "x-example": "pwd", "enum": ["inviteCode", "nick", "pwd", "wechat"]}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«AppUserInfo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/app/user/userInfo": {"get": {"tags": ["用户模块"], "summary": "获取已登录用户信息", "operationId": "userInfoUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "token", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResult«AppUserInfo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}}, "definitions": {"AppUserInfo": {"type": "object", "properties": {"account": {"type": "string", "description": "账号/电话号码"}, "aliPayAccount": {"type": "string", "description": "提现支付宝账号"}, "balance": {"type": "string", "description": "账户余额"}, "fansImg": {"type": "string", "description": "用户图像"}, "fansNick": {"type": "string", "description": "用户昵称"}, "inviteCode": {"type": "string", "description": "普通邀请码"}, "realName": {"type": "string", "description": "提现支付宝真实姓名"}, "regDateTime": {"type": "string", "description": "注册时间"}, "relationId": {"type": "string", "description": "淘宝渠道ID，不为空代表已授权，为空代表未授权"}, "superiorTkUserId": {"type": "integer", "format": "int32", "description": "上级用户ID"}, "tkUserLevel": {"type": "integer", "format": "int32", "description": "用户等级，1初级达人，2钻石达人，3超级达人"}, "token": {"type": "string", "description": "token"}, "userEnable": {"type": "integer", "format": "int32", "description": "用户状态，1-正常、2-禁用"}, "vipInviteCode": {"type": "string", "description": "VIP邀请码(普通邀请码和vip邀请码都可以邀请，vip邀请码有值时展示vip邀请码)"}, "wechatId": {"type": "string", "description": "微信号"}}, "title": "AppUserInfo"}, "BannerVo": {"type": "object", "properties": {"activityid": {"type": "string", "description": "推广活动id"}, "categoryname": {"type": "string", "description": "分类名称"}, "id": {"type": "integer", "format": "int64", "description": "banner id"}, "jingangname": {"type": "string", "description": "金刚位名称"}, "jump_type": {"type": "string", "description": "跳转类型"}, "jump_url": {"type": "string", "description": "跳转地址"}, "picture_url": {"type": "string", "description": "图片地址"}, "platformcode": {"type": "string", "description": "平台类型"}, "position": {"type": "string", "description": "图片位置"}, "tagurl": {"type": "string", "description": "标签地址"}}, "title": "BannerVo"}, "CircleMaterialVo": {"type": "object", "properties": {"actualPrice": {"type": "string", "description": "券后价"}, "actualPriceEnd": {"type": "string", "description": "预估到手（去除券和返利）"}, "comment": {"type": "string", "description": "朋友圈评论内容（表情未处理），多条评论用“|”做区分"}, "content": {"type": "string", "description": "单品导购内容（表情未处理）"}, "couponAmount": {"type": "number", "format": "float", "description": "优惠券金额"}, "couponEndTime": {"type": "string", "description": "优惠券结束时间"}, "couponStartTime": {"type": "string", "description": "优惠券开始时间"}, "couponUrl": {"type": "string", "description": "优惠券链接"}, "description": {"type": "string", "description": "推荐描述"}, "discount": {"type": "string", "description": "折扣"}, "estimateProfit": {"type": "string", "description": "预估收益（分享赚）"}, "goodsItemTitle": {"type": "string", "description": "商品主标题"}, "goodsType": {"type": "integer", "format": "int64", "description": "商品类型-淘宝：0-淘宝，1-天猫，京东：0-非自营，1-自营"}, "id": {"type": "string", "description": "商品id"}, "imageUrls": {"type": "array", "description": "商品小图列表", "items": {"type": "string"}}, "itemPic": {"type": "array", "description": "多张宝贝图片，含实拍图", "items": {"type": "string"}}, "itemSale": {"type": "string", "description": "月销量"}, "itemTitle": {"type": "string", "description": "发圈素材中的宝贝标题"}, "maxProfit": {"type": "string", "description": "最大预估收益"}, "orgPrice": {"type": "string", "description": "原价"}, "picUrl": {"type": "string", "description": "商品主图"}, "platformCode": {"type": "string", "description": "平台编号"}, "savedAmount": {"type": "string", "description": "购买省"}, "shopTitle": {"type": "string", "description": "店铺名称"}, "shopType": {"type": "string", "description": "店铺种类 天猫B，淘宝店C"}, "showTime": {"type": "string", "description": "展示时间"}, "soLaImage": {"type": "string", "description": "单张图片"}, "title": {"type": "string", "description": "发圈素材中的宝贝标题"}, "tkRates": {"type": "string", "description": "佣金比例"}}, "title": "CircleMaterialVo"}, "CpsActivity": {"type": "object", "properties": {"actid": {"type": "string"}, "appshow": {"type": "integer", "format": "int32", "description": "app是否展示  1展示   2不展示"}, "create_time": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "end_time": {"type": "string", "description": "活动结束时间"}, "icon": {"type": "string"}, "id": {"type": "integer", "format": "int32", "description": "活动ID（联盟cps活动转链必传参数）"}, "img": {"type": "string", "description": "活动大图"}, "platform": {"type": "integer", "format": "int32", "description": "电商平台编号"}, "start_time": {"type": "string", "description": "活动开始时间"}, "state": {"type": "integer", "format": "int32", "description": "状态 1 已上线  2已下线"}, "title": {"type": "string", "description": "活动名称"}}, "title": "CpsActivity"}, "InvitationPosterVo": {"type": "object", "properties": {"inviteCode": {"type": "string", "description": "邀请码"}, "inviteUrl": {"type": "string", "description": "邀请链接"}, "picture_urls": {"type": "array", "description": "海报图片地址集合", "items": {"type": "string"}}}, "title": "InvitationPosterVo"}, "JsonResult«AppUserInfo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/AppUserInfo"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«AppUserInfo»"}, "JsonResult«InvitationPosterVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/InvitationPosterVo"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«InvitationPosterVo»"}, "JsonResult«List«BannerVo»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"type": "array", "description": "返回内容", "items": {"$ref": "#/definitions/BannerVo"}}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«List«BannerVo»»"}, "JsonResult«List«CpsActivity»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"type": "array", "description": "返回内容", "items": {"$ref": "#/definitions/CpsActivity"}}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«List«CpsActivity»»"}, "JsonResult«List«string»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"type": "array", "description": "返回内容", "items": {"type": "string"}}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«List«string»»"}, "JsonResult«List«资源信息»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"type": "array", "description": "返回内容", "items": {"$ref": "#/definitions/资源信息"}}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«List«资源信息»»"}, "JsonResult«Map«string,object»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"type": "object", "description": "返回内容"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«Map«string,object»»"}, "JsonResult«PageInfo«CircleMaterialVo»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/PageInfo«CircleMaterialVo»"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«PageInfo«CircleMaterialVo»»"}, "JsonResult«PageInfo«TelegramVo»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/PageInfo«TelegramVo»"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«PageInfo«TelegramVo»»"}, "JsonResult«PageInfo«商品列表»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/PageInfo«商品列表»"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«PageInfo«商品列表»»"}, "JsonResult«PageInfo«提现记录»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/PageInfo«提现记录»"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«PageInfo«提现记录»»"}, "JsonResult«PageInfo«用户订单»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/PageInfo«用户订单»"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«PageInfo«用户订单»»"}, "JsonResult«PageInfo«粉丝信息»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/PageInfo«粉丝信息»"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«PageInfo«粉丝信息»»"}, "JsonResult«TkAppPublisher»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/TkAppPublisher"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«TkAppPublisher»"}, "JsonResult«object»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"type": "object", "description": "返回内容"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«object»"}, "JsonResult«string»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"type": "string", "description": "返回内容"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«string»"}, "JsonResult«商品列表»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/商品列表"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«商品列表»"}, "JsonResult«商品转链»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/商品转链"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«商品转链»"}, "JsonResult«商品转链结果»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/商品转链结果"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«商品转链结果»"}, "JsonResult«更新信息»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/更新信息"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«更新信息»"}, "JsonResult«资源信息»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码"}, "data": {"description": "返回内容", "$ref": "#/definitions/资源信息"}, "msg": {"type": "string", "description": "返回消息"}}, "title": "JsonResult«资源信息»"}, "PageInfo«CircleMaterialVo»": {"type": "object", "properties": {"endRow": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/CircleMaterialVo"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "nextPage": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageInfo«CircleMaterialVo»"}, "PageInfo«TelegramVo»": {"type": "object", "properties": {"endRow": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/TelegramVo"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "nextPage": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageInfo«TelegramVo»"}, "PageInfo«商品列表»": {"type": "object", "properties": {"endRow": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/商品列表"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "nextPage": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageInfo«商品列表»"}, "PageInfo«提现记录»": {"type": "object", "properties": {"endRow": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/提现记录"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "nextPage": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageInfo«提现记录»"}, "PageInfo«用户订单»": {"type": "object", "properties": {"endRow": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/用户订单"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "nextPage": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageInfo«用户订单»"}, "PageInfo«粉丝信息»": {"type": "object", "properties": {"endRow": {"type": "integer", "format": "int32"}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}, "isFirstPage": {"type": "boolean"}, "isLastPage": {"type": "boolean"}, "list": {"type": "array", "items": {"$ref": "#/definitions/粉丝信息"}}, "navigateFirstPage": {"type": "integer", "format": "int32"}, "navigateLastPage": {"type": "integer", "format": "int32"}, "navigatePages": {"type": "integer", "format": "int32"}, "navigatepageNums": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "nextPage": {"type": "integer", "format": "int32"}, "pageNum": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "prePage": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "startRow": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageInfo«粉丝信息»"}, "TelegramVo": {"type": "object", "properties": {"contentList": {"type": "array", "description": "线报内容列表", "items": {"type": "object", "additionalProperties": {"type": "object"}}}, "images": {"type": "array", "description": "线报多图", "items": {"type": "string"}}, "platCode": {"type": "string", "description": "平台code"}, "platform": {"type": "integer", "format": "int32", "description": "平台类型：1. 淘宝、2. 京东"}, "startTime": {"type": "string", "description": "开始时间"}}, "title": "TelegramVo"}, "TkAppPublisher": {"type": "object", "properties": {"account_name": {"type": "string"}, "app_key": {"type": "string"}, "code": {"type": "string"}, "create_time": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "relation_id": {"type": "integer", "format": "int64"}, "session": {"type": "string"}, "session_expiration_time": {"type": "string", "format": "date-time"}, "special_id": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int32"}, "user_id": {"type": "integer", "format": "int64"}}, "title": "TkAppPublisher"}, "商品列表": {"type": "object", "properties": {"actualPrice": {"type": "string", "description": "实际/到手价格（去除券）"}, "channelGoodsId": {"type": "string", "description": "渠道侧的商品ID"}, "cid": {"type": "string", "description": "分类ID"}, "comparePriceState": {"type": "boolean", "description": "比价状态(主要针对淘宝拼多多)：true存在比价,false为正常"}, "couponAmount": {"type": "string", "description": "优惠券金额"}, "couponEndTime": {"type": "string", "description": "优惠券信息-优惠券结束时间 yyyy-MM-dd HH:mm:ss"}, "couponStartTime": {"type": "string", "description": "优惠券信息-优惠券开始时间 yyyy-MM-dd HH:mm:ss"}, "couponUrl": {"type": "string", "description": "优惠券地址"}, "description": {"type": "string", "description": "商品描述/推荐理由"}, "discount": {"type": "string", "description": "折扣(团购商品表示距离)"}, "goodsType": {"type": "integer", "format": "int64", "description": "商品类型-淘宝：0-淘宝，1-天猫，京东：0-非自营，1-自营"}, "goodsUrl": {"type": "string", "description": "商品地址"}, "grade": {"type": "integer", "format": "int32", "description": "用户等级"}, "id": {"type": "string", "description": "商品ID"}, "imageUrls": {"type": "array", "description": "商品小图列表", "items": {"type": "string"}}, "maxProfit": {"type": "string", "description": "最大预估收益"}, "needAuth": {"type": "boolean", "description": "是否需要备案授权"}, "orgPrice": {"type": "string", "description": "初始价格（电商平台的销售金额）"}, "picUrl": {"type": "string", "description": "商品主图"}, "platformCode": {"type": "string", "description": "平台编号"}, "profit": {"type": "string", "description": "预估收益"}, "ratio": {"type": "number", "format": "double", "description": "佣金比例"}, "salesCount": {"type": "string", "description": "销售数量"}, "savedAmount": {"type": "string", "description": "购买省"}, "shopTitle": {"type": "string", "description": "店铺名称"}, "subTitle": {"type": "string", "description": "商品副标题"}, "subdivisionId": {"type": "integer", "format": "int32", "description": "商品所属细分类目id"}, "subdivisionName": {"type": "string", "description": "商品所属细分类目名称"}, "subdivisionRank": {"type": "integer", "format": "int32", "description": "商品所属细分类目排名"}, "title": {"type": "string", "description": "商品标题"}, "tkl": {"type": "string", "description": "口令"}, "wxMiniAppId": {"type": "string", "description": "微信小程序ID"}, "wxMiniprogramPath": {"type": "string", "description": "微信小程序路径"}}, "title": "商品列表"}, "商品转链": {"type": "object", "properties": {"clickUrl": {"type": "string", "description": "商品转链(可点击)"}, "goodsUrl": {"type": "string", "description": "商品转链（口令）"}, "needAuth": {"type": "boolean", "description": "是否需要备案授权"}, "platformCode": {"type": "string", "description": "平台编号"}, "shortClickUrl": {"type": "string", "description": "短链接(可点击)"}, "wxMiniAppId": {"type": "string"}, "wxMiniAppSource": {"type": "string"}, "wxMiniprogramPath": {"type": "string"}, "wxQrcodeUrl": {"type": "string"}}, "title": "商品转链"}, "商品转链结果": {"type": "object", "properties": {"authUrl": {"type": "string", "description": "授权URL"}, "content": {"type": "string", "description": "转链完成后的内容：如果该字段为空则标识转链失败"}, "failureCount": {"type": "integer", "format": "int32", "description": "转链失败条数"}, "goodsListVoList": {"type": "array", "description": "转链成功的商品列表", "items": {"$ref": "#/definitions/商品列表"}}, "needAuth": {"type": "boolean", "description": "是否需要授权：true标识且平台编号不为空则需要授权"}, "platformCode": {"type": "string", "description": "转链平台编号"}, "successCount": {"type": "integer", "format": "int32", "description": "转链成功条数"}}, "title": "商品转链结果"}, "提现记录": {"type": "object", "properties": {"app_skaccount": {"type": "string", "description": "收款账号"}, "app_skuser": {"type": "string", "description": "收款名称"}, "cash_money": {"type": "string", "description": "提现金额"}, "cash_state": {"type": "integer", "format": "int32", "description": "状态，1待审核，2已拒绝，4已审核待打款，5已打款"}, "create_time": {"type": "string", "format": "date-time", "description": "创建时间"}, "update_time": {"type": "string", "format": "date-time", "description": "更新时间"}}, "title": "提现记录"}, "更新信息": {"type": "object", "properties": {"latest_version": {"type": "string", "description": "最新版本号"}, "mandatory": {"type": "boolean", "description": "是否强制更新"}, "message": {"type": "string", "description": "提示信息，当没有更新时返回说明信息"}, "update_available": {"type": "boolean", "description": "是否有新版本"}, "update_description": {"type": "string", "description": "更新内容的描述"}, "update_url": {"type": "string", "description": "新版本APK文件的下载地址"}}, "title": "更新信息"}, "用户订单": {"type": "object", "properties": {"childOrderId": {"type": "string", "description": "商品子订单号"}, "itemId": {"type": "string", "description": "商品id"}, "itemImg": {"type": "string", "description": "商品图片"}, "itemNum": {"type": "integer", "format": "int64", "description": "商品数量"}, "itemTitle": {"type": "string", "description": "商品标题"}, "orderEarningTime": {"type": "string", "format": "date-time", "description": "订单结算时间"}, "orderId": {"type": "string", "description": "商品主订单号"}, "orderPaidTime": {"type": "string", "format": "date-time", "description": "订单付款的时间，该时间同步淘宝，可能会略晚于买家在淘宝的订单创建时间"}, "payStatus": {"type": "integer", "format": "int32", "description": "订单状态( 3结算  12付款 13退款  15维权  16已收货)"}, "payTotalPrice": {"type": "number", "description": "买家拍下付款的金额"}, "platformId": {"type": "integer", "format": "int32", "description": "平台id  1 淘宝 2 京东 3拼多多 4美团  5唯品会  6抖音  7快手  8考拉  9苏宁 10CPS活动 11滴滴  12饿了么  13抖音团购"}, "refundTag": {"type": "integer", "format": "int32", "description": "0 含义为非维权 1 含义为维权订单"}, "shareFee": {"type": "number", "description": "预估佣金"}}, "title": "用户订单"}, "粉丝信息": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号"}, "create_time": {"type": "string", "format": "date-time", "description": "注册时间"}, "enabled": {"type": "string", "description": "用户是否激活(一个以上有效订单视为激活)，true：已激活，false：未激活"}, "fans_img": {"type": "string", "description": "用户头像"}, "fans_nick": {"type": "string", "description": "用户昵称"}, "tk_user_level": {"type": "integer", "format": "int32", "description": "用户等级，1初级达人，2钻石达人，3超级达人"}, "user_enable": {"type": "integer", "format": "int32", "description": "用户状态 1-正常、2-禁用"}}, "title": "粉丝信息"}, "资源信息": {"type": "object", "properties": {"content": {"type": "string", "description": "内容"}, "cover_url": {"type": "string", "description": "封面"}, "created_by": {"type": "string"}, "created_time": {"type": "string", "format": "date-time"}, "enabled": {"type": "integer", "format": "int32", "description": "是否激活  0激活  1未激活"}, "id": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "description": "备注"}, "sort": {"type": "integer", "format": "int32"}, "title": {"type": "string", "description": "标题"}, "type": {"type": "string", "description": "类型(1、关于我们，2、商务合作，3、推广规范，4、常见问题，5、专属客服，6、隐私政策)"}, "video_link": {"type": "string", "description": "视频链接"}}, "title": "资源信息"}}}