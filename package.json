{"name": "juwatech-uni-app", "version": "0.0.0", "private": true, "license": "MIT", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "uni", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build": "uni build", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:juwa": "uni build", "build:juwa:ssr": "uni build --ssr", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "postinstall": "npx simple-git-hooks", "swagger": "sh ./server/swagger.sh", "release": "sh ./server/release.sh", "git": "sh ./server/git.sh", "ncu": "npx npm-check-updates --reject @vueuse/core -u", "uvm": "npx @dcloudio/uvm"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@dcloudio/uni-ui": "1.5.6", "@iconify-json/mdi": "1.2.0", "@tanstack/vue-query": "5.56.2", "@uni-helper/uni-network": "0.19.3", "@uni-helper/uni-use": "0.19.14", "@vueuse/core": "9.13.0", "fast-querystring": "1.1.2", "html2canvas": "1.4.1", "qrcode-vue3": "1.7.1", "uni-mini-router": "0.1.6", "uni-parse-pages": "0.0.1", "uqrcodejs": "^4.0.7", "vconsole": "3.15.1", "vite-plugin-uni-polyfill": "0.1.0", "vue": "3.5.16", "vue-i18n": "9.14.4", "wot-design-uni": "1.3.10", "z-paging": "2.7.12"}, "devDependencies": {"@dcloudio/types": "3.0.4", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@iconify-json/carbon": "1.2.1", "@types/node": "22.5.5", "@uni-helper/eslint-config": "0.1.0", "@uni-helper/uni-app-types": "0.5.13", "@uni-helper/uni-env": "0.1.4", "@uni-helper/unocss-preset-uni": "0.2.9", "@uni-helper/vite-plugin-uni-components": "0.1.0", "@uni-helper/vite-plugin-uni-layouts": "0.1.10", "@uni-helper/vite-plugin-uni-manifest": "0.2.7", "@uni-helper/vite-plugin-uni-pages": "0.2.27", "@uni-helper/volar-service-uni-pages": "0.2.27", "@unocss/eslint-config": "0.62.4", "@vue/runtime-core": "3.5.16", "@vue/tsconfig": "0.5.1", "eslint": "9.11.0", "lint-staged": "15.2.10", "pnpm": "9.4.0", "sass": "1.78.0", "simple-git-hooks": "2.11.1", "typescript": "5.6.2", "uni-mini-ci": "0.0.11", "unocss": "0.62.4", "unplugin-auto-import": "0.18.3", "vite": "5.2.8", "vite-plugin-vconsole": "2.1.1", "vue-tsc": "2.1.6"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}