<h2 align="center">
juwatech-uni-app
</h2>
<p align="center">
  <a href="https://app.juwatech.cn/">📱 在线预览 juwatech </a>
  <a href="https://checkout.feishu.cn/docx/PPfhdscaeoOvctxOk0kcNzdWnIh">📖 阅读开发文档</a>
</p>

## 特性

- ⚡️ [Vue 3](https://github.com/vuejs/core), [Vite](https://github.com/vitejs/vite), [pnpm](https://pnpm.io/), [esbuild](https://github.com/evanw/esbuild) - 就是快！

- 🗂 [基于文件的路由](./src/pages)

- 📦 [组件自动化加载](./src/components)

- 📑 [布局系统](./src/layouts)

- 🎨 [UnoCSS](https://github.com/unocss/unocss) - 高性能且极具灵活性的即时原子化 CSS 引擎

- 😃 [各种图标集为你所用](https://github.com/antfu/unocss/tree/main/packages/preset-icons)

- 🔥 使用 [新的 `<script setup>` 语法](https://github.com/vuejs/rfcs/pull/227)

- 📥 [API 自动加载](https://github.com/antfu/unplugin-auto-import) - 直接使用 Composition API 无需引入

- 🦾 [TypeScript](https://www.typescriptlang.org/) & [ESLint](https://eslint.org/) - 保证代码质量

- 💻 [![action 集成](https://github.com/h7ml/juwatech/actions/workflows/check.yml/badge.svg?branch=main)](https://github.com/h7ml/juwatech/actions/workflows/check.yml)
