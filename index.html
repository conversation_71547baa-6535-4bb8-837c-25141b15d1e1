<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta name="description" content="省赚客是集成了淘宝、天猫、京东、拼多多、唯品会、苏宁、抖音、快手、美团、饿了么、话费、电费、打车、加油、电影票等本地生活主流消费购物领券平台，满足用户日常吃喝玩乐衣食住行的超级聚合导购省钱赚钱APP"/>
    <meta name="keywords" content="省钱赚钱APP,购物返利攻略, 购物优惠省钱网站, 购物省钱"/>
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
    <link rel="shortcut icon" href="static/logo.png" type="image/png" />
    <script>
      const coverSupport =
        'CSS' in window &&
        typeof CSS.supports === 'function' &&
        (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
      document.write(
        `<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0${
          coverSupport ? ', viewport-fit=cover' : ''
        }" />`,
      )
    </script>
    <title></title>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
