{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"ignoreDeprecations": "5.0", "lib": ["esnext", "dom"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["@dcloudio/types", "@uni-helper/uni-app-types"], "sourceMap": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "vueCompilerOptions": {"experimentalRuntimeMode": "runtime-uni-app", "nativeTags": ["block", "component", "template", "slot"]}}