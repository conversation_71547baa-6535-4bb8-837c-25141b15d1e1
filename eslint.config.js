const uni = require('@uni-helper/eslint-config')
const unocss = require('@unocss/eslint-plugin')

module.exports = uni(
  {
    ignores: [
      'node_modules',
      'dist',
      'src/utils/js_sdk/',
      '*.md',
      'src/uni_modules/',
    ],
  },
  unocss.configs.flat,
  {
    rules: {
      'eslint-comments/no-unlimited-disable': 'off',
      'no-console': 'off',
      'style/no-tabs': 'off',
      'style/no-mixed-spaces-and-tabs': 'off',
      'import/no-duplicates': 'off',
      'vue/no-export-in-script-setup': 'off',
      'unused-imports/no-unused-vars': 'off',
      'vue/custom-event-name-casing': 'off',
      'unicorn/prefer-number-properties': 'off',
      'node/prefer-global/process': 'off',
      'vue/prop-name-casing': 'off',
      'block-scoped-var': 'off',
      'eqeqeq': 'off',
      'vue/eqeqeq': 'off',
      'vue/require-explicit-emits': 'off',
      'import/no-mutable-exports': 'off',
      'jsdoc/check-param-names': 'off',
      'vars-on-top': 'off',
      'ts/no-redeclare': 'off',
      'no-var': 'off',
      'unicorn/no-new-array': 'off',
      'ts/no-this-alias': 'off',
      'ts/no-use-before-define': 'off',
      'style/max-statements-per-lin': 'off',
      'vue/no-mutating-props': 'off',
      'vue/no-use-v-if-with-v-for': 'off',
      'vue/no-v-text-v-html-on-component': 'off',
    },
  },
)
