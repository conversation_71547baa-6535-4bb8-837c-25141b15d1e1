import qs from 'fast-querystring'
import { request } from '@/utils'

/**
 * 查询粉丝列表
 * @param {appUserMyfans.Params} params
 * @return
 */
async function queryFansList(params: appUserMyfans.Params): Promise<appUserMyfans.ResponseDataListItem> {
  const queryString: string = qs.stringify(params)
  const url = `app/user/myfans?${queryString}`
  const { data = {} } = await request<appUserMyfans.ResponseDataListItem>(url, {
    method: 'GET',
  })
  return data
}

export { queryFansList }
