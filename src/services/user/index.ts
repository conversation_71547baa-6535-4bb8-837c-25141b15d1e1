import qs from 'fast-querystring'
import { request } from '@/utils'

export function fetchPerson() {
  return {}
}

/**
 * 获取已登录用户信息
 * @returns 获取已登录用户信息
 */
async function fetchUserCenter(
) {
  const url = `app/user/userInfo`
  const { data } = await request<appUserUserInfo.Response['data']>(url)
  return data
}
/**
 * 通过账号密码登录
 *
 * @param params 登录参数
 * @returns 返回登录成功后的用户信息
 */
async function loginByPwd(
  params: appUserLoginAccountPwd.Params,
): Promise<appUserLoginAccountPwd.Response> {
  const url = `app/user/login/accountPwd`
  const data = await request.post<appUserLoginAccountPwd.ResponseData>(
    url,
    params,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
    },
  )
  return data
}

/**
 * 获取手机验证码
 *
 * @param params 请求参数
 * @returns 返回手机验证码数据
 */
async function getPhoneCode(
  params: appUserLoginGetSmsCode.Params,
): Promise<appUserLoginGetSmsCode.Response> {
  const url = `/app/user/login/getSmsCode`
  const data = await request.post<appUserLoginGetSmsCode.Response>(
    url,
    params,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
    },
  )
  return data
}

/**
 * 通过短信验证码登录
 *
 * @param params 登录参数
 * @returns 返回登录成功的用户信息
 */
async function loginByCode(
  params: appUserLoginAccountSmsCode.Params,
): Promise<appUserLoginAccountSmsCode.Response> {
  const url = `/app/user/login/accountSmsCode`
  const data = await request.post<appUserLoginAccountSmsCode.ResponseData>(
    url,
    params,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
    },
  )
  return data
}

/**
 * 用户意见建议反馈
 * @param {appUserFeedback.Params} params
 * @return
 */
async function userFeedback(params: appUserFeedback.Params): Promise<appUserFeedback.Response['data']> {
  const url = `app/user/feedback`
  const data = await request<appUserFeedback.Response>(url, {
    method: 'POST',
    data: params,
  })
  return data as appUserFeedback.Response['data']
}

/**
 * 用户设置提现支付宝信息
 * @param {appUserSetAliPayAccount.Params} params
 * @return
 */
async function setAliPayAccount(params: appUserSetAliPayAccount.Params): Promise<appUserSetAliPayAccount.Response['data']> {
  const url = `app/user/setAliPayAccount`
  const { data } = await request.post<appUserSetAliPayAccount.ResponseData>(
    url,
    params,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
    },
  )
  return data as appUserSetAliPayAccount.Response['data']
}

/**
 * 设置用户信息
 * @param {appUserSetUserInfo.Params} params
 * @return
 */
async function setUserInfo(params: appUserSetUserInfo.Params): Promise<appUserSetUserInfo.Response['data']> {
  const queryString: string = qs.stringify(params)
  const url = `app/user/setUserInfo?${queryString}`
  const data = await request<appUserSetUserInfo.Response>(url, {
    method: 'POST',
  })
  return data as appUserSetUserInfo.Response['data']
}

/**
 * 注销账号
 * @return
 */
async function logoff(params: { account: string }): Promise<void> {
  const queryString: string = qs.stringify(params)
  const url = `app/user/logout?${queryString}`
  await request<appUserSetUserInfo.Response>(url, {
    method: 'POST',
  })
}

export { loginByPwd, getPhoneCode, loginByCode, fetchUserCenter, userFeedback, setAliPayAccount, setUserInfo, logoff }
