import qs from 'fast-querystring'

/**
 * 联盟CPS活动转链
 * @param {appActivityChainActivity.Params} params
 * @return
 */
async function chainActivity(params: appActivityChainActivity.Params): Promise<appActivityChainActivity.Response['data']> {
  const queryString: string = qs.stringify(params)
  const url = `app/activity/chainActivity?${queryString}`
  const { data = {} } = await request<appActivityChainActivity.Response>(url, {
    method: 'GET',
  })
  return data as appActivityChainActivity.Response['data']
}

/**
 * 抖音本地生活页转链
 * @return
 */
async function dyLocalLife(): Promise<appActivityDyLocalLife.Response['data']> {
  const url = `app/activity/dyLocalLife`
  const { data = [] } = await request<appActivityDyLocalLife.Response>(url, {
    method: 'GET',
  })
  return data as appActivityDyLocalLife.Response['data']
}

/**
 * CPS活动集合列表
 *
 * @param params 请求参数 活动集合ID appActivityQueryActivityList.Params
 * @returns 返回CPS活动集合列表数据 appActivityQueryActivityList.Response['data']
 */
async function queryActivityList(params: appActivityQueryActivityList.Params): Promise<appActivityQueryActivityList.Response['data']> {
  const queryString: string = qs.stringify(params)
  const url = `app/activity/queryActivityList?${queryString}`
  const { data = [] } = await request<appActivityQueryActivityList.Response>(url, {
    method: 'GET',
  })
  return data as appActivityQueryActivityList.Response['data']
}

/**
 * 联盟CPS活动列表
 * @param {appActivityQueryPlatformActivity.Params} params
 * @return
 */
async function queryPlatformActivity(params: appActivityQueryPlatformActivity.Params): Promise<appActivityQueryPlatformActivity.Response['data']> {
  const queryString: string = qs.stringify(params)
  const url = `app/activity/queryPlatformActivity?${queryString}`
  const { data = [] } = await request<appActivityQueryPlatformActivity.Response>(url, {
    method: 'GET',
  })
  return data as appActivityQueryPlatformActivity.Response['data']
}

export { queryActivityList, chainActivity, dyLocalLife, queryPlatformActivity }
