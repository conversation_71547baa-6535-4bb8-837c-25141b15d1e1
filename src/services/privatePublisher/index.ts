import qs from 'fast-querystring'
import { request } from '@/utils'

/**
 *  私域用户备案 (省赚客)
 * @param {appPrivatePublisherBind.Params} params
 * @return Promise<appPrivatePublisherBind.Response>
 */
async function privPubBind(
  params: appPrivatePublisherBind.Params,
): Promise<appPrivatePublisherBind.Response> {
  const queryString: string = qs.stringify(params)
  const url = `/app/privatePublisher/bind?${queryString}`
  const data = await request<appPrivatePublisherBind.Response>(url, {
    method: 'GET',
  })
  return data
}

/**
 *  查询用户备案信息
 * @return Promise<appPrivatePublisherBindInfo.Response>
 */
async function privPubBindInfo() {
  const url = `/app/privatePublisher/bindInfo`
  const { data = {} } = await request<appPrivatePublisherBindInfo.Response>(url, {
    method: 'GET',
  })
  return data
}

export { privPubBind, privPubBindInfo }
