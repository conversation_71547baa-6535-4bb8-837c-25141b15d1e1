import qs from 'fast-querystring'
import { request } from '@/utils'

/**
 * 团购cps商品列表&搜索
 * @param {appGroupBuyQueryGoodsList.Params} params
 * @return
 */
async function queryGroupGoodsList(params: appGroupBuyQueryGoodsList.Params): Promise<appGroupBuyQueryGoodsList.ResponseDataListItem> {
  const queryString: string = qs.stringify(params)
  const url = `/app/groupBuy/queryGoodsList?${queryString}`
  const { data = {} } = await request<appGroupBuyQueryGoodsList.ResponseDataListItem>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 团购CPS转链&外卖红包转链
 * @param {appGroupBuyChainGroupBuy.Params} params
 * @return
 */
async function chainGroupBuy(params: appGroupBuyChainGroupBuy.Params): Promise<appGroupBuyChainGroupBuy.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `/app/groupBuy/chainGroupBuy?${queryString}`
  const { data = {} } = await request<appGroupBuyChainGroupBuy.ResponseData>(url, {
    method: 'GET',
  })
  return data
}

export { queryGroupGoodsList, chainGroupBuy }
