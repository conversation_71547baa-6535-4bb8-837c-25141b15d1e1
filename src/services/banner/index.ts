import qs from 'fast-querystring'

interface BannerListResponse
  extends Omit<appBannerBannerList.Response, 'data'> {
  data: BannerListDataItem[]
}

interface BannerListDataItem extends appBannerBannerList.ResponseDataItem {
  picture_url: string
}

interface BannerBannerListParams {
  /** banner广告位位置 */
  position: 'home_center' | 'home_top' | 'personalCenter_down' | 'personalCenter_top' | 'home_center_right' | 'home_center_left' | 'bendi_top'
}

/**
 * 广告位信息列表
 *
 * @param params 请求参数 appBannerBannerList.Params
 * @returns 返回广告位信息列表数据 appBannerBannerList.Response['data']
 */
async function queryBannerList(
  params: BannerBannerListParams,
): Promise<BannerListResponse['data']> {
  const queryString: string = qs.stringify(params)
  const url = `app/banner/bannerList?${queryString}`
  const { data = [] } = await request<appBannerBannerList.Response>(url)
  return data as BannerListResponse['data']
}

/**
 * 获取首页/频道页金刚区列表
 *
 * @param params 请求参数 appBannerJinGangList.Params
 * @returns 返回首页/频道页金刚区列表数据 appBannerJinGangList.Response['data']
 */
async function queryJinGangList(
  params: appBannerJinGangList.Params,
): Promise<appBannerJinGangList.Response['data']> {
  const queryString: string = qs.stringify(params)
  const url = `/app/banner/jinGangList?${queryString}`
  const { data } = await request<appBannerJinGangList.Response>(url)
  return data as appBannerJinGangList.Response['data']
}

export { queryBannerList, queryJinGangList }
