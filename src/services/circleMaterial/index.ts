import qs from 'fast-querystring'
import { request } from '@/utils'

/**
 * 获取热搜词
 * @param {appCircleMaterialQueryHotKey.Params} params
 * @return
 */
async function queryHotKey(params: appCircleMaterialQueryHotKey.Params): Promise<appCircleMaterialQueryHotKey.Response> {
  const queryString: string = qs.stringify(params)
  const url = `app/circleMaterial/queryHotKey?${queryString}`
  const data = await request<appCircleMaterialQueryHotKey.Response['data']>(url)
  return data
}

/**
 * 获取发圈素材
 * @param {appCircleMaterialQueryPlatformFriendsMaterial.Params} params
 * @return
 */
async function queryFriendsMaterial(params: appCircleMaterialQueryPlatformFriendsMaterial.Params): Promise<appCircleMaterialQueryPlatformFriendsMaterial.Response['data']> {
  const queryString: string = qs.stringify(params)
  const url = `app/circleMaterial/queryPlatformFriendsMaterial?${queryString}`
  const data = await request<appCircleMaterialQueryPlatformFriendsMaterial.Response['data']>(url)
  return data
}

/**
 * 每日线报
 * @param {appCircleMaterialQueryTelegram.Params} params
 * @return
 */
async function queryTelegram(params: appCircleMaterialQueryTelegram.Params): Promise<appCircleMaterialQueryTelegram.Response['data']> {
  const queryString: string = qs.stringify(params)
  const url = `app/circleMaterial/queryPlatformFriendsMaterial?${queryString}`
  const data = await request<appCircleMaterialQueryTelegram.Response['data']>(url)
  return data
}

export { queryHotKey, queryFriendsMaterial, queryTelegram }
