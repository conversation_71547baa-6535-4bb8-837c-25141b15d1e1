import { request } from '@/utils'

/**
 *  检查新版本(仅用于安卓) (省赚客)
 * @param {appUpdateCheck.Params} params
 * @return
 */
async function checkVersion(params: appUpdateCheck.Params): Promise<appUpdateCheck.Response> {
  const url = `app/update/check`
  const { data } = await request<appUpdateCheck.Response>(url, {
    method: 'GET',
    data: params,
  })
  return data
}

/**
 *  是否隐藏团队粉丝信息
 * @param {appUpdateCheck.Params} params
 * @return
 */
async function hideTeam(params: appUpdateHideTeam.Params): Promise<appUpdateHideTeam.Response> {
  const url = `app/update/hideTeam`
  const data = await request<appUpdateHideTeam.Response>(url, {
    method: 'GET',
    data: params,
  })
  return data
}

export { checkVersion, hideTeam }
