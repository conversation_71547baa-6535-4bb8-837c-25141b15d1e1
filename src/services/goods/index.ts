import qs from 'fast-querystring'
import { request } from '@/utils'

interface GoodsListResponse extends Omit<appGoodsList.Response, 'data'> {
  data: {
    list: GoodsListDataItem[]
  }
}
interface GoodsListDataItem extends appGoodsList.ResponseDataListItem {
  list: appGoodsList.ResponseDataListItem[]
}

/**
 * 查询商品列表（榜单）
 * @param params appGoodsList.Params
 * @returns appGoodsList.Response['data']
 */
async function queryGoodsList(params: appGoodsList.Params): Promise<GoodsListResponse['data']> {
  const queryString: string = qs.stringify(params)
  const url = `app/goods/list?${queryString}`
  const { data = {} } = await request<GoodsListResponse['data']>(url, {
    method: 'GET',
  })
  const goods: GoodsListResponse['data'] = { list: [], ...data }
  return goods
}

function mockGoodsCategory(): Promise<any> {
  return delay().then(() => {
    return [
      {
        title: '精选',
        label: 'all',
      },
      {
        title: '淘宝',
        label: 'TB',
      },
      {
        title: '京东',
        label: 'JD',
      },
      {
        title: '抖音',
        label: 'DY',
      },
      {
        title: '唯品会',
        label: 'WPH',
      },
      {
        title: '拼多多',
        label: 'PDD',
      },
      // {
      //   title: '考拉',
      //   label: 'KL',
      // },
    ]
  })
}

async function queryGoodsCategory(): Promise<any> {
  return mockGoodsCategory()
}

/**
 * 文案转链
 * @param {appGoodsChainTransfer.Params} params
 * @return
 */
async function chainTransfer(params: appGoodsChainTransfer.Params): Promise<appGoodsChainTransfer.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goods/chainTransfer?${queryString}`
  const { data = {} } = await request<appGoodsChainTransfer.ResponseData>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 高佣转链(需传商品ID，平台编码)
 * @param {appGoodsChainTurning.Params} params
 * @return
 */
async function chainTurning(params: appGoodsChainTurning.Params): Promise<appGoodsChainTurning.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goods/chainTurning?${queryString}`
  const { data = {} } = await request<appGoodsChainTurning.ResponseData>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 剪切板搜索
 * @param {appGoodsClipBoardSearch.Params} params
 * @return
 */
async function clipBoardSearch(params: appGoodsClipBoardSearch.Params): Promise<appGoodsClipBoardSearch.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goods/clipBoardSearch?${queryString}`
  const { data = {} } = await request<appGoodsClipBoardSearch.ResponseData>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 商品详情（需传商品ID平台编号）
 * @param {appGoodsGoodsDetail.Params} params
 * @return
 */
async function queryGoodsDetail(params: appGoodsGoodsDetail.Params): Promise<appGoodsGoodsDetail.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goods/goodsDetail?${queryString}`
  const { data = {} } = await request<appGoodsGoodsDetail.ResponseData>(url, {
    method: 'GET',
  })
  return data
}

// 商品搜索
async function searchGoods(params: appGoodsSearch.Params): Promise<appGoodsSearch.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goods/search?${queryString}`
  const { data = {} } = await request<appGoodsSearch.ResponseData>(url, {
    method: 'GET',
  })
  return data
}

// 收藏商品信息
async function collectGoods(params: appGoodsSearch.Params): Promise<appGoodsSearch.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goodsCollect/saveCollect?${queryString}`
  const { data = {} } = await request<appGoodsSearch.ResponseData>(url, {
    method: 'GET',
  })
  return data
}
// 取消收藏的商品
async function cancelGoodsCollect(params: appGoodsSearch.Params): Promise<appGoodsSearch.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goodsCollect/cancelCollect?${queryString}`
  const { data = {} } = await request<appGoodsSearch.ResponseData>(url, {
    method: 'GET',
  })
  return data
}

// 判断是否收藏
async function enabledGoodsCollect(params: appGoodsSearch.Params): Promise<appGoodsSearch.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goodsCollect/enabledCollect?${queryString}`
  const { data = {} } = await request<appGoodsSearch.ResponseData>(url, {
    method: 'GET',
  })
  return data
}
export { queryGoodsList, queryGoodsCategory, chainTransfer, chainTurning, clipBoardSearch, queryGoodsDetail, searchGoods, collectGoods, cancelGoodsCollect, enabledGoodsCollect }
