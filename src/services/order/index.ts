import qs from 'fast-querystring'
import { request } from '@/utils'

/**
 * 查询订单列表
 * @param {appOrderOrderList.Params} params
 * @return
 */
async function queryOrderList(params: appOrderOrderList.Params): Promise<appOrderOrderList.ResponseDataListItem> {
  const queryString: string = qs.stringify(params)
  const url = `app/order/orderList?${queryString}`
  const { data = {} } = await request<appOrderOrderList.ResponseDataListItem>(url, {
    method: 'GET',
  })
  return data
}

export { queryOrderList }
