import qs from 'fast-querystring'
import { request } from '@/utils'

/**
 * 资源类型列表
 * @param {appMaterialList.Params} params
 * @return
 */
async function queryMaterialList(params: appMaterialList.Params): Promise<appMaterialList.ResponseDataItem> {
  const queryString: string = qs.stringify(params)
  const url = `app/material/list?${queryString}`
  const { data = {} } = await request<appMaterialList.ResponseDataItem>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 单个资源详情
 * @param {appMaterialQueryById.Params} params
 * @return
 */
async function appMaterialQueryById(id: string): Promise<appMaterialQueryById.ResponseData> {
  const url = `app/material/queryById?id=${id}`
  const { data } = await request<appMaterialQueryById.ResponseData>(url, {
    method: 'GET',
  })
  return data
}
export { queryMaterialList, appMaterialQueryById }
