import qs from 'fast-querystring'
import { request } from '@/utils'

/**
 * 收藏商品信息
 * @param {appGoodsCollectSaveCollect.Params} params
 * @return
 */
async function saveCollect(params: appGoodsCollectSaveCollect.Params): Promise<appGoodsCollectSaveCollect.Response> {
  const queryString: string = qs.stringify(params)
  const url = `app/goodsCollect/saveCollect?${queryString}`
  const { data = {} } = await request<appGoodsCollectSaveCollect.Response>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 取消收藏商品信息
 * @param {appGoodsCollectCancelCollect.Params} params
 * @return
 */
async function cancelCollect(params: appGoodsCollectCancelCollect.Params): Promise<appGoodsCollectCancelCollect.Response> {
  const queryString: string = qs.stringify(params)
  const url = `app/goodsCollect/cancelCollect?${queryString}`
  const { data = {} } = await request<appGoodsCollectCancelCollect.Response>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 判断商品是否收藏
 * @param {appGoodsCollectCancelCollect.Params} params
 * @return
 */
async function checkEnabledCollect(params: appGoodsCollectEnabledCollect.Params): Promise<appGoodsCollectEnabledCollect.Response> {
  const queryString: string = qs.stringify(params)
  const url = `app/goodsCollect/enabledCollect?${queryString}`
  const { data = {} } = await request<appGoodsCollectEnabledCollect.Response>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 批量删除收藏的商品
 * @param {appGoodsCollectBatchDeleteCollect.Params} params
 * @return
 */
async function batchDeleteCollect(params: appGoodsCollectBatchDeleteCollect.Params): Promise<appGoodsCollectBatchDeleteCollect.Response> {
  const queryString: string = qs.stringify(params)
  const url = `app/goodsCollect/batchDeleteCollect?${queryString}`
  const { data = {} } = await request<appGoodsCollectBatchDeleteCollect.Response>(url, {
    method: 'GET',
  })
  return data
}

/**
 * 批量查询收藏的商品
 * @param {appGoodsCollectBatchSearchCollect.Params} params
 * @return
 */
async function batchSearchCollect(params: appGoodsCollectBatchSearchCollect.Params): Promise<appGoodsCollectBatchSearchCollect.ResponseData> {
  const queryString: string = qs.stringify(params)
  const url = `app/goodsCollect/batchSearchCollect?${queryString}`
  const { data = {} } = await request<appGoodsCollectBatchSearchCollect.ResponseData>(url, {
    method: 'GET',
  })
  return data
}

export { saveCollect, cancelCollect, checkEnabledCollect, batchDeleteCollect, batchSearchCollect }
