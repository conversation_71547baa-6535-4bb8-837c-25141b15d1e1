import qs from 'fast-querystring'
import { request } from '@/utils'

/**
 * 申请提现
 * @param {appCashApply.Params} params
 * @return
 */
async function cashApply(params: appCashApply.Params): Promise<appCashApply.Response> {
  const url = `app/cash/apply`
  const data = await request.post<appCashApply.Response>(
    url,
    params,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
    },
  )
  return data
}

/**
 * 提现记录查询
 * @param {appCashAppCashData.Params} params
 * @return
 */
async function queryCashData(params: appCashAppCashData.Params): Promise<appCashAppCashData.Response> {
  const queryString: string = qs.stringify(params)
  const url = `app/cash/appCashData?${queryString}`
  const { data = {} } = await request<appCashAppCashData.Response>(url, {
    method: 'GET',
  })
  return data
}

export { cashApply, queryCashData }
