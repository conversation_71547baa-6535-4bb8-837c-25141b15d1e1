/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActivityCardActivityCard: typeof import('./components/activity-card/activity-card.vue')['default']
    ActivityListActivityList: typeof import('./components/activity-list/activity-list.vue')['default']
    AdvertViewAdvertView: typeof import('./components/advert-view/advert-view.vue')['default']
    AppFooter: typeof import('./components/AppFooter.vue')['default']
    AppLogos: typeof import('./components/AppLogos.vue')['default']
    CarouselCarousel: typeof import('./components/Carousel/Carousel.vue')['default']
    CurtainCurtain: typeof import('./components/curtain/curtain.vue')['default']
    FilterFilter: typeof import('./components/filter/filter.vue')['default']
    FilterPopupFilterPopup: typeof import('./components/filter-popup/filter-popup.vue')['default']
    GoodsCardGoodsCard: typeof import('./components/goods-card/goods-card.vue')['default']
    GoodsCardGoodsCard2: typeof import('./components/goods-card/goods-card2.vue')['default']
    GoodsinfoGoodsinfo: typeof import('./components/goodsinfo/goodsinfo.vue')['default']
    GoodsListGoodsList: typeof import('./components/goods-list/goods-list.vue')['default']
    GoodsOverlayGoodsOverlay: typeof import('./components/goods-overlay/goods-overlay.vue')['default']
    HiCounter: typeof import('./components/HiCounter.vue')['default']
    HotListGoodsHotListGoods: typeof import('./components/hot-list-goods/hot-list-goods.vue')['default']
    IframeWraperIframeWraper: typeof import('./components/iframe-wraper/iframe-wraper.vue')['default']
    IkunQrcodeIkunQrcode: typeof import('./components/ikun-qrcode/ikun-qrcode.vue')['default']
    InputEntry: typeof import('./components/InputEntry.vue')['default']
    LoginTabsLoginTabs: typeof import('./components/login-tabs/login-tabs.vue')['default']
    NavSwiperNavSwiper: typeof import('./components/nav-swiper/nav-swiper.vue')['default']
    OverlaySearchOverlaySearch: typeof import('./components/overlay-search/overlay-search.vue')['default']
    OverlaySearchWraperOverlaySearchWraper: typeof import('./components/overlay-search-wraper/overlay-search-wraper.vue')['default']
    PageWraperPageWraper: typeof import('./components/page-wraper/page-wraper.vue')['default']
    PricePrice: typeof import('./components/price/price.vue')['default']
    PrivacyPopupPrivacyPopup: typeof import('./components/privacy-popup/privacy-popup.vue')['default']
    ProtocolBoxProtocolBox: typeof import('./components/protocol-box/protocol-box.vue')['default']
    SearchOverlaySearchOverlay: typeof import('./components/search-overlay/search-overlay.vue')['default']
    SimpleModalSimpleModal: typeof import('./components/simpleModal/simpleModal.vue')['default']
    TkiQrcodeTkiQrcode: typeof import('./components/tkiQrcode/tkiQrcode.vue')['default']
    UniBackTopUniBackTop: typeof import('./components/uni-back-top/uni-back-top.vue')['default']
    UniGridItemUniGridItem: typeof import('./components/uni-grid-item/uni-grid-item.vue')['default']
    UniGridUniGrid: typeof import('./components/uni-grid/uni-grid.vue')['default']
    WmPosterWmPoster: typeof import('./components/wmPoster/wmPoster.vue')['default']
  }
}
