<script lang="ts" setup>
import { simpleModalProps } from './type'

/**
 * 自定义弹窗内容组件
 * 使用方法：
 *        1.引入组件：import simpleModal from '@/components/simple-pro/customModal.vue';
 *        2.定义：components ，simpleModal
 *        3.页面使用：<simpleModal ref="simpleModal">这里面可以写你的自定义内容哟~</simpleModal>
 *        4.指定方法 this.$refs.simpleModal.func();
 *
 * 自定义方法：
 *        1. alert() ; 支持在方法中传入 object 更改内容  该方法仅弹出一个按钮 （如果传入了object参数 其中的参数值优先级最高）
 *        2. hide();
 */
const props = defineProps(simpleModalProps)
const emit = defineEmits(['confirmButton', 'cancelButton', 'maskClose', 'contentClick'])
const showConfirmButton = ref<boolean>(true) // 确认按钮
const showCancelButton = ref<boolean>(false) // 取消按钮
const isVisibility = ref<boolean>(false) // 是否显示
const confirmButtonText = ref<string>('确定') // 确定按钮文字
const cancelButtonText = ref<string>('取消') // 取消按钮文字
function __setconfig(options: { [x: string]: any } | undefined) {
  if (options != undefined && (typeof options == 'object')) {
    if (options.showConfirmButton != undefined)
      showConfirmButton.value = options.showConfirmButton
    if (options.showCancelButton != undefined)
      showCancelButton.value = options.showCancelButton
    if (options.confirmButtonText != undefined)
      confirmButtonText.value = options.confirmButtonText
    if (options.cancelButtonText != undefined)
      cancelButtonText.value = options.cancelButtonText
    if (options.isVisibility != undefined)
      isVisibility.value = options.isVisibility
    // if (options['contentClass'] != undefined) props.contentClass = options['contentClass'];
  }
}
function show(options: any) {
  // 传入 object 参数 动态修改
  __setconfig(options)
  isVisibility.value = true
}
function hide() {
  isVisibility.value = false
}
function handleClose() {
  isVisibility.value = false
  emit('cancelButton')
}
function handleConfirm() {
  emit('confirmButton')
}
function ClickMaskClose() {
  // 点击背景 or 内容时执行关闭  有坑 点击内容的时候也会关闭，
  emit('maskClose')
}
function contentClick(e: any) {
  console.log('content click')
  emit('contentClick', e)
}
function moveHandleStop() {

}
</script>

<template>
  <view style="border-radius: 16upx">
    <view
      class="modal-box" style="padding-right: 16px;padding-left: 16px"
      :class="[isVisibility ? 'show' : '', buttom ? 'bottom-modal' : '']" @tap="ClickMaskClose"
      @touchmove.stop.prevent="moveHandleStop"
    >
      <!-- 默认弹窗 -->
      <view v-if="!buttom" class="dialog" @tap.stop="contentClick">
        <view :class="contentClass">
          <slot />
        </view>
        <view v-if="showConfirmButton == true || showCancelButton == true" class="simple-bar has-bordert">
          <view v-if="showCancelButton == true" class="action has-mg-0 flex-sub text-green" @tap.stop="handleClose">
            {{ cancelButtonText }}
          </view>
          <view v-if="showConfirmButton == true" class="action has-mg-0 flex-sub has-borderl" @tap.stop="handleConfirm">
            {{ confirmButtonText }}
          </view>
        </view>
      </view>

      <!-- 底部弹窗 -->
      <view v-if="buttom" class="dialog" @tap.stop="contentClick">
        <view v-if="showConfirmButton == true || showCancelButton == true" class="simple-bar has-bordert">
          <view v-if="showCancelButton == true" class="action is-black" @tap.stop="handleClose">
            {{ cancelButtonText }}
          </view>
          <view v-if="showConfirmButton == true" class="action is-blue" @tap.stop="handleConfirm">
            {{ confirmButtonText }}
          </view>
        </view>
        <view :class="contentClass">
          <slot />
        </view>
      </view>
    </view>
  </view>
</template>

<style></style>
