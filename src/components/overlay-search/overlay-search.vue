<script lang="ts" setup>
import { getClipboardData } from './uni-clipboard'
import { useOverlaySearchStore } from '@/store/overlay-search'
import { clipBoardSearch } from '@/services'
import { useGoodsStore } from '@/store/goods'
import { appInfo } from '@/common'
import { useAuthStore } from '@/store/user'

const { setOverlaySearch, containsOverlaySearchList } = useOverlaySearchStore()
const goodsShow = ref<boolean>(false)
const searchShow = ref<boolean>(false)
const content = ref<string>('')
const transferContent = ref<appGoodsChainTransfer.ResponseData>({})

const { setGoods, clipboardData, setClipboardStore, delClipboardStore } = useGoodsStore()

async function handleChian(keyword: string) {
  const parsecontent = await clipBoardSearch({ keyword })
  console.log('剪贴板内容搜索结果', parsecontent)
  if (parsecontent?.platformCode) {
    // await uni.hideTabBar()
    goodsShow.value = true
    transferContent.value = {
      ...parsecontent,
      keyword,
    }
    setGoods(transferContent.value)
    // 搜到券了，就清掉剪贴板
    await clearClipboard()
    console.log('[overlay-search] 已查到优惠券，清空剪贴板')
  }
  else {
    content.value = keyword
    // await uni.hideTabBar()
    searchShow.value = true
    // await clearClipboard()
    console.log('[overlay-search] 未查到优惠券，不清空剪贴板')
  }
  console.log('[overlay-search] transferContent', transferContent.value)
}

async function toggleGoods() {
  // uni.showTabBar()
  goodsShow.value = false
}

async function toggleSearch() {
  // uni.showTabBar()
  searchShow.value = false
  await clearClipboard()
  console.log('[overlay-search] 未查到优惠券，但用户选择关闭监听，清空剪贴板')
}

async function clearClipboard() {
  // 安卓app使用Native.js操作剪贴板，这样不会有弹窗提示，否则这个hideToast会有一定的延时，会一闪而过的弹窗

  // #ifdef APP-PLUS
  const context = plus.android.importClass('android.content.Context')
  const main = plus.android.runtimeMainActivity()
  const clip = main.getSystemService(context.CLIPBOARD_SERVICE)
  plus.android.invoke(clip, 'setText', '')
  // #endif

  // 如果是ios设备，需要用如下代码
  // var UIPasteboard = plus.ios.importClass('UIPasteboard')
  // var generalPasteboard = UIPasteboard.generalPasteboard()
  // generalPasteboard.setValueforPasteboardType('testValue', 'public.utf8-plain-text')

  // app之外的平台，直接用一下官方自身的api
  // #ifndef APP-PLUS
  uni.setClipboardData({
    data: '',
    success: () => uni.hideToast(),
  })
  // #endif

  // 清空store
  delClipboardStore()
}

// 需要在剪贴板中监听的域名（一些主流电商平台的域名）
const ruleLinks = [
  // 淘宝
  'taobao.com',
  'tmall.com',
  'tmall.hk',
  'etao.com',
  'TaoBao.com',
  'tb.cn',
  // 京东
  'jd.com',
  '3.cn',
  'jd.hk',
  'jingxi.com',
  // 拼多多
  'pinduoduo.com',
  'yangkeduo.com',
  // 唯品会
  'vip.com',
  // 抖音
  'douyin.com',
  // 考拉
  // 'kaola.com',
  // 苏宁
  'suning.com',
]

const { userToken } = useAuthStore()

onShow(async () => {
  // 默认token
  if (userToken.value === appInfo.defaultTk) {
    console.log('[overlay-search] 当前还未登录APP，不提供剪贴板识别能力')
    return false
  }
  console.log('[overlay-search] [上次]剪贴板内容', clipboardData.value)

  const text = await getClipboardData()
  console.log('[overlay-search] [本次]剪贴板内容', text)

  // 内容为空的话，就没啥必要走一下api了
  if (text.length <= 0) {
    console.log('[overlay-search] 剪贴板内容为空，没必要走查券')
    return false
  }

  // 内容与上次相比没啥变化，也没必要走api
  if (text === clipboardData.value && (searchShow.value || goodsShow.value)) {
    console.log('[overlay-search] 剪贴板内容没变化，并且监听窗口是打开状态')
    return false
  }

  // 如果是复制的自己APP的内容就不触发查券
  if (containsOverlaySearchList(text)) {
    console.log('[overlay-search] 复制的自己APP的内容，不触发查券')
    return false
  }

  // 去除制表符
  // text = text.replace(/\s+/gi, '')

  // 如果二次进来，剪贴板有新的值，就记录一下store，覆盖掉旧的值
  setClipboardStore(text)

  // 先弹窗关闭，后面重新打开
  searchShow.value = false
  goodsShow.value = false

  // 没啥鸟用？但好像又有点用，没看懂值是怎么传过去的。
  content.value = text

  // 匹配到了url链接
  if (matchUrl(text)) {
    console.log('[overlay-search] 剪贴板内容匹配到了url链接规则')
    await searchCoupon(text)
  }

  // 匹配到了11到14位的a-zA-Z0-9字符串（一般是口令）
  else if (/[a-z0-9]{11,14}/i.test(text)) {
    console.log('[overlay-search] 剪贴板内容匹配到了口令规则')
    await searchCoupon(text)
  }

  // 如果口令和连接都没匹配到，那就是垃圾内容
  // 也可能是商品标题的文案（20-60个字符长度，不是纯数字、不是纯英文字符）
  else if (text.length >= 20 && text.length <= 60 && !isPureNumberOrEnglish(text)) {
    console.log('[overlay-search] 剪贴板内容匹配到了商品标题规则')
    await searchCoupon(text)
  }

  // 未匹配到任何规则，不用做查券处理
  else {
    console.log('[overlay-search] 监听规则校验完毕，认定为垃圾内容')
  }
})

/**
 * 是否为纯数字或纯英文
 * @param str 字符
 */
function isPureNumberOrEnglish(str: string) {
  return /^\d+$/.test(str) || /^[a-z]+$/i.test(str)
}

/**
 * 是否匹配到了url规则
 * @param text 字符
 */
function matchUrl(text: string) {
  for (let i = 0; i < ruleLinks.length; i++) {
    if (text.includes(ruleLinks[i])) {
      return true
    }
  }
  return false
}

/**
 * 根据字符来调用api搜索优惠券
 * @param text 字符
 */
async function searchCoupon(text: string) {
  await uni.showLoading({
    title: '查券中...',
    mask: true,
  })
  setOverlaySearch(true)
  await handleChian(text)
  uni.hideLoading()
}
</script>

<template>
  <view class="overlay-search">
    <search-overlay v-if="searchShow" :content="content" @toggle-show="toggleSearch" @clear-clipboard="clearClipboard" />
    <goods-overlay v-if="goodsShow" :transfer-content="transferContent" @toggle-show="toggleGoods" @clear-clipboard="clearClipboard" />
  </view>
</template>

<route lang="json5" type="page">
{
  style: { navigationBarTitleText: 'overlay-search' },
}
</route>
