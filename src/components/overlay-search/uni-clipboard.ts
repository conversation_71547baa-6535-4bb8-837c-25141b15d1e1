import { useOverlaySearchStore } from '@/store/overlay-search'

const { addOverlaySearchList } = useOverlaySearchStore()

/**
 * 获取系统剪贴板内容
 * @returns {Promise<string>} 剪贴板内容
 */
export async function getClipboardData(): Promise<any> {
  return new Promise<any>((resolve, reject) => {
    // #ifndef H5
    uni.getClipboardData({
      success: ({ data }) => resolve(data),
      fail: error => reject(error),
    })
    // #endif

    // #ifdef H5
    if (navigator?.clipboard && navigator?.clipboard?.readText) {
      navigator.clipboard.readText().then(resolve).catch(reject)
    }
    // #endif
  })
}

/**
 * 设置系统剪贴板的内容
 * @param {string} data 需要设置的内容
 * @param {boolean} his 是否需要记录到缓存
 * @returns {Promise<string>} 设置的内容
 */
export async function setClipboardData(data: any, his?: boolean): Promise<any> {
  // console.log('复制到剪贴板的内容', data)
  return new Promise<any>(() => {
    // #ifndef H5
    uni.setClipboardData({
      data,
      success() {
        uni.showToast({
          icon: 'none',
          title: '复制成功',
        })
        if (his && his === true)
          addOverlaySearchList(data)
      },
      fail(err) {
        console.error('复制失败', err)
        uni.showToast({
          icon: 'none',
          title: '复制失败',
        })
      },
    })
    // #endif

    // #ifdef H5
    const textarea = document.createElement('textarea')
    try {
      textarea.value = data
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      // console.log(textarea)
      document.body.removeChild(textarea)
      // add history
      if (his && his === true)
        addOverlaySearchList(data)
    }
    catch (error) {
      document.body.removeChild(textarea)
    }
    // #endif
  })
}
