<!--
 * @Author: dongpenghu
 * @Date: 2024-05-07 21:21:53
 * @LastEditors: dongpenghu
 * @LastEditTime: 2024-05-08 00:58:07
 * @Description: file content
 * @FilePath: \juwatech\src\components\goodsinfo\goodsinfo.vue
-->
<script lang="ts" setup>
import { goodsinfoProps } from './type'
import { appInfo } from '@/common'
import { useGoodsStore } from '@/store/goods'
import { cancelGoodsCollect, chainTurning, collectGoods, enabledGoodsCollect, queryGoodsList } from '@/services'
import { openOthers } from '@/common/jump'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

const { goods, clip } = defineProps(goodsinfoProps)
console.log('clip', clip, 'store goods', goods)

const { setGoods } = useGoodsStore()
const likeGoods = ref<Array<any>>([])

function copyTitle() {
  setClipboardData(goods.title)
}

const collectValue = ref<number>(0)
const collectBtnTxt = ref('加入收藏')

function goHome() {
  uni.switchTab({
    url: '/pages/home/<USER>',
  })
}

async function buyGoods() {
  show.value = true
  try {
    const data = await chainTurning({
      goodsId: goods.id,
      platformCode: goods.platformCode,
    })
    show.value = false
    await openOthers(data, goods.platformCode)
  }
  catch (error) {
    show.value = false
    console.log('buyGoods error', error)
  }
}

/* 收藏 */
async function collectChange() {
  if (collectValue.value == 1) {
    collectValue.value = 0
    const cancel = await cancelGoodsCollect({
      goodsId: goods.id,
      platformCode: goods.platformCode,
    })
    console.log(cancel)
    await uni.showToast({
      title: '取消收藏成功',
      icon: 'none',
      duration: 2000,
    })
    if (clip == 1 && goods.platformCode == 'PDD') {
      collectBtnTxt.value = '收藏1小时后购买'
    }
    else {
      collectBtnTxt.value = '加入收藏'
    }
  }
  else if (collectValue.value == 0) { /* 未收藏过 */
    collectValue.value = 1
    const collect = await collectGoods({
      goodsId: goods.id,
      platformCode: goods.platformCode,
    })
    if (collect) {
      await uni.showToast({
        title: collect,
        icon: 'none',
        duration: 2000,
      })
      collectBtnTxt.value = '已收藏'
    }
  }
}

const loadingShow = ref<boolean>(false)
const show = ref<boolean>(false)

/* 分享 */
async function shareGood() {
  loadingShow.value = true

  console.log('shareGood chainTurning', goods)
  const shareLink = await chainTurning({
    goodsId: goods.id,
    platformCode: goods.platformCode,
  })
  loadingShow.value = false

  if (!shareLink)
    return false

  if (shareLink.platformCode === 'TB' && shareLink.needAuth) {
    await openOthers(shareLink, shareLink.platformCode)
    return false
  }

  let templateText = `${goods?.title}
【在售价】${goods?.orgPrice}元
【到手价】${goods?.actualPrice}元
-----------------
`

  if (goods.platformCode === 'TB') {
    templateText += `${shareLink?.goodsUrl}\r\n${shareLink?.shortClickUrl}`
  }
  else if (goods.platformCode === 'DY') {
    templateText += `${shareLink?.goodsUrl}`
  }
  else {
    templateText += `${shareLink?.shortClickUrl}`
  }

  await setClipboardData(templateText, true)
}

/* 是否收藏过改商品 */
async function isActive() {
  const enabledCollect = await enabledGoodsCollect({
    goodsId: goods.id,
    platformCode: goods.platformCode,
  })
  // console.log(enabledCollect)
  if (enabledCollect) {
    collectValue.value = 1
    collectBtnTxt.value = '已收藏'
  }
}

/**
 * 格式化日期字符串
 *
 * @param dateString 待格式化的日期字符串
 * @returns 返回格式化后的日期字符串，格式为 "yyyy-MM-dd"，若 dateString 为空，则返回空字符串
 */
function formatDate(dateString: string) {
  if (!dateString)
    return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

/* 类似商品 */
async function lickGoodsList() {
  try {
    const { list = [] } = await queryGoodsList({
      pageNo: 1,
      // limit: 10,
      platformCode: goods.platformCode,
      queryValue: goods.id,
      queryType: 'recommend',
    })
    if (list)
      likeGoods.value = list
  }
  catch (error) {
    likeGoods.value = []
  }
}

function clickGoods(data) {
  const goodsDetails = likeGoods.value[data.index]
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

onMounted(() => {
  if (clip == 1 && goods.platformCode == 'PDD') {
    collectBtnTxt.value = '收藏1小时后购买'
  }
  isActive()
  lickGoodsList()
})
</script>

<template>
  <view class="main-cont bg-gray-200">
    <!-- 复制淘口令是实时接口，点击分享按钮时就会有点卡顿 -->
    <wd-overlay :show="loadingShow" :z-index="102">
      <view class="h-full flex items-center justify-center">
        <wd-loading />
      </view>
    </wd-overlay>
    <!-- 首图轮播 -->
    <view class="swiper w-screen">
      <wd-swiper
        autoplay height="375px" :indicator="{ type: 'fraction' }" indicator-position="bottom-right" image-mode="scaleToFill"
        :list="goods.imageUrls"
      />
    </view>
    <!-- <view class="flex items-center bg-gray-900 text-base text-yellow-300 font-bold">
      <image src="@/static/detail/2x.png" class="mr-4 block h-9 w-10" />
      <view class="w-3/4 text-yellow-300 leading-15">
        成为会员最高可返现金￥{{ goods.maxProfit }}元
      </view>
      <view class="right-2 z-10 mr-2 w-1/4 w-20 px-1 py-1 text-center text-xl text-xs text-yellow-300">
        立即加入
        <text
          class="cuIcon cuIcon-right"
          style="background-color: #FFE9C6;width: 40upx;height: 40upx;border-radius: 50%;color:#000000;margin-left: 6upx;"
        />
      </view>
    </view> -->
    <view class="card-text-info">
      <view class="card-padding">
        <wd-card class="no-lr-margin">
          <view class="coupon-price s-row pt-1rem">
            <view class="price" style="width: 100%; display: flex; justify-content:space-between; line-height: 2.15rem">
              <view style="vertical-align: bottom;; align-items: flex-end;">
                <span style="font-size: 26upx; color:#ff1c1c;">￥</span>
                <text style="font-size: 40upx;font-weight:700; color:#ff1c1c">
                  {{ goods.actualPrice }}
                </text>
                <text style="font-size: 24upx;font-weight:600;color:#ff1c1c">
                  券后价
                </text>
                <text style="font-size: 24upx;color: grey;text-decoration:line-through;margin-left:0.5rem">
                  ￥{{ goods.orgPrice }}
                </text>
                <text class="ml-0.5rem h-1rem rounded-[6rpx] bg-[#ff4d4f] px-[6rpx] py-[4rpx] text-[22rpx] text-white">
                  约返{{ goods.maxProfit }}
                </text>
              </view>
              <view class="flex flex-col justify-center" style="vertical-align: top;align-items: flex-start;">
                <view
                  style="width: 145upx;height:45upx; text-align: center;line-height: 45upx;border-radius:16px; border: 1px solid #ff4d4f; color: #ff4d4f ;cursor: pointer;"
                  @click="shareGood"
                >
                  <text style="cursor: pointer;">
                    复制口令
                  </text>
                </view>
              </view>
            </view>

            <view style="padding: 16upx 0;display: flex;">
              <view>
                <wd-img v-if="goods.platformCode == 'TB' && goods.goodsType == 0" src="/static/platform/TB.png" class="mt-0.1rem h-1.1rem w-1.1rem align-top" />
                <wd-img v-if="goods.platformCode == 'TB' && goods.goodsType == 1" src="/static/platform/TM.png" class="mt-0.1rem h-1.1rem w-1.1rem align-top" />
                <wd-img v-if="goods.platformCode != 'TB'" :src="`/static/platform/${goods.platformCode}.png`" class="mt-0.1rem h-1.1rem w-1.1rem align-top" />
                <text style="font-weight: bold;font-size: 30upx;">
                  <text class="bg-white text-black" @longpress="copyTitle">
                    {{ goods.title }}
                  </text>
                </text>
              </view>
            </view>

            <view
              v-if="goods.couponAmount > 0"
              style="background: url('../../static/img/get_coupon_bg.png') no-repeat; background-size: 100%;"
              @click="buyGoods"
            >
              <div class="p-5">
                <p class="mb-1 text-left text-sm text-rose-500 font-semibold">
                  {{ appInfo.appName || '省赚客' }}专属{{ goods.couponAmount }}元优惠券
                </p>
                <p v-if="goods.couponStartTime && goods.couponEndTime" class="text-left text-0.65rem text-gray-600">
                  使用时间：{{ formatDate(goods.couponStartTime) }} 至 {{ formatDate(goods.couponEndTime) }}
                </p>
              </div>
            </view>

            <view class="pb-1rem color-gray">
              <text>
                注意：
              </text>
              <text v-if="goods.platformCode == 'WPH'">
                使用品类券，津贴和唯享卡可能会影响返利
              </text>
              <text v-if="goods.platformCode == 'TB' || goods.platformCode == 'JD' || goods.platformCode == 'DY' || goods.platformCode == 'KL'">
                部分红包和购物券影响返利
              </text>
              <text v-if="goods.platformCode == 'PDD'">
                根据拼多多官方规则，部分商品可能跟踪不了订单所以无返利。包含但不限于以下情况：百亿补贴三人团、商品购买了拼多多广告流量、命中比价、使用红包购物券等
              </text>
            </view>
          </view>
        </wd-card>
        <wd-card v-if="goods.description && goods.description.length >= 50" class="no-lr-margin" style="display:flex; padding: 20px 10px">
          <view style="display:flex;">
            <view
              style="background: url('../../static/tjly.jpg') no-repeat; background-size: 80%;width: 50px; height: 58px;"
            />
            <view style="width: 68%; margin: 0px 10px">
              {{ goods.description }}
            </view>
            <view
              style="width: 80upx;height:38upx; text-align: center;line-height: 38upx;border-radius: 16px;
            border: 1px solid #ff4d4f; color: #ff4d4f; margin-top: 10px"
              @click="setClipboardData(goods.description)"
            >
              <!-- <text @click="showModal()"> -->
              <!-- <text class="cuIcon cuIcon-share" /> -->
              <text>
                复制
              </text>
              <!-- </text> -->
            </view>
          </view>
        </wd-card>
        <wd-card class="no-lr-margin">
          <view class="content">
            <text class="w-full text-align-left text-black font-bold">
              价格说明
            </text>
            <text class="mt-0.5rem block rounded-lg bg-gray-100 p-0.5rem text-0.8rem line-height-1.5rem">
              <text>
                <text class="text-black font-bold">
                  划线价格：
                </text>指商品的专柜价、吊牌价、正品零售价、厂商指导价或该商品的曾经展示过的销售价等，<text class="text-black font-bold">
                  并非原价
                </text>，仅供参考。
              </text>
              <text class="mt-0.5rem block">
                <text class="text-black font-bold">
                  未划线价格：
                </text>指商品的<text class="text-black font-bold">
                  实时标价
                </text>，不因表述的差异改变性质。具体成交价格根据商品参加活动，或会员使用优惠券、积分等发生变化最终以订单结算页价格为准。
              </text>
              <text class="mt-0.5rem block text-0.7rem">
                此说明仅当出现价格比较时有效，具体请参见商品价格发布规范。若商家单独对划线价格进行说明的，以商家的表述为准。
              </text>
            </text>
          </view>
          <!-- <view v-if="isShowList"> -->
          <!--  <wd-img -->
          <!--    v-for="(item, index) in goods.imageUrls" :key="index" :src="item" width="100%" height="30vh" -->
          <!--    :enable-preview="true" -->
          <!--  /> -->
          <!-- </view> -->
        </wd-card>
      </view>
      <view class="goodsLike">
        <view class="roundbox" />
        <text class="liketext">
          类似商品
        </text>
        <view class="roundbox" />
      </view>
      <goods-list :goods="likeGoods" class="list-card" @click="clickGoods" />
    </view>
  </view>
  <view v-if="clip == 1 && goods.platformCode == 'PDD'" class="pdd-tips">
    <view class="tip-cont">
      <view class="mb-0.25rem text-0.85rem">
        建议先收藏，<text class="tip-col">
          1小时后
        </text>再购买，可返利约<text class="tip-col">
          {{ goods.maxProfit }}
        </text>元
      </view>
      <view class="text-0.7rem">
        根据官方政策，在拼多多查看商品后1小时内购买，暂无返利。
      </view>
    </view>
  </view>
  <view class="footer">
    <view class="leftbox">
      <view class="iconbox">
        <view class="grad" @click="goHome">
          <wd-icon name="home" size="1.2rem" />
          <text style="font-size: 0.75rem">
            首页
          </text>
        </view>
        <view class="grad" @click="shareGood">
          <wd-icon name="share" size="1.2rem" />
          <text style="font-size: 0.75rem">
            分享
          </text>
        </view>
      </view>
      <view class="btnbox h-[3rem] flex flex-1 px-1rem text-0.85rem">
        <view
          class="w-40% flex flex-col items-center justify-center rounded-l-[0.5rem] px-[0.5rem]"
          style="background: #f09736; color: #fff; "
          @click="collectChange"
        >
          <text :class="{ 'text-0.75rem': clip == 1 && goods.platformCode == 'PDD' }">
            {{ collectBtnTxt }}
          </text>
          <text v-if="clip == 1 && goods.platformCode == 'PDD'" class="text-0.5rem">
            下单约返￥{{ goods.maxProfit }}
          </text>
        </view>
        <view
          class="w-40% flex flex-col items-center justify-center rounded-r-[0.5rem] px-[1rem]"
          style="background: #fd111b; color: #fff;"
          @click="buyGoods"
        >
          立即购买
          <view class="text-0.5rem">
            下单约返￥{{ (clip == 1 && goods.platformCode == 'PDD') ? 0 : goods.maxProfit }}
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 跳转电商app过渡动画，暂时先拿掉 -->
  <!-- <view class="jump-overlay">
    <wd-overlay :show="show" :z-index="102">
      <view class="wrapper">
        <view class="wrapper-cont bg-#fff">
          <view class="flex items-center justify-around">
            <wd-img src="/static/logo.png" width="4rem" height="4rem" radius="0.5rem" />
            <wd-img src="/static/arrow.png" width="3rem" height="1rem" ml-0.5rem mr-0.5rem radius="0.5rem" />
            <wd-img :src="`${platforms[goods.platformCode].icon}`" width="5rem" height="5rem" radius="0.5rem" />
          </view>
          <view class="mt-1rem">
            正在跳转到{{ platforms[goods.platformCode].name }}
          </view>
          <view class="cip mt-1rem">
            可节约{{ goods.maxProfit }}元
          </view>
        </view>
      </view>
    </wd-overlay>
  </view> -->
</template>

<style lang="less" scoped>
.main-cont {
  .wd-overlay {
    background: transparent;
  }
}

.jump-overlay {
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    .wrapper-cont {
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 0.5rem;
      padding: 2rem;
      .cip {
        background: #fee7ef;
        color: #dc393b;
        padding: 0.8rem 2rem;
        border-radius: 2rem;
      }
    }
  }
}

.mt-3 {
  ::v-deep .wd-card {
    margin: 10px !important;
  }
}

.bg-gray-200 {
  overflow-x: hidden;

  .card-text-info {
    padding: 0.5rem;
  }

  ::v-deep .wd-swiper__track {
    border-radius: 0;
  }
}

.card-padding {
  .no-lr-margin {
    margin: 0 0 0.5rem;

    .content {
      padding: 1rem 0;
    }
  }

  ::v-deep .wd-card__title-content {
    display: none !important;
  }
}

.pdd-tips {
  position: fixed;
  left: 0;
  bottom: 4rem;
  width: 100%;
  .tip-cont {
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    margin: 0 0.5rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    .tip-col {
      color: #ff4d4f;
    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rem;
  background: #fff;

  .leftbox {
    text-align: center;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .iconbox {
      display: flex;
      justify-content: space-around;
      width: 30%;
      height: 4rem;

      .grad {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
    }
  }
}

::v-deep .wd-icon-star-on {
  font-size: 1rem !important;
  padding-top: 0.1rem;
}

.icon-goodsdp {
  vertical-align: middle;
}

.goodslist {

  .goodinfolist {
    display: flex;
    justify-content: space-between;
    line-height: 40px;
  }

}

::v-deep .wd-card__footer {
  display: none !important;
}

.goodsLike {
  margin: 0 auto;
  width: 30%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;

  .roundbox {
    width: 10upx;
    height: 10upx;
    border-radius: 50%;
    background: red;
  }

  .liketext {
    text-align: center;
    margin: 10px;
    font-size: 0.9rem;
  }
}

.list-card {
  margin-bottom: 3.5rem;
}

.goods-list-wrap {
  background: none !important;
}

::v-deep {
  uni-image {
    align-items: center !important;
    display: flex !important;
  }
}
</style>
