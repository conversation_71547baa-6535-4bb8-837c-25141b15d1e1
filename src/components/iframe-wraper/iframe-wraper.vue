<script lang="ts">
/* eslint-disable */
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared",
  },
};
</script>

<script lang="ts" setup>
import { PageWraperProps } from "./type"
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

const props = defineProps(PageWraperProps)

function handleHome() {
  if (props.navigateBack || props.title) uni.navigateBack();
  else
    uni.switchTab({
      url: "/pages/home/<USER>",
    });
}

function copyLink() {
  // const fullUrl = `${appInfo.baseUrl}/#${page.value.$page.fullPath}`
  setClipboardData(props.url)
}

onMounted(()=>{
  // #ifdef APP-PLUS
  let systemInfo = uni.getSystemInfoSync()
  let wv = plus.webview.create("", "custom-webview", {
    plusrequire: "none",
    'uni-app': 'none',
    top: systemInfo.statusBarHeight + 52,
    height: systemInfo.windowHeight - systemInfo.statusBarHeight
  })
  wv.addEventListener('loading', function(event) {
    plus.nativeUI.showWaiting("加载中...")
  })
  wv.addEventListener('loaded', function(event) {
    plus.nativeUI.closeWaiting()
  })
  wv.loadURL(decodeURIComponent(props.url))
  let currentWebview = getCurrentPages().pop().$getAppWebview()
  currentWebview.append(wv)
  // #endif
})

</script>

<template>
  <view class="iframe-wraper">
    <view class="nav-bar">
      <wd-navbar :title="title" :bordered="false" left-arrow @click-left="handleHome" @click-right="copyLink" />
    </view>
    // #ifdef H5
    <view class="wview h-95vh w-full">
      <web-view
          :src="decodeURIComponent(url)"
          scrolling="auto"
          frameborder="no"
      />
    </view>
    // #endif
  </view>
</template>

<style lang="scss" scoped>
.iframe-wraper {
  padding-top: calc(var(--status-bar-height) + 0.5rem);
  .nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    background: #fff;
    padding-top: calc(var(--status-bar-height) + 0.5rem);
  }
}
/* 隐藏默认滚动条 */
::-webkit-scrollbar {
  display: none;
}
</style>
