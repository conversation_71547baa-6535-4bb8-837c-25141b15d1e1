<script lang="ts">
/* eslint-disable */
export default {
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared",
  },
};
</script>

<script lang="ts" setup>
import { PageWraperProps } from "./type";

const props = defineProps(PageWraperProps);
const router = useRouter();

const themeVars = reactive({
  colorTheme: "#fa4126",
});

function handleChange({ value }) {
  router.pushTab({ name: value });
}
</script>

<template>
  <wd-config-provider :theme-vars="themeVars" :class="'page-warp ' + props.warpClass">
    <wd-notify />
    <wd-toast v-if="props.useToast" />
    <overlay-search v-if="props.useOverlaySearch" />
    <wd-message-box v-if="props.useMessageBox" />
    <privacy-popup />
    <slot />
  </wd-config-provider>
</template>

<style lang="scss" scoped>

</style>
