<script lang="ts">
/* eslint-disable */
export default {
  name: 'goods-card',
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { goodsCardProps } from './type'
import { appInfo } from '@/common';

const props = defineProps(goodsCardProps)

const emit = defineEmits(['click', 'thumb', 'add-cart', 'ob'])

const independentID = ref<string>('')
const goods = ref<any>({ id: '' })
const isValidityLinePrice = ref<boolean>(false)

watch(
  () => props.id,
  (newValue) => {
    genIndependentID(newValue)
    if (props.thresholds?.length)
      createIntersectionObserverHandle()
  },
  { deep: true, immediate: true },
)

watch(
  () => props.data,
  (newValue) => {
    if (!newValue)
      return

    if (newValue.originPrice && newValue.price && newValue.originPrice < newValue.price)
      isValidityLinePrice.value = false
    else
      isValidityLinePrice.value = true

    goods.value = newValue
  },
  { deep: true, immediate: true },
)

onMounted(() => {
  init()
})

onBeforeUnmount(() => {
  clear()
})

let intersectionObserverContext: UniApp.IntersectionObserver | null = null

function clickHandle() {
  emit('click', { goods: goods.value })
}

function clickThumbHandle() {
  emit('thumb', { goods: goods.value })
}

function addCartHandle(e) {
  const { id } = e.currentTarget
  const { id: cardID } = e.currentTarget.dataset
  emit('add-cart', {
    ...e.detail,
    id,
    cardID,
    goods: goods.value,
  })
}

function genIndependentID(id) {
  if (id)
    independentID.value = id
  else
    independentID.value = `goods-card-${~~(Math.random() * 10 ** 8)}`
}

function init() {
  const { thresholds, id } = props
  genIndependentID(id)
  if (thresholds && thresholds.length)
    createIntersectionObserverHandle()
}

function clear() {
  clearIntersectionObserverHandle()
}

function createIntersectionObserverHandle() {
  if (intersectionObserverContext || !independentID.value)
    return

  intersectionObserverContext = uni
    .createIntersectionObserver({
      thresholds: props.thresholds,
    })
    .relativeToViewport()

  intersectionObserverContext.observe(`#${independentID.value}`, (res) => {
    intersectionObserverCB()
  })
}

function intersectionObserverCB() {
  emit('ob', {
    goods: goods.value,
    context: intersectionObserverContext,
  })
}

function clearIntersectionObserverHandle() {
  if (intersectionObserverContext) {
    try {
      intersectionObserverContext.disconnect()
    }
    catch (e) {
      console.log(e)
    }
    intersectionObserverContext = null
  }
}
</script>

<template>
  <view :id="independentID" class="goods-card" @click="clickHandle">
    <view class="goods-card__main">
      <view class="goods-card__thumb" @click="clickThumbHandle">
        <wd-img v-if="!!goods.picUrl" custom-class="goods-card__img" :src="goods.picUrl" mode="aspectFill" lazy-load />
      </view>
      <view class="goods-card__body">
        <view class="goods-card__upper">
          <view v-if="goods.subTitle || goods.platformCode" class="text-ellipsis whitespace-pre-line">
            <view v-if="goods.platformCode" class="goods-card__tags">
              <view class="goods-card__tag">
                <!--淘宝平台需要细化一下：天猫、淘宝-->
                <wd-img v-if="goods.platformCode == 'TB' && goods.goodsType == 0" :src="'/static/platform/TB.png'"
                  class="imgicon" round width="1rem" height="1rem" />
                <wd-img v-if="goods.platformCode == 'TB' && goods.goodsType == 1" :src="'/static/platform/TM.png'"
                  class="imgicon" round width="1rem" height="1rem" />
                <wd-img v-if="goods.platformCode != 'TB'" :src="'/static/platform/' + goods.platformCode + '.png'"
                  class="imgicon"  width="1rem" height="1rem" />
                {{ goods.title || goods.subTitle || goods.shopTitle }}
              </view>
            </view>
          </view>
        </view>
        <view class="goods-shop flex items-center mt-0.5rem">
          <wd-img :src="`/static/img/dp.png`" class="mr-0.15rem h-0.95rem w-0.95rem" />
          <text class="text-gray-400 overflow-hidden whitespace-nowrap text-ellipsis">
            {{ goods.shopTitle }}
          </text>
        </view>
        <!-- <view class="mb-2 mt-4" v-if="goods.platformCode !== 'WPH'" >  为啥要过滤唯品会？-->
        <view class="flex mt-0.5rem">
          <view v-if="goods.couponAmount > 0" class="border-rd-2px bg-#fd305f text-#fff mr-0.5rem"
            style="padding:0.2rem 0.2rem 0.1rem 0.2rem">
            劵 <text class="pl-0.2rem" style="border-left:1px dashed #fff">{{ goods.couponAmount ?? 0 }}</text>
          </view>
          <view class="border-rd-2px text-#fa4126" style="border:1px solid #fa4126;padding:0.1rem 0.2rem">
            约返<text>{{ goods.maxProfit }}</text>
          </view>
        </view>
        <view class="pricebottom mt-0.5rem flex items-center">
          <text>
            券后<text class="color-#ff1c1c">￥</text><text class="actualprice">{{ goods.actualPrice }}</text>
          </text>
          <text class="oldprice">￥{{ goods.orgPrice }}</text>
        </view>
        <view class="monthitem mt-0.5rem" v-if="goods.salesCount">
          月销{{ goods.salesCount ?? 1000 }}
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.goods-card {
  box-sizing: border-box;
  font-size: 24rpx;
  border-radius: 0 0 16rpx 16rpx;
  border-bottom: none;
  margin-right: 20rpx;
}

.goods-card:nth-child(even) {
  margin-right: 0;
}

.goods-card__main {
  position: relative;
  display: flex;
  line-height: 1;
  padding: 0;
  background: transparent;
  border-radius: 0 0 16rpx 16rpx;
  margin-bottom: 16rpx;
  flex-direction: column;
}

.goods-card__thumb {
  flex-shrink: 0;
  position: relative;
  width: 349rpx;
  height: 349rpx;
}

.goods-card__thumb:empty {
  display: none;
  margin: 0;
}

.goods-card__img {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 0.5rem 0.5rem 0 0;
  overflow: hidden;
  background: #fff;
}

.goods-card__body {
  display: flex;
  background: #fff;
  border-radius: 0 0 0.5rem 0.5rem;
  padding: 16rpx;
  flex-direction: column;
  justify-content: space-between;
  width: calc(349rpx - 32rpx);
}

.goods-card__upper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1 1 auto;

}

.dpitem {
  display: flex;

  .shoptitle {
    padding-left: 0.2rem;
    padding-top: 0.1rem;
    color: #b1abab;
    font-size: 0.75rem;
  }
}

.goods-card__title {
  flex-shrink: 0;
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  display: -webkit-box;
  height: 72rpx;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-word;
  line-height: 36rpx;
}

.goods-card__tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 8rpx 0 0 0;
}

.goods-card__tag {
  line-height: 30rpx;
  // display: block;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;

  /* 限制显示两行 */
  //white-space: nowrap;
  //word-break: keep-all;
  // text-overflow: ellipsis;
  .imgicon {
    width: 0.8rem;
    height: 0.8rem;
    vertical-align: middle;
  }
}

.goods-card__down {
  display: flex;
  position: relative;
  flex-direction: row;
  justify-content: flex-start;
  align-items: baseline;
  line-height: 32rpx;
  margin: 8rpx 0 0 0;

  :deep(.spec-for-price) {
    font-size: 36rpx;
    white-space: nowrap;
    font-weight: 700;
    order: 1;
    color: #fa4126;
    margin: 0;
  }

  :deep(.goods-card__origin-price) {
    white-space: nowrap;
    font-weight: 700;
    order: 2;
    color: #bbbbbb;
    font-size: 24rpx;
    margin: 0 0 0 8rpx;
  }

  :deep(.goods-card__add-cart) {
    order: 3;
    margin: auto 0 0 auto;
    position: absolute;
    bottom: 0;
    right: 0;
  }
}

.spec-for-symbol {
  font-size: 24rpx;
}

.actualprice {
  color: #ff1c1c;
  font-weight: bold;
  font-size: 1.0625rem;
}

.pricebottom {
  display: flex;
  justify-content: space-between;
}

.oldprice {
  font-size: 0.7rem;
  color: #9f9a9a;
  text-decoration: line-through;
}

.monthitem {
  color: #9f9a9a;
}
</style>
