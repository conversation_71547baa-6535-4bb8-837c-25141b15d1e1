<script setup lang="ts">
import { goodsCardProps } from './type'

const props = defineProps(goodsCardProps)
const emit = defineEmits(['itemClick'])

const goods = ref<any>({})

onMounted(() => {
  goods.value = props.data
})

function goodsDetail() {
  emit('itemClick')
}
</script>

<template>
  <view class="goods-card w-full rounded-lg" @click="goodsDetail">
    <view flex-0.4>
      <wd-img :src="goods.picUrl" width="7rem" height="7rem" mr-0.5rem radius="0.5rem" />
    </view>
    <view w-full flex flex-col justify-around>
      <view class="title line-clamp-2 text-ellipsis">
        <wd-img v-if="goods.platformCode == 'TB' && goods.goodsType == 0" src="/static/platform/TB.png" class="img-icon h-1rem w-1rem" />
        <wd-img v-if="goods.platformCode == 'TB' && goods.goodsType == 1" src="/static/platform/TM.png" class="img-icon h-1rem w-1rem" />
        <wd-img v-if="goods.platformCode != 'TB'" :src="`/static/platform/${goods.platformCode}.png`" class="img-icon h1rem w-1rem" />
        {{ goods.title || goods.description }}
      </view>
      <view class="pricebottom text-0.6rem">
        <text v-if="goods.couponAmount > 0" class="mr-0.25rem bg-#fa4126 text-red-500" style="color:#fff;border-radius:0.2rem;padding:0.1rem 0.3rem">
          券 <text class="pl-0.2rem" style="border-left:1px dashed #fff">
            {{ goods.couponAmount }}
          </text>
        </text>
        <text class="border-rd-4px text-red-500" style="border:1px solid #fa4126;border-radius:0.1rem;padding:0.04rem 0.25rem 0.04rem 0.25rem">
          约返{{ goods.maxProfit }}
        </text>
      </view>
      <view class="pricebottom">
        <text class="mr-0.5rem text-red-500">
          ￥<text class="actualprice">
            {{ goods.actualPrice }}
          </text>
        </text>
        <text class="oldprice mr-0.5rem text-gray-400 line-through">
          原价￥{{ goods.orgPrice }}
        </text>
      </view>
      <view class="bot flex items-center justify-between text-0.65rem">
        <view flex items-center>
          <wd-img src="/static/img/dp.png" class="mr-0.15rem h-0.95rem w-0.95rem" />
          <text text-gray-400>
            {{ goods.shopTitle }}
          </text>
        </view>
        <text v-if="goods.salesCount" class="monthitem text-gray-400">
          {{ goods.salesCount ?? 1000 }}人付款
        </text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
  .goods-card {
    display: flex;
    background: #fff;
    color: #464646;
    padding: 0.5rem;
    font-size: 0.75rem;

    .img-icon {
      vertical-align: middle;
    }

    .pricebottom {
      display: flex;
      align-items: center;

      .oldprice {
        font-size: 0.7rem;
        text-decoration: line-through;
      }
      .actualprice {
        color: #ff1c1c;
        font-weight: bold;
        font-size: 1.0625rem;
      }
    }
  }
</style>
