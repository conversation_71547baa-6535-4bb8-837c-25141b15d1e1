<script lang="ts">
/* eslint-disable */
export default {
  name: 'nav-swiper',
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>
<script lang="ts" setup>
import { navSwiperProps } from './type'

const props = defineProps(navSwiperProps)
const emit = defineEmits(['click', 'itemClick'])

function itemClick(item: any) {
  emit('itemClick', item)
}
let pageDataList: any = computed(() => {
  const cate_count_per_page = Number(props.rowCount) * Number(props.columnCount);
  const page_count = props.list.length / cate_count_per_page;
  const page_data_list = [];

  for (let i = 0; i < page_count; ++i) {
    page_data_list[i] = props.list.slice(i * cate_count_per_page, (i + 1) * cate_count_per_page);
  }

  return page_data_list;
})
</script>
<template>
  <swiper class="cate-nav-swiper" :duration="300">
    <swiper-item v-for="(page_data, page_data_index) of pageDataList" :key="page_data_index">
      <uni-grid :column="columnCount" :highlight="false" :square="square" :showBorder="false">
        <uni-grid-item v-for="(cate_nav, cate_nav_index) of page_data" :key="cate_nav_index">
          <view class="flex flex-col items-center contents -mt-1 relative" hover-class="none" @click="itemClick(cate_nav)">
            <image class="w-2.5rem h-2.5rem mt-1rem md:w-8rem md:h-8rem" :src="cate_nav.picture_url" mode="aspectFit"></image>
            <text class="color-#999 text-xs mt-0.325rem  md:pt-0.3rem">{{ cate_nav.jingangname }}</text>
            <view class="absolute top-2 right-0" v-if="cate_nav.tagurl">
              <wd-tag type="danger" mark>{{ cate_nav.tagurl }}</wd-tag>
            </view>
          </view>
        </uni-grid-item>
      </uni-grid>
    </swiper-item>
  </swiper>
</template>

<style lang="scss" scoped>
/* 移动端 */
@media screen and (max-width: 48rem) {
  .cate-nav-swiper {
    height: 3.5rem * 4.2;
  }
}

/* PC端 */
@media screen and (min-width: 48rem) {
  .cate-nav-swiper {
    height: 6.625rem * 4.2;
  }
}
:deep(.uni-swiper-dot-active){
  background-color: #ff0000;
}
:deep(.uni-swiper-dot){
  width: 0.875rem;
  height: 0.4375rem;
  border-radius: 0.625rem;
}
:deep(.uni-swiper-dots-horizontal) {
  bottom: 8px;
}
</style>
