<script lang="ts" setup>
import { protocolBoxProps } from './type'

const props = defineProps(protocolBoxProps)
const emit = defineEmits(['click', 'protocolClick'])

// 是否同意勾选
function agreeClick() {
  emit('click')
}
function protocolClick(tag: any) {
  emit('protocolClick', tag)
}
</script>

<script lang="ts">
/* eslint-disable */
export default {
  name: 'protocol-box',
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<template>
  <view class="protocol_box">
    <view class="select" :class="{ active: agree }" @click="agreeClick"></view>
    我已同意
    <text v-if="protocolArr.length > 0" @click="protocolClick(0)">{{ protocolArr[0] }}</text>
    &ensp;<span v-if="protocolArr.length > 1">{{ " 和 " }}</span>&ensp;
    <text v-if="protocolArr.length > 1" @click="protocolClick(1)">{{ protocolArr[1] }}</text>
  </view>
</template>

<style lang="scss" scoped>
//主题色 red
$themeColor: #ff4d4f;

.protocol_box {
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 26rpx;
  color: #666;
  padding: 0 20rpx;

  .select {
    width: 30rpx;
    height: 30rpx;
    background-image: url("./ic_nor.png");
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% auto;
    margin-right: 12rpx;
    transition: all 0.3s;
    border-radius: 50%;
    border: 1rpx solid #ddd;

    &:active {
      transform: scale(0.9);
    }

    &.active {
      background-image: url("./ic_sel.svg");
      border: none;
      animation: checkbox-pop 0.3s ease-in-out;
    }
  }

  >text {
    color: $themeColor;
    font-weight: 500;

    &:active {
      opacity: 0.8;
    }
  }
}

@keyframes checkbox-pop {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
