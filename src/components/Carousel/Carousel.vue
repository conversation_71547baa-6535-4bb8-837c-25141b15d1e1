<script>
import { computed, onMounted, onUnmounted, ref } from 'vue'

export default {
  name: 'Carousel',
  props: {
    images: {
      type: Array,
      required: true,
    },
    height: {
      type: String,
      default: '400px',
    },
    interval: {
      type: Number,
      default: 5000,
    },
  },
  setup(props) {
    const currentIndex = ref(0)

    const wrapperStyle = computed(() => ({
      transform: `translateX(-${currentIndex.value * 100}%)`,
      width: `${props.images.length * 100}%`,
    }))

    const next = () => {
      currentIndex.value = (currentIndex.value + 1) % props.images.length
    }

    const prev = () => {
      currentIndex.value = (currentIndex.value - 1 + props.images.length) % props.images.length
    }

    const goToSlide = (index) => {
      currentIndex.value = index
    }

    let intervalId = null
    const startAutoPlay = () => {
      intervalId = setInterval(next, props.interval)
    }
    const stopAutoPlay = () => {
      clearInterval(intervalId)
    }

    onMounted(() => {
      startAutoPlay()
    })

    onUnmounted(() => {
      stopAutoPlay()
    })

    return {
      currentIndex,
      wrapperStyle,
      next,
      prev,
      goToSlide,
    }
  },
}
</script>

<template>
  <div class="relative w-full overflow-hidden" :style="{ height }">
    <div class="h-full flex transition-transform duration-500 ease-in-out" :style="wrapperStyle">
      <div
        v-for="(image, index) in images"
        :key="index"
        class="h-full w-full flex-shrink-0 bg-center"
        :style="{
          backgroundImage: `url(${image})`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
        }"
      />
    </div>
    <button
      class="absolute left-4 top-1/2 transform rounded-full bg-black bg-opacity-50 p-2 text-white transition-opacity duration-300 -translate-y-1/2 hover:bg-opacity-75"
      @click="prev"
    >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    <button
      class="absolute right-4 top-1/2 transform rounded-full bg-black bg-opacity-50 p-2 text-white transition-opacity duration-300 -translate-y-1/2 hover:bg-opacity-75"
      @click="next"
    >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>
    <div class="absolute bottom-4 left-1/2 flex transform -translate-x-1/2 space-x-2">
      <button
        v-for="(_, index) in images"
        :key="index"
        class="h-3 w-3 rounded-full transition-all duration-300" :class="[
          currentIndex === index ? 'bg-white scale-125' : 'bg-white bg-opacity-50 hover:bg-opacity-75',
        ]"
        @click="goToSlide(index)"
      />
    </div>
  </div>
</template>
