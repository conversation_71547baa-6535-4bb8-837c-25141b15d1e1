<script lang="ts" setup>
import { goodsOverlayProps } from './type'
import { useGoodsStore } from '@/store/goods'
import { chainTurning } from '@/services'
import { openOthers } from '@/common/jump'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

const { transferContent, show } = defineProps(goodsOverlayProps)
const emit = defineEmits(['toggleShow', 'clearClipboard'])

console.log('[goods-overlay] transferContent', transferContent.value)

const { goods } = useGoodsStore()
console.log('[goods-overlay] goods', goods.value)

function handleClose() {
  emit('toggleShow', {
    show: false,
  })
}

async function clickShare() {
  handleClose()

  await uni.showLoading({
    mask: true,
  })

  const shareLink = await chainTurning({
    goodsId: transferContent.value.id,
    platformCode: transferContent.value.platformCode,
  })

  uni.hideLoading()

  if (shareLink.platformCode === 'TB' && shareLink.needAuth) {
    await openOthers(shareLink, shareLink.platformCode)
    return false
  }

  let templateText = `${transferContent.value.title}
【在售价】${transferContent.value.orgPrice}元
【到手价】${transferContent.value.actualPrice}元
-----------------
`

  if (transferContent.value.platformCode === 'TB') {
    templateText += `${shareLink?.goodsUrl}\r\n${shareLink?.shortClickUrl}`
  }
  else if (transferContent.value.platformCode === 'DY') {
    templateText += `${shareLink?.goodsUrl}`
  }
  else {
    templateText += `${shareLink?.shortClickUrl}`
  }

  await setClipboardData(templateText, true)
}

function clickGoods() {
  console.log('clickGoods', transferContent.value)
  handleClose()
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${transferContent.value.id}&platformCode=${transferContent.value.platformCode}&clip=1`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goods,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

onMounted(() => {
  if (!transferContent.value) {
    transferContent.value = JSON.parse(goods.value)
    console.log('onMounted -> transferContent', transferContent.value)
  }
})
</script>

<template>
  <view class="goods-overlay">
    <wd-overlay :show="true" :z-index="105">
      <view class="wrapper">
        <wd-card>
          <template #title>
            <view class="relative text-center">
              <span class="h-8px w-8px color-red">
                .
              </span>
              已为你找到
              <span class="h-8px w-8px color-red">
                .
              </span>
              <wd-icon name="close" custom-class="absolute right-0" @click="handleClose" />
            </view>
          </template>
          <view class="flex justify-between">
            <view class="mr-0.5rem">
              <wd-img :src="transferContent.picUrl" width="7.5rem" height="7.5rem" radius="0.5rem" />
            </view>
            <view md:mx-1rem class="right-cont flex flex-col justify-around">
              <text line-clamp-2 text-ellipsis whitespace-pre-line style="position:relative">
                <wd-img :src="`/static/platform/${transferContent.platformCode}.png`" class="h-1rem w-1rem" style="position:absolute" />
                <text class="ml-1.25rem color-#262626">
                  {{ transferContent.title }}
                </text>
              </text>
              <view class="flex items-center text-0.75rem">
                <text v-if="transferContent.couponAmount > 0" class="mr-0.5rem bg-#fa4126 text-red-50" style="color:#fff;border-radius:0.2rem;padding:0.1rem 0.3rem">
                  券 <text class="pl-0.2rem" style="border-left:1px dashed #fff">
                    {{ transferContent.couponAmount }}
                  </text>
                </text>
                <text class="px-0.2rem text-red-500" style="border:1px solid #fa4126;border-radius:0.1rem">
                  约返{{ transferContent.maxProfit }}
                </text>
              </view>
              <view class="flex items-center text-red-500">
                ￥<text class="font-bold">
                  {{ transferContent.actualPrice }}
                </text>
                <text class="ml-0.5rem bg-#fcead5 p-0.2rem text-0.5rem color-#d9b953" style="border-radius:0.2rem">
                  已显示真实活动价格及返利
                </text>
              </view>
              <view class="flex items-center text-0.75rem">
                <wd-img src="/static/img/dp.png" class="mr-0.15rem h-0.95rem w-0.95rem" />
                <text text-gray-400>
                  {{ transferContent.shopTitle }}
                </text>
              </view>
            </view>
          </view>
          <template #footer>
            <view class="flex justify-between">
              <view class="text-align-left">
                <wd-button size="large" plain custom-class="bg-red" @click="clickShare">
                  <text class="text-0.85rem">
                    分享赚佣金
                  </text>
                </wd-button>
              </view>
              <view class="text-align-right" :class="{ 'pdd-tip': transferContent.platformCode == 'PDD' }">
                <wd-button size="large" ml-1 custom-class="bg-red" @click="clickGoods">
                  <text class="text-0.85rem">
                    下单领返利
                  </text>
                  <br>
                  <text v-if="transferContent.platformCode == 'PDD'" class="mt--1rem text-0.65rem">
                    需一小时后下单得返利
                  </text>
                </wd-button>
              </view>
            </view>
          </template>
        </wd-card>
      </view>
    </wd-overlay>
  </view>
</template>

<style lang="scss" scoped>
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    margin: 0 auto;

    .content {
      .right-cont {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
      }
    }

    ::v-deep .wd-card {
      .wd-card__footer {
        .wd-button {
          line-height: 1;
          padding: 0 30px;
        }
      }
    }
  }
</style>

<route lang="json5" type="page">
{
  style: { navigationBarTitleText: 'goods-overlay' },
}
</route>
