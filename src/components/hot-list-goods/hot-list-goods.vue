<script lang="ts">
/* eslint-disable */
export default {
  name: 'hot-list-goods',
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>
<script setup lang="ts">
import { appInfo } from '@/common'
import { hotListGoodsProps } from './type'
import { defineProps, watch, ref } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { queryGoodsList } from '@/services/goods'
import { useGoodsStore } from '@/store/goods'
import { getGoodsText } from "@/common/goods"
const props = defineProps(hotListGoodsProps)
const result = ref<any>(null)
const { setGoods } = useGoodsStore()

watch(
  () => props.platformCode,
  (newValue) => {
    queryGoods({ ...props.params, platformCode: newValue })
  },
  { deep: true, immediate: true },
)

async function queryGoods(params: appGoodsList.Params) {
  async function fetchGoodsList() {
    const { list = [] } = await queryGoodsList(params)
    // 只返回list 的前6项
    let first6Items = list.slice(0, 6);
    return first6Items
  }
  result.value = useQuery({ queryKey: [`hot-list-${params.platformCode}`], queryFn: fetchGoodsList })
}

function goodListClickHandle(goodsDetails: { id: any; platformCode: any }) {
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

function handleMore(platformCode: string) {
  uni.switchTab({
    url: `/pages/rank/index?platformCode=${platformCode}`,
  })
}
</script>

<template>
  <view :class="'hot-list-goods hot-list-goods-' + props.platformCode">
    <wd-loading size="50px" v-if="result.isLoading" />
    <view class="container">
      <view class="title" h-2rem lh-2rem block visible m-2>
        <img :src="'/static/platform/' + props.platformCode + '.png'" rounded-2 float-left w-2rem
          h-2rem border-box />
        <view flex>
          <text flex-1 ml-1 color-red font-extrabold text-1rem>{{ getGoodsText(props.platformCode) }}实时热销榜</text>
          <text w-3em @click="handleMore(props.platformCode)">更多></text>
        </view>
      </view>
      <view class="list-info" rounded-1rem>
        <view v-for="(good, index) in result.data" :key="good.id" class="list-item-info"
          @click="goodListClickHandle(good)" w-12rem relative flex m-1rem>
          <view class="image-content" mt-1rem>
            <view flex justify-start items-center>
              <image style="width: 4rem; height: 4rem; background-color: #eeeeee;" mode="aspectFill" relative
                :class="index === result.data.length - 1 ? 'mb-2rem' : ''" :src="good.picUrl" />
              <text absolute top-1rem bg-yellow rounded-1rem lh-2rem w-2rem h-2rem text-center> {{ index }}</text>
            </view>
          </view>
          <view :class="index === result.data.length - 1 ? 'mb-2rem' : ''" mt-1rem text-0.8rem max-w-7rem text-container
            overflow-hidden text-ellipsis whitespace-nowrap>
            {{ good.title }}
            <view class="mb-2 mt-2 text-left">
              <span class="border-rd-4px bg-#ff001b text-left text-#fff">劵{{ good.couponAmount }}</span>
              <span class="ml-4 border-rd-4px bg-#fa4126 text-left text-#fff">约返{{ good.discount }}</span>
            </view>
            <view mb-2 text-left flex>
              <span class="text-left flex-1">券后{{ good.actualPrice }}</span>
              <span class="text-left"><wd-icon name="search" size="12px"></wd-icon></span>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>


<style scoped lang="scss">
.hot-list-goods {
  background-color: #ffdccf;
  border-radius: 20px;
  width: 16rem;
  margin-right: 5vw;

  .container {
    padding: 20px;
    width: 60vw;
    margin: 0 auto;
    overflow: hidden;

    .list-info {
      color: #464646;
      background: #fff
    }
  }
}
</style>

<route lang="json5" type="page">
{
  style: { navigationBarTitleText: 'hot-list-goods' },
}
</route>
