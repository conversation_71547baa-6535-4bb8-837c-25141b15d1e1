<script lang="ts">
/* eslint-disable */
export default {
  name: 'login-tabs',
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>
<template>
  <view class="login_method">
    <view class="title" v-for="(item, index) in tabsArr" :key="index" :class="{ active: (selIndex == index) }"
      @click="tabClick(index)">{{ item }}</view>

  </view>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { loginTabsProps } from './type'
const props = defineProps(loginTabsProps)
const emit = defineEmits(['tabClick'])
function tabClick(tag: any) {
  emit('tabClick', tag)
}
</script>



<style lang="scss" scoped>
//主题色 red
$themeColor: #ff4d4f;

.login_method {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 96rpx;
  padding-bottom: 10rpx;

  .title {
    font-size: 32rpx;
    color: #666;
    position: relative;
    padding-bottom: 16rpx;
    transition: all 0.3s;
    font-weight: normal;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 4rpx;
      background-color: transparent;
      transform: translateX(-50%);
      transition: all 0.3s ease;
      border-radius: 2rpx;
    }

    &.active {
      color: $themeColor;
      font-weight: 600;

      &:after {
        width: 60%;
      background-color: $themeColor;
      }
    }

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
