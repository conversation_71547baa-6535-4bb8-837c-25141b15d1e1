<!--
 * @Author: dongpenghu
 * @Date: 2024-04-27 20:37:20
 * @LastEditors: dongpenghu
 * @LastEditTime: 2024-04-28 00:01:27
 * @Description: file content
 * @FilePath: \juwatech\src\components\goods-list\goods-list.vue
-->
<script lang="ts">
/* eslint-disable */
export default {
  name: 'goods-list',
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: 'shared',
  },
}
</script>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { goodsListProps } from './type'

const props = defineProps(goodsListProps)

const emit = defineEmits(['click', 'addcart', 'thumb'])
const independentID = ref<string>('')

onMounted(() => {
  init()
})

function onClickGoods(id: string, index: number) {
  console.log(id, index);
  emit('click', { id, index })
}

function onAddCart(id: string, index: number) {
  emit('addcart', { id, index })
}

function onClickGoodsThumb(id: string, index: number) {
  emit('thumb', { id, index })
}

function init() {
  genIndependentID(props.id || '')
}

function genIndependentID(id: string) {
  if (id)
    independentID.value = id
  else
    independentID.value = `goods-list-${~~(Math.random() * 10 ** 8)}`
}
</script>

<template>
  <view :id="independentID" class="goods-list-wrap rounded-lg">
    <block v-for="(item, index) in goods" :key="index">
      <goods-card
        :id="`${independentID}-gd-${index}`"
        :data="item"
        :currency="item.currency || '¥'"
        :thresholds="thresholds"
        class="goods-card-inside"
        @thumb="onClickGoodsThumb(`${independentID}-gd-${index}`, index)"
        @click="onClickGoods(`${independentID}-gd-${index}`, index)"
        @add-cart="onAddCart(`${independentID}-gd-${index}`, index)"
      />
    </block>
  </view>
</template>

<style lang="scss" scoped>
.goods-list-wrap {
  display: flex;
  flex-flow: row wrap;
  //justify-content: space-between;
  padding: 0;
  //background: #f8f8f8;
}
</style>
