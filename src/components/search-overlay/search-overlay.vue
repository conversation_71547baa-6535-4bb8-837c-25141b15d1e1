<script lang="ts" setup>
import { searchOverlayProps } from './type'
import { useGoodsStore } from '@/store/goods'

const { content, show, tabbar } = defineProps(searchOverlayProps)

const emit = defineEmits(['toggleShow', 'clearClipboard'])

const { clipboardData } = useGoodsStore()

console.log('[search overlay] content', content)

const tabs = ref([
  { title: '淘宝', icon: 'cart', code: 'TB', imgUrl: 'https://szk-001.oss-cn-hangzhou.aliyuncs.com/icon/jg_tbtm.gif' },
  { title: '京东', icon: 'cart', code: 'JD', imgUrl: 'https://szk-001.oss-cn-hangzhou.aliyuncs.com/icon/jg_jd.png' },
  { title: '抖音', icon: 'cart', code: 'DY', imgUrl: 'https://szk-001.oss-cn-hangzhou.aliyuncs.com/icon/jg_dy.png' },
  { title: '唯品会', icon: 'cart', code: 'WPH', imgUrl: 'https://szk-001.oss-cn-hangzhou.aliyuncs.com/icon/jg_wph.png' },
  { title: '拼多多', icon: 'cart', code: 'PDD', imgUrl: 'https://szk-001.oss-cn-hangzhou.aliyuncs.com/icon/jg_pdd.png' },
])

// 是否简单版本，复杂版本抄的易资的UI
const simpleDialog = ref<boolean>(true)

function handleClose() {
  emit('toggleShow', {
    show: false,
  })
}

function handleSearch(type: string, content: string) {
  emit('clearClipboard')
  console.log('[search-overlay] 未查到券时点击了去搜索，清空剪贴板')

  console.log('type', type, 'content', content)
  if (!content || content === '') {
    content = clipboardData.value
  }
  uni.navigateTo({
    url: `/pages/search/index?type=${type}&text=${content}`,
    success: () => handleClose(),
    fail: () => handleClose(),
  })
}

function handleChain(text: string) {
  emit('clearClipboard')
  console.log('[search-overlay] 未查到券时点击了一键转链，清空剪贴板')

  uni.navigateTo({
    url: `/pages/search/turnChain?text=${text}`,
    success: () => handleClose(),
    fail: () => handleClose(),
  })
}
</script>

<template>
  <view class="search-overlay">
    <wd-overlay :show="true" :z-index="105">
      <view class="wrapper">
        <view v-if="!simpleDialog">
          <wd-card>
            <template #title>
              <view class="text-center">
                <span class="h-8px w-8px color-red">
                  .
                </span>
                此商品没有返利
                <span class="h-8px w-8px color-red">
                  .
                </span>
                <wd-icon name="close" size="18px" custom-class="relative -right-12" @click="handleClose" />
              </view>
            </template>
            <view text-center color-black>
              <view class="textarea-container">
                <wd-textarea
                  v-model="content" custom-textarea-container-class="textarea-container" placeholder="长按粘贴到此处"
                  readonly
                />
              </view>
            </view>
            <view flex justify="center" gap="0.5rem" class="text-12px">
              <wd-button size="medium" flex-2 type="primary" custom-class="bg-red" @click="handleSearch('TB', content)">
                <text class="text-12px">
                  全网搜索
                </text>
              </wd-button>
              <wd-button size="medium" ml-1 type="warning" @click="handleChain(content)">
                一键转链
              </wd-button>
            </view>
            <view flex color-black justify="center" class="text-12px">
              <text mb-4 mt-4>
                平台搜索
              </text>
            </view>
            <wd-tabbar v-model="tabbar" :is_border="false">
              <wd-tabbar-item
                v-for="tab in tabs"
                :key="tab.title"
                :icon="tab.icon"
                :title="tab.title"
                @click="handleSearch(tab.code)"
              >
                <template #icon>
                  <wd-img
                    round height="60rpx" width="60rpx"
                    :src="tab.imgUrl"
                  />
                </template>
              </wd-tabbar-item>
            </wd-tabbar>
          </wd-card>
        </view>
        <view v-if="simpleDialog" class="rounded-lg bg-#fff">
          <view class="text-align-right">
            <wd-icon name="close" size="18px" custom-class="mt-0.5rem mr-0.5rem" @click="handleClose" />
          </view>
          <view class="px-3rem pb-1rem pt-1rem">
            <text class="text-1rem font-bold">
              抱歉，该商品暂时不支持返利
            </text>
            <wd-button block size="large" custom-style="width:100%;margin-top:1rem" @click="handleClose">
              我知道了
            </wd-button>
          </view>
        </view>
      </view>
    </wd-overlay>
  </view>
</template>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  ::v-deep .wd-card__title-content {
    padding-bottom: 6px;
  }
}

.wd-textarea {
  padding: var(--wot-textarea-cell-padding, 10px) 0;
}
:deep(.textarea-container) {
  background: var(--wot-button-info-bg-color, #F0F0F0);
  border-radius: 0.5rem;

  .wd-textarea__value {
    width: 100%;
    height: 100%;
    padding: 10rpx;

    .wd-textarea__inner {
      font-size: 0.75rem;
    }
  }
}

:deep(.wd-tabbar--default.is-border::after) {
  height: 0;
}

:deep(.wd-textarea__readonly-mask) {
  padding: 0;
}
</style>

<route lang="json5" type="page">
{
  style: { navigationBarTitleText: 'search-overlay' },
}
</route>
