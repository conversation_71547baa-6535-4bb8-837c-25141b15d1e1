<script setup lang="ts">
import { activityCardProps } from './type'

const props = defineProps(activityCardProps)

const activity = ref<any>({})

onMounted(() => {
  activity.value = props.data
})
</script>

<template>
  <view class="activitys-list-item rounded-lg bg-#fff px-0 py-0">
    <view class="title mt-0.25rem font-bold">
      {{ activity.title }}
    </view>
    <view class="picont mt-0.5rem">
      <view class="picbag h-7rem rounded-lg" :style="{ backgroundImage: `url(${activity.img})`, backgroundRepeat: `no-repeat`, backgroundSize: `100% 100%` }" />
    </view>
    <view class="botbtn mt-0.5rem flex justify-between px-0.5rem">
      <slot />
    </view>
  </view>
</template>

<style scoped lang="scss">
  .activitys-list-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
  }
</style>
