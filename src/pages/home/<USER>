<script lang="ts" setup>
/* eslint-disable */
import { reactive, ref } from 'vue'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { useGoodsStore } from '@/store/goods'
import type KV from '@/model/KV'
import { chainActivity, checkVersion, hideTeam, logoff, queryBannerList, queryGoodsCategory, queryGoodsList, queryJinGangList } from '@/services'
import type { PlatformCode } from '@/types/platformCode'
import { useAuthStore } from '@/store/user'
import { appInfo } from '@/common'
import { queryHotKey } from '@/services'
import { openOthers, openWeChatMiniProgram } from '@/common/jump'

// const advertRef = ref()
const { setGoods } = useGoodsStore()
const tabList = ref<KV<string>[]>([])
const currentTab = ref<PlatformCode>('all' as PlatformCode)
const state = ref<LoadMoreState>('loading')

const goods = ref<any[]>([] as any[])
const jingangList = ref<appBannerJinGangList.Response['data']>([] as appBannerJinGangList.Response['data'])
const cpslist = ref<appActivityQueryActivityList.Response['data']>([] as appActivityQueryActivityList.Response['data'])
const cpslist2 = ref<appActivityQueryActivityList.Response['data']>([] as appActivityQueryActivityList.Response['data'])
const goodsListLoadStatus = ref<number>(0)
const current = ref<number>(0)
const searchValue = ref<string>('')
const imgSrcs = ref<string[]>([])
const bannerList = ref<appBannerBannerList.Response['data']>([] as appBannerBannerList.Response['data'])
const bannerCenter = ref<appBannerBannerList.Response['data']>([] as appBannerBannerList.Response['data'])
const autoplay = ref(true)
const duration = ref<number>(500)
const interval = ref<number>(5000)

// 控制金刚区轮播图显示
const shouldHideJingang = ref(false)

// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

async function itemClick(item: appBannerJinGangList.Response['data']) {
  console.log('%c [ item ]-96', 'font-size:13px; background:pink; color:#bf2c9f;', item)
  // jump_type=URL，点击金刚图标，直接跳转jump_url链接
  // jump_type=ACTIVITY，有以下类型：
  //   jump_url="/channel/*"   跳转到栏目页，目前有淘宝、京东、拼多多、抖音、唯品会几个栏目，尽可能统一复用页面格式样式
  //   jump_url="/category/*"  活动类集合，将多个活动图列表排列，可前往活动会场，可复制口令文案，尽可能统一格式样式，做到通用
  //   jump_url="/goodslist/*" 商品集合，尽可能统一格式样式，做到通用
  // jump_type=HOE_ACTIVITY  点击金刚图标，需要使用activityId请求活动转链接口，拿到转链后的url，然后再跳转到转链后url链接
  if (item.jump_type === 'URL') {
    uni.navigateTo({
      url: `/pages/iframe?url=${encodeURIComponent(item.jump_url)}&name=${item.jingangname || item.categoryname}`,
    })
  }
  if (item.jump_type === 'ACTIVITY') {
    let url = ''
    if (item.jump_url.startsWith('/channel/')) {
      url = `/pages/channel/index?platformCode=${item.activityid}&title=${item.jingangname || item.categoryname}`
    }
    if (item.jump_url.startsWith('/category/')) {
      url = `/pages/category/index?activityid=${item.activityid}&title=${item.jingangname || item.categoryname}`
    }
    if (url !== '') {
      uni.navigateTo({
        url: url,
      })
    }
  }
  if (item.jump_type === 'HOE_ACTIVITY') {
    await uni.showLoading({
      mask: true,
    })
    const data = await chainActivity({
      id: item.jump_url
    })
    uni.hideLoading()
    await openOthers(data, data.platformCode, item.jingangname || item.categoryname)
  }
  // 兼容之前的逻辑
  if (item.jump_type === 'WX_ACTIVITY') {
    const parse = JSON.parse(item.jump_url);
    const data = {
      wxMiniAppId: parse.appid,
      wxMiniprogramPath: parse.path,
    }
    await openOthers(data, data.platformCode, item.jingangname || item.categoryname)
  }
}

const goodListPagination = reactive({
  index: 0,
  num: 20,
})
const { userToken } = useAuthStore()
console.log('🚀 ~ userToken:', userToken)

onMounted(async () => {
  if (userToken.value) {
    // setTimeout(async () => {
    //   // await advertRef.value.initAdvert()
    //   await init()
    // }, 100)
    await init()

    // 检查是否需要隐藏金刚区轮播图（iOS平台且hideTeam为hide）
    // #ifdef APP-PLUS
    if (plus.os.name === 'iOS') {
      try {
        const result = await hideTeam({
          current_version: appInfo.version,
        })
        if (result.data && result.data === 'hide') {
          shouldHideJingang.value = true
        }
      } catch (error) {
        console.error('检查hideTeam状态失败:', error)
      }
    }
    // #endif
  }
  else {
    uni.reLaunch({
      url: '/pages/login/index',
    })
  }
})

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    loadGoodsList() // 调用加载更多的函数
  }
})

const scrollTop = ref<number>(0)
onPageScroll((e) => {
  scrollTop.value = e.scrollTop
  // console.log('当前页面滚动距离', e.scrollTop)
})

const privateData = reactive({
  tabIndex: 0,
})

const hotKeys = ref<string[]>([])
const hotKey = ref<string>('购物先查券，优惠又省钱')
// 加载热门话题
async function loadHotKey(data: []) {
  hotKeys.value = data
  hotKey.value = data[0] || '购物先查券，优惠又省钱'
  setInterval(() => hotKey.value = hotKeys.value[getRandomInt(1, hotKeys.value.length)] || '购物先查券，优惠又省钱', 5 * 1000)
}

function getRandomInt(min: number, max: number) {
  min = Math.ceil(min)
  max = Math.floor(max)
  return Math.floor(Math.random() * (max - min + 1)) + min
}

async function init() {
  await loadHomePage()
}

async function parsePageData() {
  // 优先从缓存拿
  const homePageData = uniStorage.getItem('preload_home_page_data')
  console.log('homePageData', homePageData)
  if (homePageData) {
    console.log('storage中存在预加载数据，说明当前是首次打开APP')
    uniStorage.removeItem('preload_home_page_data')
    return homePageData
  }
  console.log('不存在预加载数据，不是首次打开APP，需要撸一次API接口')
  // 缓存拿不到，合并请求一次API
  const [banner, jingang, cps, cps2, center, hotKey] = await Promise.all([
    queryBannerList({ position: 'home_top' }),
    queryJinGangList({ marketType: 'jinGang' }),
    queryBannerList({ position: 'home_center_right' }),
    queryBannerList({ position: 'home_center_left' }),
    queryBannerList({ position: 'home_center' }),
    queryHotKey({ minId: '1', tag: 'cycle' }),
  ])
  return {
    banner,
    jingang,
    cps,
    cps2,
    center,
    hotKey: hotKey.data,
  }
}

async function loadHomePage() {
  const tabs = await queryGoodsCategory()

  // const banner = await queryBannerList({ position: 'home_top' })
  // const jingang = await queryJinGangList({
  //   // marketChannelType: 'dyChannel',//首页金刚区，此参数可以为空
  //   marketType: 'jinGang',
  // })
  // const cps = await queryBannerList({ position: 'home_center_right' })
  // const cps2 = await queryBannerList({ position: 'home_center_left' })
  // const center = await queryBannerList({ position: 'home_center' })

  const { banner, jingang, cps, cps2, center, hotKey } = await parsePageData()

  cpslist.value = cps
  cpslist2.value = cps2
  jingangList.value = jingang
  bannerList.value = banner
  bannerCenter.value = center
  tabList.value = tabs
  imgSrcs.value = banner?.map(item => item.picture_url)

  await loadHotKey(hotKey)

  await loadGoodsList()
}
// 获取元素离页面顶部的距离
async function getElementScollTop(className: string) {
  const query = uni.createSelectorQuery()
  query
    .select(className)
    .boundingClientRect(async (data: any) => {
      const pageScrollTop = Math.round(data.top)
      await uni.pageScrollTo({
        scrollTop: pageScrollTop, // 滚动的距离
        duration: 0, // 过渡时间
      })
    })
    .exec()
}

function onReTry() {
  loadGoodsList()
}

async function loadGoodsList(fresh = false) {
  if (goodsListLoadStatus.value === 1) {
    console.log('[home goods] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const Limit = goodListPagination.num
  let PageNo = privateData.tabIndex * Limit + goodListPagination.index + 1
  if (fresh)
    PageNo = 1

  const platformCode = currentTab.value.replace(
    'all',
    '',
  ) as appGoodsList.Params['platformCode']

  const platformQueries = {
    '': {
      queryType: 'choice',
      platformCode: 'TB',
    },
    'TB': { queryType: 'list', queryValue: '1' },
    'KL': { queryType: 'list' },
    'JD': {
      queryType: 'list',
      queryValue: currentTab.value === 'all' ? '2' : '1',
    },
    'DY': { queryType: 'list', queryValue: '1' },
    'WPH': { queryType: 'list', queryValue: '1' },
    'PDD': { queryType: 'list', queryValue: '1' },
  }

  try {
    const { list = [] } = await queryGoodsList({
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
      platformCode,
      ...platformQueries[platformCode],
    })
    goodsListLoadStatus.value = 0

    if (!list || list.length <= 0) {
      console.log('[home goods] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        goods.value = []
      }
      if (goods.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      goods.value = list || []
    else if (list && list.length > 0)
      goods.value = goods.value.concat(list) // 追加新加载的商品

    goodListPagination.index = PageNo // 更新当前页码
    goodListPagination.num = Limit
    // nextTick(async () => {
    // if (fresh)
    // await getElementScollTop('.home-main-tabs-content')
    // })
  }
  catch (err) {
    console.error('[home goods] 首页加载失败', err)
    goods.value = []
    goodsListLoadStatus.value = 3
  }
}

function goodListClickHandle({ index }: { index: number }) {
  const goodsDetails = goods.value[index]
  console.log(index, goodsDetails)
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

function goodListAddCartHandle() {

}

function navToSearchPage() {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

// home_top
async function handleSwiper(e: { index: number }) {
  if (bannerList.value) {
    let banner = bannerList.value[e.index]
    console.log('banner', banner)
    // uni.navigateTo({
    //   url: `/pages/iframe?url=${encodeURIComponent(banner.jump_url)}&name=${banner.categoryname}`,
    // })
    await itemClick(banner)
  }
}

// home_center
async function handleSwiper1(e: { index: number }) {
  if (bannerCenter.value) {
    let banner = bannerCenter.value[e.index]
    console.log('banner', banner)
    // uni.navigateTo({
    //   url: `/pages/iframe?url=${encodeURIComponent(banner.jump_url)}&name=${banner.categoryname}`,
    // })
    await itemClick(banner)
  }
}

// home_center_left
async function handleSwiper2(e: { index: number }) {
  if (cpslist2.value) {
    let banner = cpslist2.value[e.index]
    console.log('banner', banner)
    // uni.navigateTo({
    //   url: `/pages/iframe?url=${encodeURIComponent(banner.jump_url)}&name=${banner.categoryname}`,
    // })
    await itemClick(banner)
  }
}

function handleTable(e: { index: number }) {
  if (tabList.value[e.index].label) {
    currentTab.value = tabList.value[e.index].label as appGoodsList.Params['platformCode']
    state.value = 'loading'
    isDataEmpty.value = 0
    noMoreData.value = 0
    goods.value = []
    loadGoodsList(true)
  }
}

function handleAdv() {
  uni.navigateTo({
    url: `/pages/home/<USER>
  })
}

function handleServer(type: 'service' | 'link' | string) {
  if (type === 'service') {
    // message.alert('客服')
    uni.navigateTo({
      // url: `/pages/iframe?url=${encodeURIComponent(appInfo.serverApi)}material/detail/169`,
      // test???
      url: `/pages/personal/material?materialId=169&name=客服`,
    })
  }
  else if (type === 'help') {
    uni.navigateTo({
      url: `/pages/home/<USER>
    })
  }
  else {
    uni.navigateTo({
      url: `/pages/search/turnChain`,
    })
  }
}
</script>

<template>
  <overlay-search-wraper>
    <view class="home bg-#edf2f5">
      <!--开屏页-->
      <!--<advert-view ref="advertRef" :timedown="1" />-->
      <view>
        <!-- <view class="status-bar bg-red-5" /> -->
        <!--
          <wd-message-box selector="wd-message-box-slot">
            <wd-img :width="200" :height="200" src="https://h7ml.cn/wechat.jpg" :enable-preview="true" />
          </wd-message-box>
          -->
        <!-- 顶部区域 -->
        <view class="home-header">
          <view class="search bg-transparent" flex justify-between>
            <!-- 搜索框 -->
            <view flex-1>
              <wd-search v-model="searchValue" custom-class="custom-search bg-#fff h-10 border-rd-25px reactive"
                :placeholder="hotKey" disabled hide-cancel placeholder-left flex-1 @click="navToSearchPage">
                <template #prefix>
                  <div class="font-18px ml-4 bg-#fff text-left color-black font-700">
                    省赚客
                  </div>
                </template>
              </wd-search>
            </view>
            <!-- 客服 + 转链 -->
            <view justify-end>
              <span class="flex color-#fff">
                <!--
                  <span class="linkFontSize ml-4vw w-10vw" @click="handleServer('service')">
                    <wd-icon name="service" ml-0.2rem text-center size="1.2rem" />
                    <br>
                    客服
                  </span>
                  -->
                <span class="linkFontSize ml-4vw w-10vw" @click="handleServer('help')">
                  <wd-icon name="help-circle" ml-0.2rem text-center size="1.2rem" />
                  <br>
                  帮助
                </span>
                <!--
                  <span class="linkFontSize w-10vw" @click="handleServer('link')">
                    <wd-icon name="link-unlink" ml-0.2rem size="1.2rem" />
                    <br>
                    转链
                  </span>
                  -->
              </span>
            </view>
          </view>
        </view>
        <!-- 搜索框下的两个轮播 -->
        <view class="home-top-banners">
          <!-- home_top轮播图 -->
          <view v-if="imgSrcs.length" class="swiper px2 pb2">
            <wd-swiper height="6rem" :img-srcs="imgSrcs" :autoplay="autoplay" :duration="duration" :interval="interval"
              indicator-position="bottom-right" image-mode="scaleToFill" :current="current"
              :indicator="{ type: 'fraction' }" :list="imgSrcs" @click="handleSwiper" />
          </view>
          <!-- 金刚区轮播图 -->
          <view v-if="!shouldHideJingang" class="px-2">
            <view class="home-jingan relative border-rd-10px bg-#fff">
              <nav-swiper :list="jingangList" :row-count="3" :column-count="5" @item-click="itemClick" />
            </view>
          </view>
        </view>
        <!-- 广告区 -->
        <view v-if="!shouldHideJingang" class="advertising md:hidden md:h-5rem" @click="handleAdv" />
        <!-- home_center轮播图 -->
        <view v-if="bannerCenter?.length && !shouldHideJingang" class="border-rd-4px px2 mt2">
          <wd-swiper height="6rem" :autoplay="autoplay" :duration="duration" :interval="interval"
            indicator-position="bottom-right" image-mode="scaleToFill" :current="current"
            :indicator="{ type: 'fraction' }" :list="bannerCenter?.map((item) => item.picture_url)"
            @click="handleSwiper1" />
        </view>
        <!-- 轮播图 + 豆腐块 -->
        <view v-if="cpslist?.length && !shouldHideJingang" class="home-cpslist flex px2 mt2 mb2 md:hidden">
          <!-- home_center_left轮播图 -->
          <view class="view w-full" mr-0.5rem flex-1>
            <wd-swiper height="14.25rem" :list="cpslist2?.map((cps) => cps.picture_url)" @click="handleSwiper2" autoplay
              :current="0" imageMode="scaleToFill" />
          </view>
          <!-- home_center_right轮播图（外卖、打车） -->
          <view class="border-rd-4px" flex-1>
            <wd-img height="7rem" class="w-full" :src="cpslist[0].picture_url" @click="itemClick(cpslist[0])" />
            <wd-img height="7rem" class="w-full" :src="cpslist[1].picture_url" @click="itemClick(cpslist[1])" />
          </view>
        </view>
        <!-- 商品推荐 -->
        <view class="home-main pb-0 pt-0 min-h-screen">
          <view class="tabs">
            <wd-tabs v-if="tabList.length" v-model="currentTab" custom-class="goods-nav-tab-fix" @click="handleTable">
              <block v-for="(tab, index) in tabList" :key="index">
                <wd-tab class="home-main-tabs" :title="tab.title" :name="tab.label">
                  <view class="home-main-tabs-content bg-#edf2f5 p2">
                    <goods-list :goods="goods" @click="goodListClickHandle" @addcart="goodListAddCartHandle" />
                  </view>
                </wd-tab>
              </block>
            </wd-tabs>
          </view>
          <!-- loading -->
          <wd-loadmore :state="state" @reload="onReTry" />
          <!-- no data -->
          <wd-status-tip v-if="goodsListLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png"
            tip="当前搜索无结果" />
        </view>
      </view>
    </view>
    <UniBackTop :scroll-top="scrollTop" />
  </overlay-search-wraper>
</template>

<style lang="scss" scoped>
@import '@/plugins-status-bar.scss';

:deep(.home) {
  width: 100vw;
  box-sizing: border-box;

  .wd-overlay {
    background: transparent;
  }

  .wd-loadmore {
    margin-top: -0.5rem;
  }

  .wd-status-tip {
    height: 20rem;
  }

  .wd-tabs__nav-container {
    .wd-tabs__nav-item {
      font-size: 16px;
    }

    .is-active {
      color: red
    }
  }

  .advertising {
    background-image: url("@/static/guide.png");
    background-repeat: no-repeat;
    min-height: 1.5rem;
    background-size: 100%;
    margin-top: 0.5rem;
  }

  .linkFontSize {
    font-size: 0.8rem;

    .wd-icon {
      font-weight: bold;
    }
  }

  .home-header {
    //background-image: url(https://stares.oss-cn-hangzhou.aliyuncs.com/banner/oss_QaXYXf.jpg);
    //background-size: cover;
    //background-repeat: no-repeat;
    background-color: #FF0E07;
    // height: calc(19rem + var(--status-bar-height) + var(--status-bar-height)/2);
    width: 100%;
    height: 3rem;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 104;
    padding-top: calc(var(--status-bar-height) + 1rem);
    // border-top: 1px solid blue;

    .search {
      // margin-bottom: 20rpx;
      padding: 0 0.5rem;
      // border-top: 1px solid yellow;

      .custom-search {
        padding: 0;

        .wd-search__block {
          background: transparent;
        }

        .wd-search__input {
          overflow: hidden;
          border-radius: 10px;
          padding-left: 20px;
        }
      }
    }
  }

  .home-top-banners {
    background-image: url(https://stares.oss-cn-hangzhou.aliyuncs.com/banner/oss_QaXYXf.jpg);
    background-size: cover;
    background-repeat: no-repeat;
    padding-top: calc(var(--status-bar-height) + 4rem);
  }

  .home-main {
    min-height: 88vh;

    //.wd-tabs__nav {
    //  background: #edf2f5;
    //}
  }

  .home-main-tabs ::v-deep {
    .wd-tabs__nav-item {
      font-size: 18px;
    }

    .is-active {
      color: red;
    }
  }

  ::-webkit-scrollbar {
    background: transparent;
    /* 将滚动条背景色设为透明 */
  }

  .home-jingan {
    :deep(.jingan-card) {
      .wd-card__content {
        text-align: center;
      }
    }
  }

  .border-rd-logo {
    .wd-img__image {
      border-radius: 50%;
    }
  }

  .goods-nav-tab-fix .wd-tabs__nav {
    position: sticky;
    top: calc(var(--status-bar-height) + 4rem);
    z-index: 100;
  }
}

:deep(.wd-search__search-left-icon) {
  color: #ef0041;
  left: unset;
  right: 16px;
}
</style>

<route type="home" lang="json">
{}
</route>
