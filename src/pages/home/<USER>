<script setup lang="ts">
function handleBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="extensionNorm" min-h-screen>
    <view class="nav-wrapper">
      <wd-navbar title="使用教程" left-arrow :bordered="false" @click-left="handleBack" />
    </view>
    <view class="cont-wrapper min-h-screen">
      <wd-img src="/static/help/1.jpg" mode="widthFix" />
      <wd-img src="/static/help/2.jpg" mode="widthFix" />
      <wd-img src="/static/help/3.jpg" mode="widthFix" />
      <wd-img src="/static/help/4.jpg" mode="widthFix" />
      <wd-img src="/static/help/5.jpg" mode="widthFix" />
      <wd-img src="/static/help/6.jpg" mode="widthFix" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .extensionNorm {
    max-height: calc(100vh - var(--status-bar-height));
    height: calc(100vh - var(--status-bar-height));
    min-height: calc(100vh - var(--status-bar-height));

    .nav-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 100;
      padding-top: calc(var(--status-bar-height) + 0.5rem);
      background-color: #fff;
    }

    .cont-wrapper {
      padding-top: calc(var(--status-bar-height) + 3.35rem);
      display: flex;
      flex-direction: column;

      .wd-img {
        margin-top: -1px;
      }
    }
  }
</style>

<route lang="json">
{
  "style": {
    "navigationBarTextStyle": "black"
  }
}
</route>
