<script lang="ts" setup>
/* eslint-disable */
import { reactive, ref } from 'vue'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { chainActivity, chainGroupBuy, queryBannerList, queryGroupGoodsList, queryJinGangList } from '@/services'
import { openOthers } from '@/common/jump'
import { checkAppInstalled, tabs } from './types'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

const currentTabIdx = ref<number>(0)
const state = ref<LoadMoreState>('loading')

const goods = ref<any[]>([] as any[])
const jingangList = ref<appBannerJinGangList.Response['data']>([] as appBannerJinGangList.Response['data'])
const goodsListLoadStatus = ref<number>(0)
const current = ref<number>(0)
const searchValue = ref<string>('')
const imgSrcs = ref<string[]>([])
const bannerList = ref<appBannerBannerList.Response['data']>([] as appBannerBannerList.Response['data'])
const autoplay = ref(true)
const show = ref(false)
const duration = ref<number>(500)
const interval = ref<number>(5000)

// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

async function changeTab(e: { index: number }) {
  currentTabIdx.value = e.index
  state.value = 'loading'
  isDataEmpty.value = 0
  noMoreData.value = 0
  await uni.showLoading({
    mask: true
  })
  await loadGoodsList(true)
  uni.hideLoading()
}

async function itemClick(item: appBannerJinGangList.Response['data']) {
  console.log('%c [ item ]-96', 'font-size:13px; background:pink; color:#bf2c9f;', item)
  // jump_type=URL，点击金刚图标，直接跳转jump_url链接
  // jump_type=ACTIVITY，有以下类型：
  // jump_url="/channel/*"   跳转到栏目页，目前有淘宝、京东、拼多多、抖音、唯品会几个栏目，尽可能统一复用页面格式样式
  // jump_url="/category/*"  活动类集合，将多个活动图列表排列，可前往活动会场，可复制口令文案，尽可能统一格式样式，做到通用
  // jump_url="/goodslist/*" 商品集合，尽可能统一格式样式，做到通用
  // jump_type=HOE_ACTIVITY  点击金刚图标，需要使用activityId请求活动转链接口，拿到转链后的url，然后再跳转到转链后url链接
  if (item.jump_type === 'URL') {
    // 如果是http或https开头的链接，直接跳转
    if (item.jump_url.startsWith('http') || item.jump_url.startsWith('https')) {
      uni.navigateTo({
        url: `/pages/iframe?url=${encodeURIComponent(item.jump_url)}&name=${item.jingangname || item.categoryname}`,
      })
    } else {
      let data = {
        "platformCode": "TB",
        "needAuth": false,
        "shortClickUrl": item.jump_url,
      }
      await openOthers(data, data.platformCode, item.jingangname || item.categoryname)
    }
  }
  if (item.jump_type === 'ACTIVITY') {
    let url = ''
    if (item.jump_url.startsWith('/channel/')) {
      url = `/pages/channel/index?platformCode=${item.activityid}&title=${item.jingangname || item.categoryname}`
    }
    if (item.jump_url.startsWith('/category/')) {
      url = `/pages/category/index?activityid=${item.activityid}&title=${item.jingangname || item.categoryname}`
    }
    if (url !== '') {
      uni.navigateTo({
        url: url,
      })
    }
  }
  if (item.jump_type === 'HOE_ACTIVITY') {
    await uni.showLoading({
      mask: true,
    })
    const data = await chainActivity({
      id: item.jump_url
    })
    uni.hideLoading()
    await openOthers(data, data.platformCode, item.jingangname || item.categoryname)
  }
  // 兼容之前的逻辑
  if (item.jump_type === 'WX_ACTIVITY') {
    const parse = JSON.parse(item.jump_url);
    const data = {
      wxMiniAppId: parse.appid,
      wxMiniprogramPath: parse.path,
    }
    await openOthers(data, data.platformCode, item.jingangname || item.categoryname)
  }
}

const goodListPagination = reactive({
  index: 0,
  num: 20,
})

onMounted(async () => {
  await init()
})

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    loadGoodsList() // 调用加载更多的函数
  }
})

// 默认经纬度是杭州武林广场
const location = reactive({
  longitude: 120.169883,
  latitude: 30.276771,
})

async function init() {
  // const banner = await queryBannerList({ position: 'bendi_top' })
  // bannerList.value = banner
  // imgSrcs.value = banner?.map(item => item.picture_url)
  //
  // jingangList.value = await queryJinGangList({
  //   marketChannelType: 'bendiChannel',
  //   marketType: 'jinGang',
  // })

  const [jingang,banner] = await Promise.all([
    queryJinGangList({
      marketChannelType: 'bendiChannel',
      marketType: 'jinGang',
    }),
    queryBannerList({ position: 'bendi_top' }),
    initLocationAndLoad(),
  ])
  jingangList.value = jingang
  bannerList.value = banner
  imgSrcs.value = banner?.map(item => item.picture_url)
}

async function initLocationAndLoad() {
  const granted = uni.getStorageSync('grant_location') || false
  if (!granted) {
    console.log('用户未授权位置权限，触发弹窗')
    // await uni.hideTabBar()
    show.value = true
    return false
  }
  uni.getLocation({
    type: 'wgs84',
    isHighAccuracy: true,
    success: function(res) {
      console.log('用户定位信息：', res);
      location.longitude = res.longitude
      location.latitude = res.latitude
      // 非首次授权
      // uni.showTabBar()
      show.value = false
      uni.setStorageSync('grant_location', true)
    },
    fail: function(err) {
      console.log('获取定位失败：', err)
      // 可能网络问题，可能是在设置中关了
      // uni.hideTabBar()
      show.value = true
    },
    complete: function() {
      loadGoodsList()
    }
  })
}

async function openLocation(failback: Function) {
  // 这里先恢复UI
  // await uni.showTabBar()
  show.value = false
  // 拿定位也有耗时
  uni.getLocation({
    type: 'wgs84',
    isHighAccuracy: true,
    success: function(res) {
      console.log('用户定位信息：', res);
      location.longitude = res.longitude
      location.latitude = res.latitude
      // 同意授权
      // uni.showTabBar()
      show.value = false
      uni.setStorageSync('grant_location', true)
    },
    fail: function(err) {
      console.log('获取定位失败：', err)
      // 拒绝授权
      // uni.showTabBar()
      show.value = false
      // uni.showToast({icon: 'none', title: '请手动前往设置开启权限',})
      const granted = uni.getStorageSync('grant_location')
      if (granted === '') {
        uni.setStorageSync('grant_location', false)
      } else {
        uni.openAppAuthorizeSetting()
      }
    },
    complete: function() {
      loadGoodsList()
    }
  })
}

function closeLocation() {
  // uni.showTabBar()
  show.value = false
  loadGoodsList()
}

function onReTry() {
  loadGoodsList()
}

async function loadGoodsList(fresh = false) {
  if (goodsListLoadStatus.value === 1) {
    console.log('[life goods] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const Limit = goodListPagination.num
  let PageNo = goodListPagination.index + 1
  if (fresh)
    PageNo = 1

  try {
    const data = await queryGroupGoodsList({
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
      longitude: location.longitude,
      latitude: location.latitude,
      channel: tabs[currentTabIdx.value].channel,
      platform: tabs[currentTabIdx.value].platform,
      bizLine: tabs[currentTabIdx.value].bizLine,
    })
    goodsListLoadStatus.value = 0

    if (!data || !data.list || data.list.length <= 0) {
      console.log('[life goods] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        goods.value = []
      }
      if (goods.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (data.list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      goods.value = data.list || []
    else if (data.list && data.list.length > 0)
      goods.value = goods.value.concat(data.list) // 追加新加载的商品

    goodListPagination.index = PageNo // 更新当前页码
    goodListPagination.num = Limit
  }
  catch (err) {
    console.error('[life goods] 本地生活加载失败', err)
    goods.value = []
    goodsListLoadStatus.value = 3
  }
}

function navToSearchPage() {
  uni.navigateTo({
    url: `/pages/life/search?longitude=${location.longitude}&latitude=${location.latitude}`,
  })
}

// home_top
async function handleSwiper(e: { index: number }) {
  if (bannerList.value) {
    let banner = bannerList.value[e.index]
    console.log('banner', banner)
    // uni.navigateTo({
    //   url: `/pages/iframe?url=${encodeURIComponent(banner.jump_url)}&name=${banner.categoryname}`,
    // })
    await itemClick(banner)
  }
}

function handleClickLeft() {
  uni.navigateBack()
}

async function shareCopy(goods: object) {
  await uni.showLoading({
    mask: true,
  })
  const data = await chainGroupBuy({
    channel: goods.platformCode,
    linkType: 2,
    platform: tabs[currentTabIdx.value].platform,
    bizLine: tabs[currentTabIdx.value].bizLine,
    skuViewId: goods.id,
  })
  uni.hideLoading()

  if (!data)
    return false

  let templateText = `${goods?.title}
【在售价】${goods?.orgPrice}元
【到手价】${goods?.actualPrice}元
-----------------
`
  if (data.platformCode === 'DYGROUP' || data.platformCode === 'DY') {
    templateText += `${data.goodsUrl}\r\n${data.clickUrl}`
  } else {
    templateText += `${data.clickUrl}`
  }

  await setClipboardData(templateText, true)
}

async function buyGoods(goods: object) {
  // 优先打开APP，其次是微信小程序，再不行就打开H5
  let linkType = 2

  // #ifdef APP-PLUS
  const existCps = await checkAppInstalled(tabs[currentTabIdx.value].channel)
  if (existCps) {
    linkType = 3
  } else {
    const existWx = await checkAppInstalled('WEIXIN')
    if (existWx) {
      linkType = 4
    }
  }
  // #endif

  await uni.showLoading({
    mask: true,
  })
  const data = await chainGroupBuy({
    channel: goods.platformCode,
    linkType: linkType,
    platform: tabs[currentTabIdx.value].platform,
    bizLine: tabs[currentTabIdx.value].bizLine,
    skuViewId: goods.id,
  })
  uni.hideLoading()

  // 如果非APP环境下，这个值只可能是1或2，否则会有3和4两种可能性
  if (linkType === 1 || linkType === 2) {
    let url = data.shortClickUrl || data.clickUrl || data.goodsUrl
    uni.navigateTo({
      url: `/pages/iframe?url=${encodeURIComponent(url)}&name=${tabs[currentTabIdx.value].name}`,
    })
  } else {
    await openOthers(data, data.platformCode)
  }
}

</script>

<template>
  <overlay-search-wraper>
    <view class="home bg-#edf2f5">
      <!--<view class="nav-bar">
        <wd-navbar title="本地生活" left-arrow :bordered="false" @click-left="handleClickLeft" />
      </view>-->
      <view class="contentss">
        <!-- 顶部区域 -->
        <view class="home-header">
          <view class="search bg-transparent">
            <!-- 搜索框 -->
            <wd-search
              v-model="searchValue" custom-class="custom-search bg-#fff h-10 border-rd-25px reactive"
              placeholder="搜索团购到店优惠,最高返10%" disabled hide-cancel placeholder-left flex-1 @click="navToSearchPage"
            >
              <template #prefix>
                <div class="font-18px ml-4 bg-#fff text-left color-black font-700">
                  省赚客
                </div>
              </template>
            </wd-search>
          </view>
        </view>
        <!-- 搜索框下的两个轮播 -->
        <view class="home-top-banners mb-0.5rem">
          <!-- home_top轮播图 -->
          <view v-if="imgSrcs.length" class="swiper p2">
            <wd-swiper
              height="6rem" :img-srcs="imgSrcs" :autoplay="autoplay" :duration="duration" :interval="interval"
              indicator-position="bottom-right" image-mode="scaleToFill" :current="current"
              :indicator="{ type: 'fraction' }" :list="imgSrcs" @click="handleSwiper"
            />
          </view>
          <!-- 金刚区轮播图 -->
          <view class="px-2">
            <view class="home-jingan relative border-rd-10px bg-#fff">
              <nav-swiper :list="jingangList" :row-count="3" :column-count="5" @item-click="itemClick" />
            </view>
          </view>
        </view>
        <!-- 渠道tab -->
        <view class="cateb">
          <view class="tabs">
            <wd-tabs v-if="tabs.length" v-model="currentTabIdx" @click="changeTab">
              <block v-for="(tab, index) in tabs" :key="index">
                <wd-tab class="home-main-tabs" :title="tab.name"></wd-tab>
              </block>
            </wd-tabs>
          </view>
        </view>
        <!-- 商品推荐 -->
        <view class="home-main px-0.5rem mt-0.5rem min-h-screen">
          <view class="categoods flex flex-wrap">
            <view class="categoods-item flex flex-col justify-between bg-#fff rounded-lg mb-0.5rem pb-0.5rem" v-for="good in goods" v-if="goods.length > 0" :key="good.id">
              <view class="cg-top w-full">
                <text v-if="good.shopTitle !== ''" class="shopt bg-#FA4126 text-#fff text-0.75rem">{{ good.shopTitle }}</text>
                <wd-img :src="good.picUrl" width="100%" height="8rem" radius="0.5rem" />
              </view>
              <view class="cg-middle px-0.25rem">
                <view class="mb-0.25rem">
                  <text class="cg-title line-clamp-2 text-ellipsis text-0.85rem">{{ good.title }}</text>
                </view>
                <view class="mb-0.25rem">
                  <text class="mr-0.25rem text-red-500">
                    ￥<text class="font-bold text-1rem">{{ good.actualPrice }}</text>
                  </text>
                  <text class="mr-0.25rem text-gray-400 line-through text-0.65rem">
                    ￥{{ good.orgPrice }}
                  </text>
                </view>
                <view class="mb-0.25rem flex items-center justify-around">
                  <text class="px-0.2rem text-0.6rem text-red-500" style="border:1px solid #fa4126;border-radius:0.1rem;padding:0.1rem 0.2rem">
                    约返{{ good.maxProfit }}
                  </text>
                  <view class="flex items-center">
                    <wd-img src="/static/icon/aqbz.png" width="1.5rem" height="1.5rem" />
                    <text class="color-#B6B49F text-0.75rem">随时退，过期退</text>
                  </view>
                </view>
              </view>
              <view class="cg-bottom px-0.25rem flex justify-around items-center">
                <view class="cg-share w-1.6rem h-1.6rem flex justify-center items-center ml-0.5rem" @click="shareCopy(good)">
                  <wd-img src="/static/icon/share.png" width="1rem" height="1rem" />
                </view>
                <view class="cg-buy flex-1">
                  <wd-button size="small" @click="buyGoods(good)">购买</wd-button>
                </view>
              </view>
            </view>
            <!-- <view v-for="good in goods" v-if="goods.length > 0" :key="good.id" class="cat-item flex bg-#fff rounded-lg p-0.5rem">
              <view>
                <wd-img :src="good.picUrl" width="7rem" height="7rem" mr-0.5rem radius="0.5rem" />
              </view>
              <view w-100vw flex flex-col justify-around>
                <view line-clamp-2 text-ellipsis>
                  <wd-img :src="`/static/icon/${good.platformCode}.png`" class="gd-icon h-1rem w-1rem" />
                  {{ good.title }}
                </view>
                <view class="flex items-center mt-0.25rem">
                  <text class="mr-0.25rem text-red-500">
                    ￥<text class="font-bold text-1rem">{{ good.actualPrice }}</text>
                  </text>
                  <text class="mr-0.5rem text-gray-400 line-through text-0.65rem">
                    ￥{{ good.orgPrice }}
                  </text>
                  <text v-if="good.couponAmount > 0" class="mr-0.25rem bg-#fa4126 text-0.6rem text-red-500" style="color:#fff;border-radius:0.2rem;padding:0.2rem">
                    券 <text class="pl-0.2rem" style="border-left:1px dashed #fff">
                    {{ good.couponAmount }}
                  </text>
                  </text>
                  <text class="px-0.2rem text-0.6rem text-red-500" style="border:1px solid #fa4126;border-radius:0.1rem;padding:0.1rem 0.2rem">
                    约返{{ good.maxProfit }}
                  </text>
                </view>
                <view flex items-center mt-0.25rem>
                  <wd-img src="/static/img/dp.png" class="mr-0.15rem h-0.95rem w-0.95rem" />
                  <text class="text-gray-400">
                    {{ good.shopTitle }}
                  </text>
                </view>
                <view h-1.4rem class="buy-info mt-0.25rem">
                  <text class="buy-hot p-0.2rem text-#fff">
                    <wd-img src="/static/rank/huo.png" width="1rem" height="1rem" float-left mr-0.25rem />
                    2小时热卖{{ good.salesCount ?? 1000 }}件
                  </text>
                  <view class="buy-now">
                    <wd-img src="/static/rank/buy-now.png" />
                  </view>
                </view>
                <view class="mt-0.5rem">
                  <text class="text-gray-400">距离0.05km</text>
                </view>
              </view>
            </view> -->
          </view>
          <!-- loading -->
          <wd-loadmore :state="state" @reload="onReTry" />
          <!-- no data -->
          <wd-status-tip v-if="goodsListLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
        </view>
      </view>
    </view>
  </overlay-search-wraper>
  <!--定位授权弹窗提示-->
  <wd-popup v-model="show" :close-on-click-modal="false" closable lock-scroll :z-index="102" custom-style="border-radius: 0.5rem" @close="closeLocation">
    <view class="popu p-0.5rem w-75vw">
      <view class="mt-2rem flex justify-center">
        <view class="bg-amber h-4rem w-4rem overflow-hidden" style="border-radius: 50%">
          <wd-img :src="`/static/logo.png`" class="w-full h-full" />
        </view>
      </view>
      <view class="text-center mt-1rem">
        <text class="text-1.05rem">省赚客需要以下权限</text>
      </view>
      <view class="mt-1rem px-1rem flex justify-center">
        <text class="text-0.85rem text-#333">为保证您正常、安全的使用省赚客APP，我们需要向您申请如下权限：</text>
      </view>
      <view class="flex mt-1rem px-1rem">
        <view>
          <view class="bg-#FEE4ED flex justify-center items-center w-2rem h-2rem" style="border-radius: 50%">
            <wd-icon name="phone" color="#ED3226" size="20px" />
          </view>
        </view>
        <view class="pl-0.5rem text-0.85rem">
          <view>
            <text class="text-#333">获取定位权限</text>
          </view>
          <view class="mt-0.5rem">
            <text class="text-#ACACAC">为了您能正常访问本地团购商品列表，我们需要申请定位权限，如您拒绝授权不影响您使用 APP 的其他功能。</text>
          </view>
        </view>
      </view>
      <view class="mt-1rem mb-0.5rem px-1rem">
        <wd-button block size="large" @click="openLocation">开启权限</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
@import '@/plugins-status-bar.scss';

:deep(.home) {
  width: 100vw;
  box-sizing: border-box;

  //.nav-bar {
  //  position: fixed;
  //  top: 0;
  //  left: 0;
  //  width: 100%;
  //  z-index: 100;
  //  background-color: #fff;
  //  padding-top: calc(var(--status-bar-height) + 0.5rem);
  //}

  .wd-loadmore {
    margin-top: -0.5rem;
  }

  .wd-status-tip {
    height: 20rem;
  }

  .linkFontSize {
    font-size: 0.8rem;
    .wd-icon {
      font-weight: bold;
    }
  }

  .home-header {
    position: fixed;
    //top: calc(var(--status-bar-height) + 3rem);
    top: 0;
    left: 0;
    width: 100%;
    z-index: 101;
    background-color: #F72929;
    padding: calc(var(--status-bar-height) + 1rem) 0 0.5rem;

    .search {
      padding: 0 0.5rem;

      .custom-search {
        padding: 0;

        .wd-search__block {
          background: transparent;
        }

        .wd-search__input {
          overflow: hidden;
          border-radius: 10px;
          padding-left: 20px;
        }
      }
    }
  }

  .home-top-banners {
    //padding-top: calc(var(--status-bar-height) + 6rem);
    padding-top: calc(var(--status-bar-height) + 3.5rem);
    background: linear-gradient(
        to bottom,
        #F72929,          /* 起始颜色 */
        #F72929 20%,      /* 在 20% 的位置颜色仍然是 #F72929 */
        #edf2f5 80%,      /* 在 80% 的位置颜色开始变为 #edf2f5 */
        #edf2f5           /* 结束颜色 */
    );
  }

  .cateb {
    position: sticky;
    //top: calc(var(--status-bar-height) + 6.4rem);
    top: calc(var(--status-bar-height) + 4rem);
    //white-space: nowrap;
    //overflow-x: scroll;
    z-index: 101;
    background-color: #edf2f5;
    .tabs {
      .wd-tabs {
        .wd-tabs__nav {
          .wd-tabs__nav--wrap {
            .wd-tabs__nav-container {
              .wd-tabs__nav-item {
                font-size: 16px;
              }
              .is-active {
                color: red;
              }
            }
          }
        }
      }
    }
    //.wd-button {
    //  padding: 0 1rem;
    //  margin-right: 0.5rem;
    //  border-radius: 0.25rem;
    //  height: 25px;
    //  color: rgba(0, 0, 0, 0.85);
    //  background-color: transparent;
    //  border-color: rgb(209 213 219);
    //}
    //.wd-button:last-child {
    //  margin-right: 0;
    //}
    //.wd-button.active {
    //  color: #fa4126;
    //  border-color: #fa4126;
    //}
  }

  .home-main {
    .categoods {
      .categoods-item {
        flex: 1 1 48%;
        margin-left: 2%;
        max-width: 49%;
        .cg-top {
          position: relative;
          overflow: hidden;
          .shopt {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            padding: 0.25rem;
            border-radius: 0.2rem;
          }
        }
        .cg-bottom {
          .cg-share {
            border-radius: 50%;
            overflow: hidden;
            background-color: #FA4126;
          }
          .cg-buy {
            padding-left: 1rem;
            .wd-button {
              width: 100%;
            }
          }
        }
      }
      .categoods-item:nth-child(odd) {
        margin-left: 0;
      }
    }
    // .catgoods {
    //   .cat-item {
    //     font-size: 0.75rem;
    //     margin-top: 0.5rem;
    //     .gd-icon {
    //       vertical-align: middle;
    //     }
    //     .buy-info {
    //       display: flex;
    //       background-color:#fd386d;
    //       border-radius: 0.25rem;
    //       .buy-hot {
    //         flex: 2;
    //       }
    //       .buy-now {
    //         flex: 1;
    //         width: 5rem;
    //         height: 1.5rem;
    //         margin-top: -0.1rem;
    //         .wd-img {
    //           width: 100%;
    //           height: 100%;
    //         }
    //       }
    //     }
    //   }
    // }
  }

  ::-webkit-scrollbar {
    background: transparent;
    /* 将滚动条背景色设为透明 */
  }

  .home-jingan {
    :deep(.jingan-card) {
      .wd-card__content {
        text-align: center;
      }
    }
  }

  .border-rd-logo {
    .wd-img__image {
      border-radius: 50%;
    }
  }

  .goods-nav-tab-fix .wd-tabs__nav {
    position: sticky;
    top: calc(var(--status-bar-height) + 4rem);
    z-index: 100;
  }
}

:deep(.wd-search__search-left-icon) {
  color: #ef0041;
  left: unset;
  right: 16px;
}
</style>

<route type="page" lang="json">
{
"style": {
"navigationBarTextStyle": "white"
}
}
</route>
