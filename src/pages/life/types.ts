interface Tab {
  /** 渠道：MTCPS,DYCPS,ELMCPS */
  channel: string
  /** 1 到家及其他业务类型，2 到店业务类型 */
  platform: number
  /** 当字段platform为1，到家及其他业务类型；当字段platform为2，到店业务类型时：1、美食，2、休闲生活，3、酒店，4、门票 不填则默认1 */
  bizLine: number
  /** 名称 */
  name: string
}

export const tabs: Tab[] = [
  {
    channel: 'MTCPS',
    platform: 1,
    bizLine: 1,
    name: '美团好券',
  },
  // {
  //   channel: 'ELMCPS',
  //   platform: 2,
  //   bizLine: 1,
  //   name: '饿了么好券',
  // },
  // {
  //   channel: 'ELMCPS',
  //   platform: 1,
  //   bizLine: 1,
  //   name: '囤券券',
  // },
  {
    channel: 'MTCPS',
    platform: 2,
    bizLine: 1,
    name: '美团团购',
  },
  {
    channel: 'DYCPS',
    platform: 1,
    bizLine: 1,
    name: '抖音团购',
  },
  {
    channel: 'MTCPS',
    platform: 2,
    bizLine: 2,
    name: '生活服务',
  },
  {
    channel: 'MTCPS',
    platform: 2,
    bizLine: 3,
    name: '酒店民宿',
  },
  {
    channel: 'MTCPS',
    platform: 2,
    bizLine: 4,
    name: '景区门票',
  },
  {
    channel: 'MTCPS',
    platform: 1,
    bizLine: 5,
    name: '美团医药',
  },
]

export const cpsInfo: object = {
  MTCPS: {
    name: '美团',
    pname: 'com.sankuai.meituan',
    action: 'meituan://',
  },
  ELMCPS: {
    name: '饿了么',
    pname: 'me.ele',
    action: 'eleme://',
  },
  DYCPS: {
    name: '抖音',
    pname: 'com.ss.android.ugc.aweme',
    action: 'Aweme://',
  },
  WEIXIN: {
    name: '微信',
    pname: 'com.tencent.mm',
    action: 'weixin://',
  },
}

export async function checkAppInstalled(channel: string) {
  const info = cpsInfo[channel]
  if (!info)
    return false

  if (plus.os.name === 'Android') {
    if (plus.runtime.isApplicationExist({ pname: info.pname })) {
      console.log(`[Android] 已安装${info.name}APP`)
      return true
    }
    else {
      console.log(`[Android] 未安装${info.name}APP`)
      return false
    }
  }

  if (plus.os.name === 'iOS') {
    if (plus.runtime.isApplicationExist({ action: info.action })) {
      console.log(`[IOS] 已安装${info.name}APP`)
      return true
    }
    else {
      console.log(`[IOS] 未安装${info.name}APP`)
      return false
    }
  }

  return false
}
