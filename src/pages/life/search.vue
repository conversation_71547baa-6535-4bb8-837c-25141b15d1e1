<script lang="ts" setup>
/* eslint-disable */
import { reactive, ref } from 'vue'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { chainGroupBuy, queryGroupGoodsList } from '@/services'
import { openOthers } from '@/common/jump'
import { checkAppInstalled, tabs } from './types'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

// 默认经纬度是杭州武林广场
const props = defineProps({
  longitude: {
    type: String,
    default: '120.169',
  },
  latitude: {
    type: String,
    default: '30.276',
  },
})

const state = ref<LoadMoreState>('loading')

const goods = ref<any[]>([] as any[])
const goodsListLoadStatus = ref<number>(0)
const searchValue = ref<string>('')

// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

const goodListPagination = reactive({
  index: 0,
  num: 20,
})

onMounted(async () => {
  await init()
})

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    loadGoodsList() // 调用加载更多的函数
  }
})

// 默认经纬度是杭州武林广场
const location = reactive({
  longitude: props.longitude,
  latitude: props.latitude,
})

async function init() {
  uni.getLocation({
    type: 'wgs84',
    isHighAccuracy: true,
    success: function(res) {
      console.log('用户定位信息：', res);
      location.longitude = res.longitude
      location.latitude = res.latitude
    },
    fail: function(err) {
      console.log('获取定位失败：', err)
    },
    complete: function() {
      loadGoodsList()
    }
  })
}

function onReTry() {
  loadGoodsList()
}

async function loadGoodsList(fresh = false) {
  if (goodsListLoadStatus.value === 1) {
    console.log('[life goods] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const Limit = goodListPagination.num
  let PageNo = goodListPagination.index + 1
  if (fresh)
    PageNo = 1

  try {
    const data = await queryGroupGoodsList({
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
      longitude: location.longitude,
      latitude: location.latitude,
      channel: tabs[0].channel,
      platform: tabs[0].platform,
      bizLine: tabs[0].bizLine,
      keyword: searchValue.value,
    })
    goodsListLoadStatus.value = 0

    if (!data || !data.list || data.list.length <= 0) {
      console.log('[life goods] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        goods.value = []
      }
      if (goods.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (data.list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      goods.value = data.list || []
    else if (data.list && data.list.length > 0)
      goods.value = goods.value.concat(data.list) // 追加新加载的商品

    goodListPagination.index = PageNo // 更新当前页码
    goodListPagination.num = Limit
  }
  catch (err) {
    console.error('[life goods] 本地生活加载失败', err)
    goods.value = []
    goodsListLoadStatus.value = 3
  }
}

async function clickSearch() {
  state.value = 'loading'
  isDataEmpty.value = 0
  noMoreData.value = 0
  await uni.showLoading({
    mask: true,
  })
  await loadGoodsList(true)
  uni.hideLoading()
}

function handleClickLeft() {
  uni.navigateBack()
}

async function shareCopy(goods: object) {
  await uni.showLoading({
    mask: true,
  })
  const data = await chainGroupBuy({
    channel: goods.platformCode,
    linkType: 2,
    platform: tabs[0].platform,
    bizLine: tabs[0].bizLine,
    skuViewId: goods.id,
  })
  uni.hideLoading()

  if (!data)
    return false

  let templateText = `${goods?.title}
【在售价】${goods?.orgPrice}元
【到手价】${goods?.actualPrice}元
-----------------
`
  if (data.platformCode === 'DYGROUP' || data.platformCode === 'DY') {
    templateText += `${data.goodsUrl}\r\n${data.clickUrl}`
  } else {
    templateText += `${data.clickUrl}`
  }

  await setClipboardData(templateText, true)
}

async function buyGoods(goods: object) {
  // 优先打开APP，其次是微信小程序，再不行就打开H5
  let linkType = 2

  // #ifdef APP-PLUS
  const existCps = await checkAppInstalled(tabs[0].channel)
  if (existCps) {
    linkType = 3
  } else {
    const existWx = await checkAppInstalled('WEIXIN')
    if (existWx) {
      linkType = 4
    }
  }
  // #endif

  await uni.showLoading({
    mask: true,
  })
  const data = await chainGroupBuy({
    channel: goods.platformCode,
    linkType: linkType,
    platform: tabs[0].platform,
    bizLine: tabs[0].bizLine,
    skuViewId: goods.id,
  })
  uni.hideLoading()

  // 如果非APP环境下，这个值只可能是1或2，否则会有3和4两种可能性
  if (linkType === 1 || linkType === 2) {
    let url = data.shortClickUrl || data.clickUrl || data.goodsUrl
    uni.navigateTo({
      url: `/pages/iframe?url=${encodeURIComponent(url)}&name=${tabs[0].name}`,
    })
  } else {
    await openOthers(data, data.platformCode)
  }
}
</script>

<template>
  <view class="home min-h-screen bg-#edf2f5">
    <view class="nav-bar">
      <wd-navbar title="本地生活" left-arrow :bordered="false" @click-left="handleClickLeft" />
    </view>
    <!--开屏页-->
    <view class="contents">
      <!-- 顶部区域 -->
      <view class="home-header">
        <view class="search bg-transparent">
          <!-- 搜索框 -->
          <wd-search
            v-model="searchValue" custom-class="custom-search bg-#fff h-10 border-rd-25px"
            placeholder="输入关键字，如：店名、品牌名" cancel-txt="搜索" placeholder-left @search="clickSearch" @clear="clickSearch" @cancel="clickSearch"
          />
        </view>
      </view>
      <!-- 商品推荐 -->
      <view class="home-main px-0.5rem">
        <view class="categoods flex flex-wrap">
          <view class="categoods-item flex flex-col justify-between bg-#fff rounded-lg mb-0.5rem pb-0.5rem" v-for="good in goods" v-if="goods.length > 0" :key="good.id">
            <view class="cg-top w-full">
              <text v-if="good.shopTitle !== ''" class="shopt bg-#FA4126 text-#fff text-0.75rem">{{ good.shopTitle }}</text>
              <wd-img :src="good.picUrl" width="100%" height="8rem" radius="0.5rem" />
            </view>
            <view class="cg-middle px-0.25rem">
              <view class="mb-0.25rem">
                <text class="cg-title line-clamp-2 text-ellipsis text-0.85rem">{{ good.title }}</text>
              </view>
              <view class="mb-0.25rem">
                <text class="mr-0.25rem text-red-500">
                  ￥<text class="font-bold text-1rem">{{ good.actualPrice }}</text>
                </text>
                <text class="mr-0.25rem text-gray-400 line-through text-0.65rem">
                  ￥{{ good.orgPrice }}
                </text>
              </view>
              <view class="mb-0.25rem flex items-center justify-around">
                <text class="px-0.2rem text-0.6rem text-red-500" style="border:1px solid #fa4126;border-radius:0.1rem;padding:0.1rem 0.2rem">
                  约返{{ good.maxProfit }}
                </text>
                <view class="flex items-center">
                  <wd-img src="/static/icon/aqbz.png" width="1.5rem" height="1.5rem" />
                  <text class="color-#B6B49F text-0.75rem">随时退，过期退</text>
                </view>
              </view>
            </view>
            <view class="cg-bottom px-0.25rem flex justify-around items-center">
              <view class="cg-share w-1.6rem h-1.6rem flex justify-center items-center ml-0.5rem">
                <wd-img src="/static/icon/share.png" width="1rem" height="1rem" @click="shareCopy(good)" />
              </view>
              <view class="cg-buy flex-1">
                <wd-button size="small" @click="buyGoods(good)">购买</wd-button>
              </view>
            </view>
          </view>
        </view>
        <!-- loading -->
        <wd-loadmore :state="state" @reload="onReTry" />
        <!-- no data -->
        <wd-status-tip v-if="goodsListLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import '@/plugins-status-bar.scss';

:deep(.home) {
  width: 100vw;
  box-sizing: border-box;

  .nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    background-color: #fff;
    padding-top: calc(var(--status-bar-height) + 0.5rem);
  }

  .wd-loadmore {
    margin-top: -0.5rem;
  }

  .wd-status-tip {
    height: 20rem;
  }

  .linkFontSize {
    font-size: 0.8rem;
    .wd-icon {
      font-weight: bold;
    }
  }

  .home-header {
    position: fixed;
    top: calc(var(--status-bar-height) + 3rem);
    left: 0;
    width: 100%;
    z-index: 101;
    background-color: #edf2f5;
    padding: 0.5rem 0;

    .search {
      padding: 0 0.5rem;

      .custom-search {
        padding: 0;

        .wd-search__block {
          background: transparent;
        }

        .wd-search__input {
          overflow: hidden;
          border-radius: 10px;
          padding-left: 40px;
        }
      }
    }
  }

  .home-top-banners {
    padding-top: calc(var(--status-bar-height) + 6rem);
  }

  .cateb {
    position: sticky;
    top: calc(var(--status-bar-height) + 6.4rem);
    white-space: nowrap;
    overflow-x: scroll;
    z-index: 101;
    background-color: #edf2f5;
    .wd-button {
      padding: 0 1rem;
      margin-right: 0.5rem;
      border-radius: 0.25rem;
      height: 25px;
      color: rgba(0, 0, 0, 0.85);
      background-color: transparent;
      border-color: rgb(209 213 219);
    }
    .wd-button:last-child {
      margin-right: 0;
    }
    .wd-button.active {
      color: #fa4126;
      border-color: #fa4126;
    }
  }

  .home-main {
    padding-top: calc(var(--status-bar-height) + 6.5rem);
    .categoods {
      .categoods-item {
        flex: 1 1 48%;
        margin-left: 2%;
        max-width: 49%;
        .cg-top {
          position: relative;
          overflow: hidden;
          .shopt {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            padding: 0.25rem;
            border-radius: 0.2rem;
          }
        }
        .cg-bottom {
          .cg-share {
            border-radius: 50%;
            overflow: hidden;
            background-color: #FA4126;
          }
          .cg-buy {
            padding-left: 1rem;
            .wd-button {
              width: 100%;
            }
          }
        }
      }
      .categoods-item:nth-child(odd) {
        margin-left: 0;
      }
    }
  }

  ::-webkit-scrollbar {
    background: transparent;
    /* 将滚动条背景色设为透明 */
  }

  .home-jingan {
    :deep(.jingan-card) {
      .wd-card__content {
        text-align: center;
      }
    }
  }

  .border-rd-logo {
    .wd-img__image {
      border-radius: 50%;
    }
  }

  .goods-nav-tab-fix .wd-tabs__nav {
    position: sticky;
    top: calc(var(--status-bar-height) + 4rem);
    z-index: 100;
  }
}
</style>

<route type="page" lang="json">
{
  "style": {
    "navigationBarTextStyle": "black"
  }
}
</route>
