<script lang="ts" setup>
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { chainActivity, queryActivityList } from '@/services'
import { openOthers } from '@/common/jump'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

const activityList = ref<appActivityQueryActivityList.Response['data']>([])

const listLoadStatus = ref<number>(0)
const state = ref<LoadMoreState>('loading')
// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)

onMounted(async () => {
  setTimeout(async () => {
    await loadActivity()
  }, 100)
})

const shareActivity = throttle(async (params: appActivityQueryActivityList.ResponseDataItem) => {
  await uni.showLoading({
    mask: true,
  })
  const data = await chainActivity({
    id: params.id,
  })
  uni.hideLoading()
  console.log(params, data)
  if (data) {
    if (data.platformCode === 'TB') {
      await setClipboardData(data.goodsUrl, true)
    }
    else {
      await setClipboardData(data.shortClickUrl || data.clickUrl, true)
    }
  }
}, 1000)

const enterActivity = throttle(async (params: appActivityQueryActivityList.ResponseDataItem) => {
  const data = await chainActivity({
    id: params.id,
  })
  console.log(params, data)
  // if (data && data.clickUrl) {
  //   uni.navigateTo({
  //     url: `/pages/iframe?url=${encodeURIComponent(data.clickUrl)}&name=${params.title}`,
  //   })
  // }
  await openOthers(data, data.platformCode, params.title)
}, 1000)

async function loadActivity() {
  listLoadStatus.value = 1
  try {
    const data = await queryActivityList({
      activeid: 'dstop',
    })
    listLoadStatus.value = 0
    if (data) {
      activityList.value = data
    }
    else {
      isDataEmpty.value = 1
    }
  }
  catch (e) {
    console.error('会场榜加载失败', e)
    listLoadStatus.value = 0
    isDataEmpty.value = 0
  }
}
</script>

<template>
  <view class="rank-activity bg-#edf2f5 px-0.5rem">
    <view v-for="item in activityList" :key="item.id">
      <activity-card :data="item">
        <template #default>
          <wd-button plain size="small" @click="shareActivity(item)">
            分享拿收益
          </wd-button>
          <wd-button size="small" @click="enterActivity(item)">
            立即进会场
          </wd-button>
        </template>
      </activity-card>
    </view>
    <!-- loading -->
    <wd-loadmore v-if="listLoadStatus == 1" :state="state" @reload="loadActivity" />
    <!-- no data -->
    <wd-status-tip v-if="listLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
  </view>
</template>

<style lang="scss" scoped>
:deep(.rank-activity) {
  padding-top: calc(var(--status-bar-height) + 6.7rem);
  // #ifdef H5
  padding-top: calc(var(--status-bar-height) + 5.8rem);
  // #endif
}
</style>

<route lang="json5" type="page">
{}
</route>
