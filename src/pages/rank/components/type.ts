export interface tabsprops {
  index: number
  name: string
  key: string
}

export interface rankItem {
  name: string
  key: string
  children?: rankItem[]
  queryValue?: {
    name: string
    key: string
    showSub?: boolean
  }[]
  subQuery?: {
    name: string
    key: string
  }[]
}
// 榜单类型
export const rankItems: rankItem[] = [
  {
    name: '单品榜',
    key: 'single',
    // 平台id  1 淘宝 2 京东 3拼多多 4 美团  5唯品会  6抖音  7快手  8考拉  9苏宁 10CPS活动 11滴滴  12饿了么
    children: [
      {
        name: '淘 宝',
        key: 'TB',
        queryValue: [
          {
            name: '高佣榜',
            key: '5',
            showSub: true,
          },
          {
            name: '定向榜',
            key: '7',
            showSub: true,
          },
          {
            name: '实时榜',
            key: '6',
          },
          {
            name: '母婴榜',
            key: '1',
          },
          {
            name: '热销榜',
            key: '11',
          },
          {
            name: '今日榜',
            key: '12',
          },
        ],
        // subQuery=（0全部，1女装，2男装，3内衣，4美妆，5配饰，6鞋品，7箱包，8儿童，9母婴，10居家，11美食，12数码，13家电，14其他，15车品，16文体，17宠物）
        subQuery: [
          {
            name: '全部',
            key: '0',
          },
          {
            name: '女装',
            key: '1',
          },
          {
            name: '男装',
            key: '2',
          },
          {
            name: '内衣',
            key: '3',
          },
          {
            name: '美妆',
            key: '4',
          },
          {
            name: '配饰',
            key: '5',
          },
          {
            name: '鞋品',
            key: '6',
          },
          {
            name: '箱包',
            key: '7',
          },
          {
            name: '儿童',
            key: '8',
          },
          {
            name: '母婴',
            key: '9',
          },
          {
            name: '居家',
            key: '10',
          },
          {
            name: '美食',
            key: '11',
          },
          {
            name: '数码',
            key: '12',
          },
          {
            name: '家电',
            key: '13',
          },
          {
            name: '其他',
            key: '14',
          },
          {
            name: '车品',
            key: '15',
          },
          {
            name: '文体',
            key: '16',
          },
          {
            name: '宠物',
            key: '17',
          },
        ],
      },
      {
        name: '京 东',
        key: 'JD',
        queryValue: [
          {
            name: '实时热销榜',
            key: '1',
          },
          {
            name: '30天热销榜',
            key: '2',
          },
          {
            name: '30天收益榜',
            key: '3',
          },
          {
            name: '9块9包邮榜',
            key: '4',
          },
        ],
      },
      {
        name: '抖 音',
        key: 'DY',
        queryValue: [
          {
            name: '今日爆单榜',
            key: '0',
          },
          {
            name: '热销商品榜',
            key: '1',
          },
          {
            name: '精选低价榜',
            key: '2',
          },
        ],
      },
      {
        name: '唯品会',
        key: 'WPH',
        queryValue: [
          {
            name: '高佣热推榜',
            key: '0',
          },
          {
            name: '实时热销榜',
            key: '1',
          },
        ],
      },
      {
        name: '拼多多',
        key: 'PDD',
        queryValue: [
          {
            name: '今日销量榜',
            key: '1',
          },
          {
            name: '实时热销榜',
            key: '5',
          },
          {
            name: '实时收益榜 ',
            key: '6',
          },
          {
            name: '高佣榜',
            key: '24',
          },
        ],
      },
    ],
  },
  {
    name: '会场榜',
    key: 'venue',
  },
]
