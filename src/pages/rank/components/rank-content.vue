<script lang="ts" setup>
import { onReachBottom } from '@dcloudio/uni-app'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import rankActivity from './rank-activity.vue'
import { useRankStore } from '@/store/rank'
import { queryGoodsList } from '@/services'
import { useGoodsStore } from '@/store/goods'

const receivedMessage = ref('TB')
// 二级榜单查询参数
const queryValue = ref<any>([])
// 三级榜单查询参数
const ranksubQuery = ref<any>([])
const ranksubQueryTab = ref<any>('0')
const queryValueTab = ref<string>('0')
const { setGoods } = useGoodsStore()
const { rankType, rankTabs } = useRankStore()
const stateLoad = ref<LoadMoreState>('loading')
const goodsListLoadStatus = ref<number>(0)
const goods = ref<any[]>([])
const goodListPagination = reactive({
  index: 0,
  num: 20,
})
const privateData = reactive({
  tabIndex: 0,
})

// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

// 第四层分类查询是否为空
const isSubQueryEmpty = ref<boolean>(true)

// 第三层榜单入口点击事件
function queryChange(e: { index: string | undefined }) {
  console.log('--------------------------------------------------------------queryChange', e)
  resetQueryParams()
  isSubQueryEmpty.value = !queryValue.value[e.index].showSub
  loadGoodsList(true, e.index, ranksubQueryTab.value)
}
// 第四层分类入口点击事件
function rankChange(index: number) {
  console.log('--------------------------------------------------------------rankChange', index)
  resetQueryParams()
  ranksubQueryTab.value = index
  loadGoodsList(true, queryValueTab.value, index)
}

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', rankType.value, goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (rankType.value === 'single' && goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    loadGoodsList()
  }
})

// 挂载时也触发了一次
// onLoad(() => {
//   loadGoodsList()
// })

function onReTry() {
  console.log('--------------------------------------------------------------onReTry')
  loadGoodsList()
}

async function loadGoodsList(fresh = false, query = '', sub = '') {
  if (goodsListLoadStatus.value === 1) {
    console.log('[rank goods] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const Limit = goodListPagination.num
  let PageNo = privateData.tabIndex * Limit + goodListPagination.index + 1
  if (fresh)
    PageNo = 1

  const platformCode = receivedMessage.value.replace(
    'all',
    '',
  ) as appGoodsList.Params['platformCode']

  console.log('[query params] -> query', query)
  console.log('[query params] -> sub', sub)

  console.log('[global datas] -> queryValue', queryValue.value)
  console.log('[global datas] -> ranksubQuery', ranksubQuery.value)

  try {
    const data = await queryGoodsList({
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
      platformCode,
      queryValue: queryValue.value[query || 0].key,
      subQuery: ranksubQuery.value.length === 0 ? '' : (isSubQueryEmpty.value ? '' : ranksubQuery.value[sub || 0].key),
      queryType: 'list',
    })
    goodsListLoadStatus.value = 0

    if (!data || !data.list || data.list.length <= 0) {
      console.log('[rank goods] 没有更多啦~~~')
      stateLoad.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        goods.value = []
      }
      if (goods.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (data.list.length < Limit) {
    //   stateLoad.value = 'finished'
    //   noMoreData.value = 1
    // }

    const list = data?.list ?? []
    if (fresh)
      goods.value = list ?? []
    else goods.value = goods.value.concat(list)

    goodListPagination.index = PageNo
    goodListPagination.num = Limit
    // nextTick(async () => {
    // if (fresh)
    // await getElementScollTop('.home-main-tabs-content')
    // })
  }
  catch (err) {
    console.error('[rank goods] 榜单加载失败', err)
    goods.value = []
    goodsListLoadStatus.value = 3
  }
}

function goodListClickHandle(goodsDetails: appGoodsGoodsDetail.ResponseData) {
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

function resetQueryParams() {
  goodListPagination.index = 0
  goodListPagination.num = 20
  goods.value = []
  ranksubQueryTab.value = ''
  stateLoad.value = 'loading'
  isDataEmpty.value = 0
  noMoreData.value = 0
  isSubQueryEmpty.value = !queryValue.value[0].showSub
}

onMounted(() => {
  console.log('--------------------------------------------------------------onMounted')
  console.log('rankType', rankType.value)

  // 监听来自header的事件
  // todo : 待rankItems 补充完毕后 类型应该修改为 rankItem
  const handleMessage = (item: any) => {
    try {
      console.log('%c [ item ]-120', 'font-size:13px; background:pink; color:#bf2c9f;', item)
      console.log('=========================>queryValueTab', item?.queryValue?.[0].key ?? 0)
      console.log('=========================>ranksubQueryTab', item?.subQuery?.[0].key ?? '')
      receivedMessage.value = item.key
      queryValue.value = item.queryValue ?? []
      ranksubQuery.value = item.subQuery ?? []
      queryValueTab.value = item?.queryValue?.[0].key ?? 0
      ranksubQueryTab.value = item?.subQuery?.[0].key ?? ''
      console.log('--------------------------------------------------------------handleMessage')
      resetQueryParams()
      loadGoodsList(true)
    }
    catch (err) {
      console.log('%c [ err ]-128', 'font-size:13px; background:pink; color:#bf2c9f;', err)
    }
  }
  uni.$on('receiveMessageFromRank', handleMessage)

  // 记得在组件卸载前移除事件监听
  // onUnmounted(() => {
  //   uni.$off('receiveMessageFromRank', handleMessage);
  // });
})
</script>

<template>
  <view class="rank">
    <!-- 单品榜 -->
    <view v-if="rankType === 'single'" class="singlep" :class="{ singlep2: isSubQueryEmpty }">
      <!-- 导航区 -->
      <view class="rand-two-dash">
        <!-- 三层：具体榜单入口（如：高佣榜、实时榜、今日榜） -->
        <wd-tabs v-if="queryValue.length > 0" v-model="queryValueTab" @click="queryChange">
          <block v-for="(item, index) in queryValue" :key="index">
            <wd-tab :title="item.name" />
          </block>
        </wd-tabs>
        <!-- 四层：榜单分类（如：女装、男装） -->
        <view v-if="ranksubQuery.length > 0 && queryValue[queryValueTab].showSub" class="category-list">
          <wd-button
            v-for="(item, index) in ranksubQuery" :key="index" hairline plain size="small"
            :class="{ 'wd-active': ranksubQueryTab == index }" @click="rankChange(index)"
          >
            {{ item.name }}
          </wd-button>
        </view>
      </view>
      <!-- 内容区 -->
      <view class="cont-list-item">
        <view
          v-for="(good, index) in goods" v-if="goods.length > 0" :key="good.id" class="list-item-details rounded-lg"
          mb-2 flex p-2 @click="goodListClickHandle(good)"
        >
          <view v-if="index <= 3" class="absolute z-1">
            <wd-img :src="`/static/rank/top${index + 1}.png`" width="3rem" height="3rem" />
          </view>
          <view flex-0.4>
            <wd-img
              :src="good.picUrl" width="7rem" height="7rem" mr-0.5rem radius="0.5rem"
              :class="{ 'top-goods': index <= 3 }"
            />
          </view>
          <view flex-0.4 w-100vw flex flex-col justify-around md:mx-1rem>
            <view line-clamp-2 text-ellipsis>
              <wd-img
                :src="good.platformCode === 'TB' ? (good.goodsType === 0 ? '/static/platform/TB.png' : '/static/platform/TM.png') : `/static/platform/${good.platformCode}.png`"
                class="gd-icon h-1rem w-1rem"
              />
              {{ good.title }}
            </view>
            <view class="flex items-center">
              <text class="mr-0.25rem text-1rem text-red-500 font-bold">
                ￥{{ good.actualPrice }}
              </text>
              <text class="mr-0.5rem text-0.6rem text-gray-400 line-through">
                ￥{{ good.orgPrice }}
              </text>
              <text
                v-if="good.couponAmount > 0" class="mr-0.25rem bg-#fa4126 text-0.6rem text-red-500"
                style="color:#fff;border-radius:0.2rem;padding:0.2rem"
              >
                券 <text class="pl-0.2rem" style="border-left:1px dashed #fff">
                  {{ good.couponAmount }}
                </text>
              </text>
              <text
                class="px-0.2rem text-0.6rem text-red-500"
                style="border:1px solid #fa4126;border-radius:0.1rem;padding:0.1rem 0.2rem"
              >
                约返{{ good.maxProfit }}
              </text>
            </view>
            <view flex items-center>
              <wd-img src="/static/img/dp.png" class="mr-0.15rem h-0.95rem w-0.95rem" />
              <text text-gray-400>
                {{ good.shopTitle }}
              </text>
            </view>
            <view h-1.4rem md:hidden class="buy-info">
              <text class="buy-hot p-0.2rem text-#fff">
                <wd-img src="/static/rank/huo.png" width="1rem" height="1rem" float-left mr-0.25rem />
                2小时热卖{{ good.salesCount ?? 1000 }}件
              </text>
              <view class="buy-now">
                <wd-img src="/static/rank/buy-now.png" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- loading -->
      <wd-loadmore v-if="rankType === 'single'" :state="stateLoad" @reload="onReTry" />
      <!-- no data -->
      <wd-status-tip
        v-if="rankType === 'single' && goodsListLoadStatus == 0 && isDataEmpty === 1"
        image="/static/search-nodata.png" tip="当前搜索无结果"
      />
    </view>
    <!-- 会场榜 -->
    <view v-if="rankType === 'venue'">
      <rank-activity />
    </view>
  </view>
</template>

<style lang="scss" scoped>
:deep(.rank) {
  .singlep {
    padding-top: calc(var(--status-bar-height) + 11.25rem);
    // #ifdef H5
    padding-top: calc(var(--status-bar-height) + 10.75rem);
    // #endif
    background-color: #edf2f5ff;

    //.wd-status-tip {
    //  background-color: #fff;
    //}

    .category-list {
      white-space: nowrap;
      overflow-x: scroll;
      padding: 0.5rem;
      background-color: #edf2f5ff;

      .wd-button {
        padding: 0 1rem;
        margin-right: 0.5rem;
        border-radius: 0.25rem;
        height: 25px;
        color: rgba(0, 0, 0, 0.85);
        background-color: transparent;
        border-color: rgb(209 213 219);
      }

      .wd-button.wd-active {
        color: #fa4126;
        border-color: #fa4126;
      }

      .wd-button:last-child {
        margin-right: 0;
      }
    }

    ::-webkit-scrollbar {
      background: transparent;
      /* 将滚动条背景色设为透明 */
    }

    .cont-list-item {
      padding: 0 0.5rem;
      font-size: 0.75rem;

      .list-item-details {
        position: relative;
        background: #fff;
      }

      .gd-icon {
        vertical-align: middle;
      }

      .top-goods {
        border-top-left-radius: 0.8rem !important;
      }
    }

    .right {
      -webkit-box-flex: 1;
      -ms-flex: 1;
      flex: 1;
    }

    .buy-info {
      display: flex;
      background-color: #fd386d;
      border-radius: 0.25rem;

      .buy-hot {
        flex: 2;
      }

      .buy-now {
        flex: 1;
        width: 5rem;
        height: 1.5rem;
        margin-top: -0.1rem;

        .wd-img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .rand-two-dash {
      position: fixed;
      top: calc(var(--status-bar-height) + 5.75rem);
      // #ifdef H5
      top: calc(var(--status-bar-height) + 5.25rem);
      // #endif
      left: 0;
      width: 100%;
      z-index: 100;
    }
  }

  .singlep2 {
    padding-top: calc(var(--status-bar-height) + 9rem) !important;
    // #ifdef H5
    padding-top: calc(var(--status-bar-height) + 8rem) !important;
    // #endif
  }
}
</style>

<route lang="json">
{
  "layout": "home"
}
</route>
