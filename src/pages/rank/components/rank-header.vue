<script lang="ts" setup>
import { type rankItem, rankItems, type tabsprops } from './type'
import { useRankStore } from '@/store/rank'

const { rankType, rankTabs, setRankType, setRankTabs } = useRankStore()
// 当前榜单类型
const rankTypes = ref(0)
const childType = ref<string>('TB')

// 一层榜单入口点击事件
function handleTabs(item: tabsprops) {
  // rankTypes.value = item.index
  setRankType(rankItems[item.index].key)
}
// 二层电商入口点击事件
function handleClick(item: rankItem) {
  console.log('+++++++++++++++++++++++++++++++++++++++++handleClick', item)
  childType.value = item.key
  setRankTabs(item.key)
  uni.$emit('receiveMessageFromRank', item)
}

function onInit() {
  rankTypes.value = rankType.value === 'single' ? 0 : 1
  setTimeout(() => {
    console.log('+++++++++++++++++++++++++++++++++++++++++onInit')
    const item = rankItems[0].children[0]
    uni.$emit('receiveMessageFromRank', item)
  }, 100)
}

onMounted(() => {
  onInit()
  console.log('rankType', rankType.value)
  console.log('rankTypes', rankTypes.value)
})
</script>

<template>
  <view class="rank-header">
    <!-- 一层：单品榜、会场榜 -->
    <wd-tabs v-model="rankTypes" class="rank-header-top color-#fff" @click="handleTabs">
      <block v-for="(item, index) in rankItems" :key="index">
        <wd-tab :title="`${item.name}`" />
      </block>
    </wd-tabs>
    <view v-if="rankType === 'single'" text-center text-size-12px text-color-white>
      <!-- 二层：电商入口 -->
      <wd-row>
        <wd-col
          v-for="(child, index) in rankItems[rankTypes].children" :key="index"
          :span="4" @click="handleClick(child)"
        >
          <view
            class="ml-1 h-8 w-100% border-r-4 rounded-sm text-center lh-8"
            :class="childType === child.key ? 'bg-#fd111a text-#fff border-active' : ' text-#fd111a bg-#ffffff'"
          >
            {{ child.name }}
          </view>
        </wd-col>
      </wd-row>
    </view>
    <view v-if="rankType === 'venue'" class="ml-5% w-90% text-center">
      <wd-button block type="error">
        榜单TOP分会场集合
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
:deep(.rank-header) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-size: cover;
  background-repeat: repeat;
  padding-top: calc(var(--status-bar-height) + 0.25rem);
  padding-bottom: 0.5rem;
  background-image: url('@/static/rank/header.png');

  .wd-tabs,
  .uni-scroll-view,
  .wd-tabs__nav,
  .wd-tabs__nav-item {
    color: #fff;
    background: none;
    font-size: 1rem
  }

  .wd-tabs__nav {
    margin-bottom: 0.25rem;
  }

  .wd-tabs__line{
     display: none;
  }

  .wd-col {
    margin-left: 0.6rem;
    //margin-left: 1.5rem;
  }

  .border-active {
    border: 0.5px solid #fff;
  }
}
</style>

<route lang="json">
{
  "layout": "default"
}
</route>
