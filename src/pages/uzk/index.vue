<script>
import { appInfo } from '@/common'

const Alibcsdk = uni.requireNativePlugin('UZK-Alibcsdk')
// console.log('Alibcsdk-----------------', Alibcsdk)
export default {
  data() {
    return {
      title: '',
      is_login: false,
    }
  },
  onLoad() {

  },
  methods: {
    init() {
      console.log('点击了')
      Alibcsdk.init(
        (result) => {
          console.log(JSON.stringify(result))
          this.showMsg(JSON.stringify(result))
        },
      )
    },
    login() { // 授权登录
      Alibcsdk.login(
        (result) => {
          console.log(JSON.stringify(result))
          this.showMsg(JSON.stringify(result))
        },
      )
    },
    logout() {
      Alibcsdk.logout(
        (result) => {
          console.log(JSON.stringify(result))
          this.showMsg(JSON.stringify(result))
        },
      )
    },
    openurl() {
      var url
					= 'https://uland.taobao.com/coupon/edetail?spm=a311n.9159044%2Fnew2019.19184768.1&e=FnN7yyC4dDENfLV8niU3R5TgU2jJNKOfNNtsjZw%2F%2FoJa3UC6%2BxeMcvutwNVTX3KSLspxGy3zBjap%2B7s0sowtFPv0%2FvoXyvpgzdTtiOUchMjLkU4k%2FaBIVWVfKa%2BhVnNDLVAuU8CMzMdDN4WEWA%2BAQJjB6TX2HR3QWPoiHfuLmMmYBFolcv5ukJVnRh%2ByXggR%2BnoDgTNhwmM%3D&app_pvid=59590_11.21.12.113_437_1567994479465&ptl=floorId%3A21801%3Bapp_pvid%3A59590_11.21.12.113_437_1567994479465%3Btpp_pvid%3A80bf32d7-c746-4719-b292-9d9082df9a4e&union_lens=lensId%3An%401567994479%4080bf32d7-c746-4719-b292-9d9082df9a4e_594360302497%401%3Brecoveryid%3A201_11.142.52.250_1238534_1567994478674%3Bprepvid%3A201_11.142.52.250_1238534_1567994478674&pid=mm_33231688_7050284_23466709'
      Alibcsdk.openurl({
        url,
        linkkey: 'taobao',
        adzoneid: '103062550066',
        pid: 'mm_131245267_59600050_103062550066',
        nativeFailedMode: 'download',
        appkey: '34494988',
        opentype: 'native',
      })
    },
    /**
			渠道备案可以使用通用的渠道备案链接（可在淘宝联盟后台获取）后面拼接上rtag参数，改参数为自定义参数，可以为用户ID，用户通过该页面备案成功后
			可通过taobao.tbk.sc.publisher.info.get( 淘宝客-公用-私域用户备案信息查询 )这个接口来获取对应rtag的渠道ID。页面地址：
			https://open.taobao.com/api.htm?docId=37989&docType=2&scopeId=14474
     */
    /* openqdurl() {
				var url = "https://mos.m.taobao.com/inviter/register?inviterCode=2W7YYZ&src=pub&app=common&rtag=123456";
				Alibcsdk.openwebviewurl({
					url: url,
				}, result => {
					uni.showToast({
						title: result.msg
					});
					console.log(JSON.stringify(result));
				})
			},
			*/
    /*
			申请渠道可以使用该方法打开H5授权登录页面，从服务端获取到access_token在调用渠道备案接口。
			H5授权登录换取access_token见淘宝联盟开发平台，页面地址：https://open.taobao.com/doc.htm?docId=118&docType=1&spm=a219a.7395903.0.0.16af3971ApSa3N
			https://oauth.m.taobao.com/authorize?response_type=code&client_id=你的appkey&redirect_uri=你的回调地址&state=自定义参数&view=web
			*/
    /* openh5url() {
				var url =
					"https://oauth.m.taobao.com/authorize?response_type=code&client_id=28002500&redirect_uri=http://www.baidu.com&state=1212&view=web";
				Alibcsdk.openwebviewurl({
					url: url,
				}, result => {
					uni.showToast({
						title: result.msg
					});
					console.log(JSON.stringify(result));
				})
			},
			*/
    opendetail() {
      Alibcsdk.opendetail({
        itemid: '563275619905',
        linkkey: 'taobao',
        adzoneid: '103062550066',
        pid: 'mm_131245267_59600050_103062550066',
        nativeFailedMode: 'download',
        appkey: '34494988',
        opentype: 'native',
      })
    },
    openshop() {
      Alibcsdk.openshop({
        shopid: 57630185,
        sellerid: '202224264',
        linkkey: 'taobao',
        adzoneid: '103062550066',
        pid: 'mm_131245267_59600050_103062550066',
        nativeFailedMode: 'download',
        appkey: '34494988',
        opentype: 'native',
      })
    },
    openmycart() {
      Alibcsdk.openmycart({
        linkkey: 'taobao',
        adzoneid: '103062550066',
        pid: 'mm_131245267_59600050_103062550066',
        nativeFailedMode: 'download',
        appkey: '34494988',
        opentype: 'native',
      })
    },
    getuserinfo() {
      Alibcsdk.getuserinfo(
        (result) => {
          this.showMsg(JSON.stringify(result))
          console.log(JSON.stringify(result))
        },
      )
    },
    /**
     * 用户授权后，前端拿到用户的code，用code作为参数传给后端，后端用code来换取用户的access_token,获取到用户的access_token后
     *再用用户的access_token去请求淘宝联盟渠道备案接口进行=备案。
     *接口名称：taobao.tbk.sc.publisher.info.save( 淘宝客-公用-私域用户备案 )
     *接口地址：https://open.taobao.com/api.htm?docId=37988&docType=2&scopeId=14474
     * 用code换取用户的access_token见淘宝联盟FAQ,地址：https://open.taobao.com/doc.htm?docId=118&docType=1&spm=a219a.7395903.0.0.7ef4397107L4Jv
     */

    qdBycode() {
      if (this.is_login) {
        this.publisher('code')
      }
      else {
        Alibcsdk.login((res) => {
          if (res.status) {
            this.is_login = true
            this.publisher('code')
          }
        })
      }
    },
    /**
     * 用户授权后，前端拿到用户access_token，用access_token作为参数传给后端，后端用access_token去请求
     *淘宝联盟渠道备案接口进行备案。
     *接口名称：taobao.tbk.sc.publisher.info.save( 淘宝客-公用-私域用户备案 )
     *接口地址：https://open.taobao.com/api.htm?docId=37988&docType=2&scopeId=14474
     */
    qdBytoken() {
      if (this.is_login) {
        this.publisher('token')
      }
      else {
        Alibcsdk.login((res) => {
          if (res.status) {
            this.is_login = true
            this.publisher('token')
          }
        })
      }
    },
    qdByhide() {
      if (this.is_login) {
        this.qdhide()
      }
      else {
        Alibcsdk.login((res) => {
          if (res.status) {
            this.is_login = true
            this.qdhide()
          }
        })
      }
    },
    qdhide() {
      Alibcsdk.qdByhide({
        url: `https://oauth.m.taobao.com/authorize?response_type=code&client_id=${appInfo.taobaoId}&redirect_uri=${encodeURI(`${appInfo.serverApi}tao/tbCallBack`)}&state=&view=wap`,
      }, (result) => {
        console.log(JSON.stringify(result))
        if (result.status) {
          uni.showModal({
            title: '数据获取成功',
            content: JSON.stringify(result),
            showCancel: false,
            cancelText: '取消',
            confirmText: '确认',
            success: (res) => {},
            fail: () => {},
            complete: () => {},
          })
          // var access_token = result.data.access_token;
          // 用获取到的access_token作为参数传给后端，换取渠道id
          // this.request(access_token);
        }
      })
    },
    publisher(type) {
      Alibcsdk.getpublisher({
        url: `https://oauth.m.taobao.com/authorize?response_type=code&client_id=${appInfo.taobaoId}&redirect_uri=${encodeURI(`${appInfo.serverApi}tao/tbCallBack`)}&state=&view=wap`,
      }, (result) => {
        console.log(JSON.stringify(result))
        if (result.status) {
          // if(type == "token"){
          // 	//用获取到的access_token作为参数传给后端，换取渠道id
          // 	//this.request(result.data.access_token);
          // 	return;
          // }
          uni.showModal({
            title: '数据获取成功',
            content: JSON.stringify(result),
            showCancel: false,
            cancelText: '取消',
            confirmText: '确认',
            success: (res) => {},
            fail: () => {},
            complete: () => {},
          })
        }
        else {
          uni.showToast({
            title: '用户关闭了页面',
          })
        }
      })
    },
    request(access_token) {
      uni.request({
        url: `https://www.012u.com/api/v4/tb/test_addqd?access_token=${access_token}`,
        method: 'GET',
        data: {},
        success: (res) => {
          console.log(JSON.stringify(res.data))
          uni.showModal({
            title: '数据获取成功',
            content: JSON.stringify(res.data.data),
            showCancel: false,
            cancelText: '取消',
            confirmText: '确认',
            success: (res) => {},
            fail: () => {},
            complete: () => {},
          })
        },
        fail: () => {},
        complete: () => {},
      })
    },
    getutdid() {
      console.log('getutdid')
      Alibcsdk.getutdid(
        (result) => {
          console.log('getutdid', result)
          this.showMsg(JSON.stringify(result))
        },
      )
    },
    getBaichuanVersion() {
      Alibcsdk.getBaichuanVersion((res) => {
        this.showMsg(JSON.stringify(res))
      })
    },
    showMsg(msg) {
      uni.showModal({
        title: '接口调用成功',
        content: msg,
        showCancel: false,
        cancelText: '',
        confirmText: '确定',
        success: (res) => {},
        fail: () => {},
        complete: () => {},
      })
    },
  },
}
</script>

<template>
  <view class="content">
    <button class="btu" @click="init">
      初始化
    </button>

    <button class="btu" @click="login">
      淘宝授权登录
    </button>

    <button class="btu" @click="getuserinfo">
      获取用户授权信息
    </button>

    <button class="btu" @click="logout">
      退出淘宝登录
    </button>

    <button class="btu" @click="openurl">
      打开优惠券
    </button>

    <!-- <button @click="openqdurl" class="btu">打开渠道备案</button>

		<button @click="openh5url" class="btu">H5授权</button> -->

    <button class="btu" @click="opendetail">
      打开商品详情
    </button>

    <button class="btu" @click="openshop">
      打开店铺
    </button>

    <button class="btu" @click="openmycart">
      打开我的购物车
    </button>

    <button class="btu" @click="qdBycode">
      渠道备案By code
    </button>

    <button class="btu" @click="qdBytoken">
      渠道备案By token（推荐）
    </button>

    <button class="btu" @click="qdByhide">
      渠道备案(静默式)
    </button>

    <button class="btu" @click="getutdid">
      获取UTDID
    </button>

    <button class="btu" @click="getBaichuanVersion">
      获取SDK版本信息
    </button>
    <text>{{ title }}</text>
  </view>
</template>

<style>
  .content {
    padding-top: calc(var(--status-bar-height) + 0.5rem);
  }
	.btu {
		margin-top: 10px;
		margin-left: 10px;
		margin-right: 10px;
	}
</style>
