<script lang="ts" setup>
import CommonFunctions from './components/common-functions.vue'
import EarningsInfo from './components/earnings-info.vue'
import SwiperInfo from './components/swiper-info.vue'
import UserProfile from './components/user-profile.vue'
import FreeShipping from './components/free-shipping.vue'
import { fetchUserCenter } from '@/services'
import { appInfo } from '@/common'
import { useAuthStore } from '@/store/user'
import MoneyInfo from '@/pages/personal/components/money-info.vue'

const { userToken, setUserInfo } = useAuthStore()
const userInfo = ref(null)

onMounted(async () => {
  await init()
})

async function init() {
  const info = await fetchUserCenter()
  // 默认用户
  if (userToken.value === appInfo.defaultTk) {
    userInfo.value = {
      fansNick: '请登录APP',
      inviteCode: '------',
      balance: '0.00',
      token: appInfo.defaultTk,
    }
  }
  else {
    userInfo.value = info
  }
  setUserInfo(info)
  // 是否隐藏团队粉丝信息
  // hideTeam({
  //   current_version: appInfo.version,
  // }).then((res) => {
  //   if (res.data && res.data === 'hide') {
  //     appInfo.hideTeam = true
  //   }
  //   console.log('hideTeam', appInfo.hideTeam)
  // })
}

function handleClick(item: {
  module: string
  link: string
  materialId?: string
  name: string
}) {
  const { module, link, materialId, name } = item
  let url = ''

  if (materialId) {
    // 远程图片太耗时，这个不会经常改动，直接放本地
    if (link === 'beginnerTutorial') {
      url = '/pages/home/<USER>'
    }
    else {
      url = `/pages/personal/material?materialId=${materialId}&name=${name}`
    }
  }
  else if (module && link) {
    url = module === 'search'
      ? `/pages/${module}/${link}?navigateBack=true`
      : `/pages/personal/${module}/${link}/index`
  }

  if (url) {
    console.log(
      '%c [ url ]-108',
      'font-size:13px; background:pink; color:#bf2c9f;',
      url,
    )

    // todo: 针对于 app端无法渲染 rich-text
    // #ifdef H5
    uni.navigateTo({ url })
    // #endif
    // #ifdef APP-PLUS
    uni.navigateTo({ url })
    // #endif
  }
}
</script>

<template>
  <overlay-search-wraper class="min-h-screen bg-#edf2f5">
    <!-- 个人信息 -->
    <UserProfile :user-info="userInfo" />
    <!-- 余额信息 -->
    <MoneyInfo :user-info="userInfo" />
    <!-- 基础信息 -->
    <EarningsInfo :hide-team="appInfo.hideTeam" @click="handleClick" />
    <!-- 常用功能 -->
    <CommonFunctions :hide-team="appInfo.hideTeam" @click="handleClick" />
    <!-- 关于我们（合并到常用功能中去） -->
    <!-- <ContactUs @click="handleClick" /> -->
    <SwiperInfo />
    <!-- 9.9包邮 -->
    <FreeShipping />
  </overlay-search-wraper>
</template>

<route type="page" lang="json">
{
  "style": {
    "navigationBarTextStyle": "black"
  }
}
</route>
