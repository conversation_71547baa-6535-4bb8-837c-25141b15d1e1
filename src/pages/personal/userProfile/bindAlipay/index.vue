<script lang="ts" setup>
import { setAliPayAccount } from '@/services'
import { useAuthStore } from '@/store/user'

const { userInfo } = useAuthStore()
const model = reactive<{
  realName: string
  aliPayAccount: string
}>({
  realName: '',
  aliPayAccount: '',
})
const form = ref()

function handleSubmit() {
  form.value
    .validate()
    .then(async ({ valid, errors }) => {
      if (valid) {
        const { aliPayAccount = '' } = await setAliPayAccount(model)
        if (aliPayAccount)
          uni.navigateBack()
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}
watch(
  () => userInfo,
  (newValue) => {
    if (newValue) {
      model.realName = newValue.value.realName || ''
      model.aliPayAccount = newValue.value.aliPayAccount || ''
    }
  },
  { deep: true, immediate: true },
)
function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="form-wrapper min-h-screen">
    <view class="nav-bar">
      <wd-navbar title="绑定支付宝" left-arrow :bordered="false" @click-left="handleClickLeft" />
    </view>
    <wd-form ref="form" :model="model">
      <wd-cell-group border>
        <wd-input
          v-model="model.realName" label="真实姓名" label-width="100px" prop="realName" clearable
          placeholder="请输入支付宝认证的真实姓名" :rules="[
            { required: true, message: '请输入支付宝认证的真实姓名' },
            {
              required: true,
              pattern: /^[\u4e00-\u9fa5]{2,4}$/,
              message: '请输入支付宝认证的真实姓名，只能是2-4个汉字',
            }]"
        />
        <wd-input
          v-model="model.aliPayAccount" label="支付宝账号" label-width="100px" prop="aliPayAccount" clearable
          placeholder="请输入支付宝账号" :rules="[
            { required: true, message: '支付宝账号跟真实姓名需要对应' },
            {
              required: true,
              pattern: /(^[a-zA-Z0-9_.@]{0,30}$)|(^(?=\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$).{0,30}$)/,
              message: '请输入正确的支付宝账号格式（6-30位字母、数字或下划线）',
            }]"
        />
      </wd-cell-group>
      <view class="footer">
        <wd-button type="primary" size="large" block @click="handleSubmit">
          提交
        </wd-button>
      </view>
    </wd-form>
  </view>
</template>

<style lang="scss" scoped>
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}
.wd-form {
  padding: calc(var(--status-bar-height) + 4rem) 0.5rem 0.5rem;
}
.footer {
  padding: 12px;
}
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
