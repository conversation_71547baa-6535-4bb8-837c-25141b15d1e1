<script lang="ts" setup>
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { queryCashData } from '@/services'

const listLoadStatus = ref<number>(0)
const state = ref<LoadMoreState>('loading')
// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

const listPagination = reactive({
  index: 0,
  num: 20,
})
const withdrawals = ref([])

onMounted(() => {
  loadCashData()
})

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', listLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (listLoadStatus.value === 0 && noMoreData.value === 0) {
    loadCashData() // 调用加载更多的函数
  }
})

async function loadCashData(fresh = false) {
  if (listLoadStatus.value === 1) {
    console.log('[提现记录] 正在请求中~~~')
    return false
  }
  listLoadStatus.value = 1

  const Limit = listPagination.num
  let PageNo = listPagination.index + 1
  if (fresh)
    PageNo = 1

  try {
    const { list = [] } = await queryCashData({
      pageNo: PageNo,
      limit: Limit,
    })

    listLoadStatus.value = 0
    if (!list || list.length <= 0) {
      console.log('[提现记录] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        withdrawals.value = []
      }
      if (withdrawals.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      withdrawals.value = list || []
    else if (list && list.length > 0)
      withdrawals.value = withdrawals.value.concat(list) // 追加新加载的记录

    listPagination.index = PageNo // 更新当前页码
    listPagination.num = Limit
  }
  catch (e) {
    console.error('[提现记录]加载失败', e)
    listLoadStatus.value = 0
    isDataEmpty.value = 0
  }
}

// 格式化日期时间
function formatDateTime(dateTimeStr: string | number | Date) {
  const dateTime = new Date(dateTimeStr)
  return `${dateTime.getFullYear()}-${pad(dateTime.getMonth() + 1)}-${pad(dateTime.getDate())} ${pad(dateTime.getHours())}:${pad(dateTime.getMinutes())}:${pad(dateTime.getSeconds())}`
}

// 格式化日期
function formatDate(dateStr: string | number | Date) {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`
}

// 补零
function pad(num: number) {
  return num.toString().padStart(2, '0')
}

function formatState(state: any) {
  switch (state) {
    case 1:
      return '待审核'
    case 2:
      return '已拒绝'
    case 4:
      return '已审核待打款'
    case 5:
      return '已打款'
    default:
      return '未知状态'
  }
}

function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="wwrapper min-h-screen bg-#edf2f5">
    <view class="nav-bar">
      <wd-navbar title="提现记录" left-arrow :bordered="false" @click-left="handleClickLeft" />
    </view>
    <view class="withdrawal-record">
      <view v-for="withdrawal in withdrawals" :key="withdrawal.id" class="withdrawal-item">
        <wd-card :title="withdrawal.create_time">
          <view class="mb-2">
            <text>收款账号：</text>
            <text class="text-black">
              {{ withdrawal.app_skaccount }}
            </text>
          </view>
          <view class="mb-2">
            <text>收款名称：</text>
            <text class="text-black">
              {{ withdrawal.app_skuser }}
            </text>
          </view>
          <!-- <view class="mb-2">
            <text>支付方式: </text>
            <text class="text-black">支付宝</text>
          </view> -->
          <view class="mb-2">
            <text>提现金额：</text>
            <text class="text-#FF9000">
              ￥{{ withdrawal.cash_money }}
            </text>
          </view>
          <view class="flex items-center">
            <text>提现状态：</text>
            <wd-tag v-if="withdrawal.cash_state === 2" plain color="#F87171FF" bg-color="#F87171FF">
              {{ formatState(withdrawal.cash_state) }}
            </wd-tag>
            <wd-tag v-else-if="withdrawal.cash_state === 4" plain color="#60A5FAFF" bg-color="#60A5FAFF">
              {{ formatState(withdrawal.cash_state) }}
            </wd-tag>
            <wd-tag v-else-if="withdrawal.cash_state === 5" plain color="#4ADE80FF" bg-color="#4ADE80FF">
              {{ formatState(withdrawal.cash_state) }}
            </wd-tag>
            <wd-tag v-else plain>
              {{ formatState(withdrawal.cash_state) }}
            </wd-tag>
          </view>
        </wd-card>
      </view>
    </view>
    <!-- loading -->
    <wd-loadmore v-if="listLoadStatus == 1" :state="state" @reload="loadCashData" />
    <!-- no data -->
    <wd-status-tip v-if="listLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nocontent.png" tip="暂无提现记录" />
  </view>
</template>

<style lang="scss" scoped>
  .wwrapper {
    .nav-bar {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 100;
      background: #fff;
      padding-top: calc(var(--status-bar-height) + 0.5rem);
    }
    .withdrawal-record {
      padding-top: calc(var(--status-bar-height) + 3.5rem);
      padding-bottom: 0.5rem;
      .withdrawal-item {
        margin-top: 0.5rem;
        .wd-card {
          margin: 0 0.5rem;
        }
      }
    }
  }
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
