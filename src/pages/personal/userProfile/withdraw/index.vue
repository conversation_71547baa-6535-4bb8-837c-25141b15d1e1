<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { cashApply, fetchUserCenter } from '@/services'

const message = useMessage()

const userInfo = ref(null)
const balance = ref(0.00)
const withdrawAmount = ref('')

// function editInfo() {
//   uni.navigateTo({
//     url: '/pages/personal/userProfile/bindAlipay/index',
//   })
// }

onMounted(async () => {
  await init()
})

async function init() {
  const info = await fetchUserCenter()
  if (info) {
    userInfo.value = info
    balance.value = info.balance
  }
}

function withdrawalRecord() {
  uni.navigateTo({
    url: '/pages/personal/userProfile/withdrawalRecord/index',
  })
}

async function submitWithdraw() {
  if (withdrawAmount.value !== null && parseInt(withdrawAmount.value) < 1) {
    message.alert('提现金额最低提现1元')
    return false
  }
  const data = await cashApply({
    cash_money: withdrawAmount.value,
  })
  console.log('提现申请结果', data)
  await uni.showToast({ icon: 'none', title: data.msg })
}

function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="with-draw min-h-screen bg-#edf2f5">
    <view class="nav-bar">
      <wd-navbar title="余额提现" left-arrow :bordered="false" @click-left="handleClickLeft" />
    </view>
    <view class="custom-card">
      <view class="money-info">
        <view class="mb-4">
          <view>可提现余额</view>
          <view class="mt-0.25rem flex items-center justify-between">
            <view class="text-#ea016e">
              ￥<text class="text-2rem">
                {{ balance }}
              </text>
            </view>
            <view>
              <text class="record-btn" @click="withdrawalRecord">
                提现记录
              </text>
            </view>
          </view>
        </view>
        <view class="minput mb-4">
          <view class="mb-4">
            提现金额
          </view>
          <view class="flex items-center">
            <view class="mr-0.5rem text-1.5rem">
              ￥
            </view>
            <view class="flex-1 text-#8a8a8a">
              <input v-model="withdrawAmount" type="number" placeholder="输入提现金额，最低提现1元">
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="info-card">
      <view class="mb-4">
        <wd-form :model="userInfo">
          <wd-cell-group border>
            <wd-input v-model="userInfo.realName" readonly label="真实姓名" label-width="100px" prop="realName" />
            <wd-input v-model="userInfo.aliPayAccount" readonly label="支付宝账号" label-width="100px" prop="aliPayAccount" />
          </wd-cell-group>
        </wd-form>
        <!-- <wd-button block @click="editInfo">
          修改
        </wd-button> -->
      </view>
      <wd-button block type="error" @click="submitWithdraw">
        立即提现
      </wd-button>
    </view>
    <view class="tip-card mt-0.5rem p-0.5rem">
      <view class="rounded-lg bg-#fff p-0.5rem">
        <view class="font-bold">
          提现规则说明
        </view>
        <view class="mt-0.5rem text-0.85rem">
          <!-- <view class="text-#fa4350">
            <view class="line-height-1.5rem">
              重要提示！！！
            </view>
            <view class="line-height-1.5rem">
              1、每月15号结算上个月确认收货的订单佣金;
            </view>
            <view class="line-height-1.5rem">
              2、每月15号00:00开始到21号14:00 为公司财冬账单核定报税众理期，在比期间暂时关闭提现功能;
            </view>
            <view class="line-height-1.5rem">
              3、一般情况下，每月21号14:00后可以正常提现上个月的订单佣金；
            </view>
          </view>
          <view class="mt-0.5rem">
            <view class="line-height-1.5rem">
              1、每月15号结算上月确认收货的订单【抖音、拼多多等部分确认收货订单会因官方平台出现延迟结算，导致当前易资也无法按时结算，请您谅解，一般在下个月就能自动结算，请耐心等待】，即可提现。
            </view>
            <view class="line-height-1.5rem">
              2、自购账户金额满1元起提现，提现金额为1元的整数倍，提现无手续费。
            </view>
            <view class="line-height-1.5rem">
              3、每天提现1次，申请提现后24小时内到账(正常一般几分钟内就到账，如果几分钟内未到账，则大概率触发系统风控，需要人工审核，一般24小时内处理完毕)。
            </view>
            <view class="line-height-1.5rem">
              4、提现时务必确认好提现金额，到账后请注意核实支付宝账单，有问题请联系客服。
            </view>
            <view class="line-height-1.5rem">
              5、由于我们用的是企业支付宝自动转账，支付宝官方需要校验支付宝账号和姓名一一对应才能转账成功。所以支付宝账号要填写正确，姓名要填写真实姓名，支付宝和姓名需-一对应。
            </view>
          </view> -->

          <view class="mt-0.5rem">
            <view class="font-bold line-height-1.5rem">
              一、结算周期
            </view>
            <view class="line-height-1.5rem">
              每月25日统一结算上月联盟已结算的订单佣金，结算完成后开放提现申请权限。
            </view>
          </view>

          <view class="mt-0.5rem">
            <view class="font-bold line-height-1.5rem">
              二、提现规则
            </view>
            <view class="line-height-1.5rem">
              起提金额：单笔最低1元人民币
            </view>
            <view class="line-height-1.5rem">
              提现单位：须为1元的整数倍
            </view>
            <view class="line-height-1.5rem">
              手续费：提现全程免收手续费
            </view>
          </view>

          <view class="mt-0.5rem">
            <view class="font-bold line-height-1.5rem">
              三、审核流程
            </view>
            <view class="line-height-1.5rem">
              提现申请提交后进入人工审核环节
            </view>
            <view class="line-height-1.5rem">
              审核时效：原则上不超过24个工作日小时
            </view>
            <view class="line-height-1.5rem">
              到账时间：审核通过后实时进行企业支付宝转账
            </view>
          </view>

          <view class="mt-0.5rem">
            <view class="font-bold line-height-1.5rem">
              四、账户信息要求
            </view>
            <view class="line-height-1.5rem">
              请确保填写信息准确无误：支付宝实名认证账户与支付宝账户完全一致的真实姓名
            </view>
            <view class="line-height-1.5rem">
              转账验证：支付宝系统将自动校验账号与姓名一致性，信息不符将导致转账失败
            </view>
          </view>

          <view class="mt-0.5rem">
            <view class="font-bold line-height-1.5rem">
              五、到账确认
            </view>
            <view class="line-height-1.5rem">
              资金到账后请及时核对支付宝账单明细，如有疑问请及时联系官方客服处理。
            </view>
            <view class="line-height-1.5rem">
              （注：所有时间节点遇法定节假日将顺延至最近工作日办理）
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
:deep(.with-draw) {
  .nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    background: #fff;
    padding-top: calc(var(--status-bar-height) + 0.5rem);
  }

  .custom-card {
    padding: calc(var(--status-bar-height) + 4rem) 0.5rem 0.5rem;
    background-image: url('@/static/moments-1.png');
    background-size: cover;
    background-repeat: repeat;
    .money-info {
      background-color: #fff3f7;
      border-radius: 0.5rem;
      padding: 0.5rem;
      .record-btn {
        border: 1px solid #ff649c;
        padding: 0.2rem 0.5rem;
        color: #646464;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        background-color: #fff;
      }
      .minput {
        background-color: #fff;
        padding: 1rem 0.5rem;
        border-radius: 0.5rem;
      }
    }
  }

  .info-card {
    padding: 0 0.5rem;
    .wd-cell-group {
      border-radius: 0.5rem;
      overflow: hidden;
      .wd-input__label {
        color:#646464;
      }
    }
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
