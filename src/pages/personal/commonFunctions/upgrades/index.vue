<script lang="ts" setup>
import { useAuthStore } from '@/store/user'

const { userInfo } = useAuthStore()

function handleClick(url: string) {
  uni.navigateTo({
    url,
  })
}

function switchTab(url: string) {
  uni.switchTab({
    url,
  })
}

function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="nav-bar">
    <wd-navbar title="达人升级" left-arrow :bordered="false" @click-left="handleClickLeft" />
  </view>
  <view class="upgrades min-h-screen bg-[#121212] p-2 text-white">
    <view class="flex items-center rounded-lg bg-[#222] p-2 space-x-2">
      <view class="avaar h-16 w-16 flex items-center justify-center rounded-full bg-#dd524d">
        <!-- <text class="text-xl text-[#bf2c9f]">
          S{{ userInfo?.tkUserLevel }}
        </text> -->
        <wd-img v-if="userInfo?.fansImg" :src="`/static/avatar/${userInfo?.fansImg}`" class="h-full w-full" />
        <wd-img v-else src="/static/avatar/1.jpg" class="h-full w-full" />
      </view>
      <view>
        <text class="block text-lg">
          {{ userInfo?.fansNick }}
        </text>
        <view>
          <wd-tag v-if="userInfo?.tkUserLevel === 1" color="#48CFFF" bg-color="#48CFFF" plain>
            初级达人
          </wd-tag>
          <wd-tag v-if="userInfo?.tkUserLevel === 2" color="#FFCF65" bg-color="#FFCF65" plain>
            钻石达人
          </wd-tag>
          <wd-tag v-if="userInfo?.tkUserLevel === 3" color="#DD524D" bg-color="#DD524D" plain>
            超级达人
          </wd-tag>
        </view>
      </view>
    </view>
    <!-- Conditions -->
    <view class="mt-3 rounded-lg bg-[#222] px-2 py-4">
      <view class="mb-1rem">
        <text>满足以下条件，即可升级</text>
      </view>
      <view class="flex justify-between">
        <view class="rounded-lg bg-[#333] px-4 py-2">
          <view class="flex flex-col items-center">
            <view class="mb-2 flex items-center">
              <wd-img src="/static/icon/zuanshi.png" width="1rem" height="1rem" />
              <text class="ml-0.25rem">
                钻石达人
              </text>
            </view>
            <text class="mb-2">
              邀请4个有效粉丝
            </text>
            <!-- <text class="mb-2">
              已激活 0/4
            </text> -->
            <view class="h-2 w-full overflow-hidden rounded-full bg-gray-600">
              <view class="h-full w-0 bg-[#bf2c9f]" />
            </view>
          </view>
        </view>
        <view class="rounded-lg bg-[#333] px-4 py-2">
          <view class="flex flex-col items-center">
            <view class="mb-2 flex items-center">
              <wd-img src="/static/icon/super_zuanshi.png" width="1rem" height="1rem" />
              <text class="ml-0.25rem">
                超级达人
              </text>
            </view>
            <text class="mb-2">
              邀请100个有效粉丝
            </text>
            <!-- <text class="mb-2">
              已激活 0/100
            </text> -->
            <view class="h-2 w-full overflow-hidden rounded-full bg-gray-600">
              <view class="h-full w-0 bg-[#bf2c9f]" />
            </view>
          </view>
        </view>
        <!-- <view class="flex flex-col items-center rounded-lg bg-[#333] p-4">
          <text class="text-md mb-2">
            邀请有效粉丝6位
          </text>
          <text class="mb-4 text-sm">
            近30天内(0/6)
          </text>
          <view class="h-2 w-full overflow-hidden rounded-full bg-gray-600">
            <view class="h-full w-0 bg-[#bf2c9f]" />
          </view>
          <wd-button mt-3 block @click="handleClick('/pages/personal/earningsInfo/share/h2canvas?navigateBack=true')">
            立即邀请
          </wd-button>
        </view>
        <view class="flex flex-col items-center rounded-lg bg-[#333] p-4">
          <text class="text-md mb-2">
            自购佣金≥900元
          </text>
          <text class="mb-4 text-sm">
            近30天内(0/900)
          </text>
          <view class="h-2 w-full overflow-hidden rounded-full bg-gray-600">
            <view class="h-full w-0 bg-[#bf2c9f]" />
          </view>
          <wd-button
            class="mt-4 rounded-full bg-[#bf2c9f] px-4 py-2" mt-3 block
            @click="switchTab('/pages/home/<USER>')"
          >
            赚佣金
          </wd-button>
        </view> -->
      </view>
    </view>
    <view>
      <wd-button mt-3 block @click="handleClick('/pages/personal/earningsInfo/share/index')">
        立即邀请
      </wd-button>
    </view>
    <view class="mt-3 rounded-lg bg-[#222] p-2 pt-4">
      <view class="mb-5">
        1. 新用户是初级达人，享超高佣金
      </view>
      <view class="mb-5">
        2. 初级达人邀请4个有效粉丝可升级为钻石达人，享超高佣金，团队的3个百分点永久分成
      </view>
      <view class="mb-5">
        3. 钻石达人邀请100个有效粉丝可升级为超级达人，享超高佣金，团队的6个百分点永久分成
      </view>
      <!-- <view class="mt-5">
        <text class="font-bold" block>
          有效粉丝：
        </text>
        <text>粉丝下单1笔（实付大于2元），确认收货后即可成为有效粉丝</text>
      </view> -->
      <view class="mb-5 border-t border-gray-600">
        <text>省钱购物就用省赚客APP，购物先领券，优惠又省钱</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}
.upgrades {
  padding-top: calc(var(--status-bar-height) + 4.2rem);
  .avaar {
    overflow: hidden;
  }
}
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
