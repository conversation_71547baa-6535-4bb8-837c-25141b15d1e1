<script lang="ts" setup>
import { userFeedback } from '@/services'

const model = reactive<{
  content: string
}>({
  content: '',
})

const form = ref()

function handleSubmit() {
  form.value
    .validate()
    .then(async ({ valid, errors }) => {
      // console.log(valid)
      // console.log(errors)
      // console.log(model.content)
      if (!valid) {
        return false
      }
      const { statusText = '' } = await userFeedback(model)
      if (statusText === 'OK') {
        uni.showToast({
          title: '提交成功',
          icon: 'none',
          duration: 2000,
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1000)
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}
function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="nav-bar">
    <wd-navbar title="建议反馈" left-arrow :bordered="false" @click-left="handleClickLeft" />
  </view>
  <view class="jycontent">
    <!-- <wd-notice-bar wrapable :scrollable="false">
      <template #prefix>
        <wd-icon class="prefix" name="warn-bold">
          占位符
        </wd-icon>
      </template>
      如果是客服问题，不用在此页面留言，请及时联系客服<template #suffix>
        <div style="color: #4d80f0" @click="handleServer">
          去联系
        </div>
      </template>
    </wd-notice-bar> -->
    <view>
      <wd-form ref="form" :model="model">
        <wd-cell-group border>
          <wd-textarea
            v-model="model.content" label="" :maxlength="1000" prop="content" clearable show-word-limit
            placeholder="请输入您的宝贵意见(最多1000字哦)" :rules="[{ required: true, message: '请输入您的宝贵意见' }]"
          />
        </wd-cell-group>
        <view class="footer mt-1rem">
          <wd-button type="primary" size="large" block @click="handleSubmit">
            提交
          </wd-button>
        </view>
      </wd-form>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}
.jycontent {
  padding: calc(var(--status-bar-height) + 4.5rem) 1rem 1rem;
  ::v-deep .wd-cell-group__body {
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
  }
}
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
