<script lang="ts" setup>
import { queryMaterialList } from '@/services'

const value = ref<string[]>(['0'])
const materiallist = ref<appMaterialList.ResponseDataItem>([])
onMounted(() => {
  init()
})
async function init() {
  const data = await queryMaterialList({ type: '4' })
  materiallist.value = data
}

function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="nav-bar">
    <wd-navbar title="常见问题" left-arrow :bordered="false" @click-left="handleClickLeft" />
  </view>
  <view class="queryMaterialList">
    <wd-collapse v-model="value" accordion>
      <block v-for="(item, index) in materiallist" :key="index">
        <wd-collapse-item :name="`${item.id}`" :title="item.title">
          <view>
            <rich-text class="appMaterial" :nodes="item.content" />
          </view>
        </wd-collapse-item>
      </block>
    </wd-collapse>
  </view>
</template>

<style lang="scss" scoped>
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}
.queryMaterialList {
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 3rem);
}
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
