<script lang="ts" setup>
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { orderStatus, platformTypes } from './type'
import { hideTeam, queryOrderList } from '@/services'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'
import { appInfo } from '@/common'

const title_tabs = ['全部订单', '我的订单', '团队订单']
const sub_tabs = ['全部', '已付款', '已收货', '已结算', '已失效']

const activeTitleTab = ref<string>('全部订单')
const activeSubTab = ref<number>(0)

const showTitleTab = ref<boolean>(false)

function getIndexForTab(tabs: [], str: string) {
  return tabs.findIndex(item => (item === str))
}

const goodListPagination = reactive({
  index: 0,
  num: 20,
})
const orders = ref<appOrderOrderList.ResponseData['list']>([])

const listLoadStatus = ref<number>(0)
const state = ref<LoadMoreState>('loading')
// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', listLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (listLoadStatus.value === 0 && noMoreData.value === 0) {
    handleSearchOrders()
  }
})

async function handleSearchOrders(fresh = false) {
  if (listLoadStatus.value === 1) {
    console.log('[search orders] 正在请求中~~~')
    return false
  }
  listLoadStatus.value = 1

  if (fresh) {
    state.value = 'loading'
    orders.value = []
  }

  const Limit = goodListPagination.num
  let PageNo = goodListPagination.index + 1
  if (fresh)
    PageNo = 1
  try {
    const data = await queryOrderList({
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
      orderType: getIndexForTab(title_tabs, activeTitleTab.value),
      status: orderStatus[activeSubTab.value].code,
    })
    listLoadStatus.value = 0

    if (!data || !data.list || data.list.length <= 0) {
      console.log('[search orders] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        orders.value = []
      }
      if (orders.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (data.list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      orders.value = data.list || []
    else
      orders.value = orders.value.concat(data.list)

    goodListPagination.index = PageNo
    goodListPagination.num = Limit
  }
  catch (err) {
    orders.value = []
    listLoadStatus.value = 3
  }
}

function handleClickLeft() {
  uni.navigateBack()
}

function copyText(text: string) {
  setClipboardData(text)
}

onMounted(async () => {
  await handleSearchOrders()
  // show title
  const hide = await isTimeGreaterThan()
  showTitleTab.value = !(hide && hide == true)
})

async function isTimeGreaterThan() {
  // const currentTime = new Date()
  // const targetTime = new Date('2025-05-23T00:00:00')
  // const result = currentTime > targetTime
  // // console.log('time', result)
  // return result
  const d = await hideTeam({
    current_version: appInfo.version,
  })
  console.log('d', d.data)
  return d.data && d.data === 'hide'
}

function changeTitleTab() {
  console.log('changeTitleTab', activeTitleTab.value, noMoreData.value, isDataEmpty.value)
  handleSearchOrders(true)
}

function changeSubTab(e: { index: number }) {
  console.log('changeSubTab', e.index, 'activeSubTab', activeSubTab.value)
  activeSubTab.value = e.index
  handleSearchOrders(true)
}

function ruleClick() {
  uni.navigateTo({
    url: '/pages/personal/material?materialId=176&name=结算规则',
  })
}
</script>

<template>
  <view class="nav-bar">
    <wd-navbar left-arrow :bordered="false" @click-left="handleClickLeft">
      <template v-if="showTitleTab" #title>
        <wd-segmented v-model:value="activeTitleTab" :options="title_tabs" @change="changeTitleTab" />
      </template>
      <template #right>
        <wd-icon name="layers" size="24" @click="ruleClick" />
      </template>
    </wd-navbar>
    <wd-tabs v-model="activeSubTab" @click="changeSubTab">
      <block v-for="item in sub_tabs" :key="item">
        <wd-tab :title="`${item}`" />
      </block>
    </wd-tabs>
  </view>
  <view class="order min-h-screen bg-#edf2f5">
    <view v-for="(item, index) in orders" :key="index" class="order-item flex flex-col justify-around rounded-lg bg-#fff p-0.5rem">
      <view class="order-top flex items-center justify-between">
        <view flex items-center>
          <wd-img :src="`${platformTypes[item.platformId].icon}`" class="mr-0.25rem h-1rem w-1rem" />
          <text class="time">
            下单时间：{{ item.orderPaidTime }}
          </text>
        </view>
        <view>
          <wd-tag v-if="item.payStatus == 3" round type="success">
            已结算
          </wd-tag>
          <wd-tag v-if="item.payStatus == 12" round>
            已付款
          </wd-tag>
          <wd-tag v-if="item.payStatus == 13 || item.payStatus == 15" round type="danger">
            已失效
          </wd-tag>
          <wd-tag v-if="item.payStatus == 16" round>
            已收货
          </wd-tag>
        </view>
      </view>
      <view class="order-content mt-0.5rem flex">
        <view flex-0.4>
          <wd-img :src="item.itemImg" width="5rem" height="5rem" mr-0.5rem radius="0.5rem" />
        </view>
        <view flex flex-col justify-around>
          <view class="title">
            {{ item.itemTitle }}
          </view>
          <view class="count text-gray-400">
            购买数量：×{{ item.itemNum }}
          </view>
          <view class="price flex items-center justify-between">
            <text class="mr-0.5rem">
              预估佣金<text class="text-red-500 font-bold">
                ￥{{ item.shareFee }}
              </text>
            </text>
            <text class="mr-0.5rem">
              付款金额<text class="font-bold">
                ￥{{ item.payTotalPrice }}
              </text>
            </text>
          </view>
        </view>
      </view>
      <view class="order-bottom mt-0.5rem text-gray-400">
        <view class="no flex items-center">
          <text class="mr-0.5rem">
            <text class="font-bold">
              订单号：
            </text>
            <text>{{ item.orderId }}</text>
          </text>
          <view class="text-left">
            <wd-button size="small" type="info" plain @click="copyText(`${item.orderId}`)">
              复制
            </wd-button>
          </view>
        </view>
        <!-- <view class="mt-0.25rem">
          <text class="font-bold">
            订单结算状态：
          </text>
          <text>未结算</text>
        </view> -->
      </view>
    </view>
    <!-- loading -->
    <wd-loadmore v-if="listLoadStatus == 1" :state="state" @reload="handleSearchOrders" />
    <!-- no data -->
    <wd-status-tip v-if="listLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
  </view>
</template>

<style lang="scss" scoped>
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
  //padding-bottom: 0.5rem;

  ::-webkit-scrollbar {
    background: transparent;
    /* 将滚动条背景色设为透明 */
  }

  ::v-deep .wd-navbar__title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 70%;
  }
}
.order {
  padding: calc(var(--status-bar-height) + 6.6rem) 0.5rem 0.5rem;

  .order-item {
    margin-top: 0.5rem;
    font-size: 0.75rem;

    ::v-deep .wd-button {
      font-size: 0.6rem;
      height: 20px;
      color: #9ca3afff;
      border-color: #9ca3afff;
    }
  }

  .order-item:first-child {
    margin-top: 0;
  }
}
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
