<script lang="ts" setup>
import UQRCode from 'uqrcodejs'
import { posterList } from '@/services/poster'
import { appInfo } from '@/common'
import { useAuthStore } from '@/store/user'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

const { userInfo } = useAuthStore()

const posterListInfo = ref<any>({})
const bannerIndex = ref<number>(0)

function onChange(e: any) {
  bannerIndex.value = e.detail.current
}

onMounted(async () => {
  await init()
})

async function init() {
  posterListInfo.value = await posterList()
  // 生成海报
  // 第一张让他快点生成
  singleDrawPoster(0)
  // 剩下的几张延迟生成
  setTimeout(() => batchDrawPoster(), 1000)
}

function singleDrawPoster(index: number) {
  uni.getImageInfo({
    src: posterListInfo.value.picture_urls[index],
    success: imageInfo => drawPoster(imageInfo, index),
    fail: err => console.error('获取图片信息失败:', err),
  })
}

async function batchDrawPoster() {
  posterListInfo.value.picture_urls.forEach((item: string, index: number) => {
    if (index !== 0) {
      singleDrawPoster(index)
    }
  })
}

async function drawPoster(imageInfo: object, index: number) {
  const scaleWidth = pxWidth / imageInfo.width
  const scaleHeight = pxHeight / imageInfo.height
  const scale = Math.max(scaleWidth, scaleHeight)

  const drawWidth = imageInfo.width * scale
  const drawHeight = imageInfo.height * scale

  const ctx = uni.createCanvasContext(`canvas-${index}`, this)
  ctx.drawImage(imageInfo.path, 0, 0, drawWidth, drawHeight)
  // ctx.draw()

  await drawBack(ctx)

  await drawQrCode(ctx, index, posterListInfo.value.inviteUrl)

  await drawText(ctx, '- 邀请码 -', 12, 'gray', pxHeight - 45)

  await drawText(ctx, userInfo.value.vipInviteCode || userInfo.value.inviteCode, 16, 'black', pxHeight - 28)
}

// 海报页尺寸
const pxWidth = vwToPx(80)
const pxHeight = vhToPx(60)
// 背景尺寸
const backWidth = ref<number>(100)
const backHeight = ref<number>(130)
// 背景圆角尺寸
const backRadius = ref<number>(5)
// 二维码尺寸
const qrcodeSize = ref<number>(80)

function vhToPx(vh: number) {
  const windowHeight = uni.getSystemInfoSync().windowHeight
  return (vh * windowHeight) / 100
}

function vwToPx(vw: number) {
  const windowWidth = uni.getSystemInfoSync().windowWidth
  return (vw * windowWidth) / 100
}

async function drawBack(ctx) {
  // 计算正方形的绘制位置（居中）
  const x = (pxWidth - backWidth.value) / 2
  const y = pxHeight - backHeight.value - 20
  // 绘制带圆角的正方形
  ctx.beginPath()
  ctx.moveTo(x + backRadius.value, y)
  ctx.lineTo(x + backWidth.value - backRadius.value, y)
  ctx.arc(x + backWidth.value - backRadius.value, y + backRadius.value, backRadius.value, -Math.PI / 2, 0)
  ctx.lineTo(x + backWidth.value, y + backHeight.value - backRadius.value)
  ctx.arc(x + backWidth.value - backRadius.value, y + backHeight.value - backRadius.value, backRadius.value, 0, Math.PI / 2)
  ctx.lineTo(x + backRadius.value, y + backHeight.value)
  ctx.arc(x + backRadius.value, y + backHeight.value - backRadius.value, backRadius.value, Math.PI / 2, Math.PI)
  ctx.lineTo(x, y + backRadius.value)
  ctx.arc(x + backRadius.value, y + backRadius.value, backRadius.value, Math.PI, 3 * Math.PI / 2)
  ctx.closePath()
  // 设置填充颜色为白色
  ctx.setFillStyle('white')
  ctx.fill()
  ctx.draw(true)
}

async function drawQrCode(ctx, index: number, data: string) {
  const subctx = uni.createCanvasContext(`sub-canvas-${index}`, this)

  const qr = new UQRCode()
  qr.data = data
  qr.size = qrcodeSize.value
  qr.make()
  qr.canvasContext = subctx
  qr.drawCanvas()

  uni.canvasToTempFilePath({
    canvasId: `sub-canvas-${index}`,
    success: (res) => {
      // console.log('res', res.tempFilePath)
      ctx.drawImage(res.tempFilePath, (pxWidth - qrcodeSize.value) / 2, pxHeight - qrcodeSize.value - 60, qrcodeSize.value, qrcodeSize.value)
      ctx.draw(true)
    },
    fail: (err) => {
      console.error('保存失败', err)
    },
  })
}

async function drawText(ctx, text: string, size: number, color: string, y: number) {
  ctx.setFontSize(size)
  ctx.setFillStyle(color)
  ctx.fillText(text, (pxWidth - ctx.measureText(text).width) / 2, y)
  ctx.draw(true)
}

function handleClickLeft() {
  uni.navigateBack()
}

function copyInvite(code: string) {
  setClipboardData(code)
}

function shareLink(url: string) {
  const templateText = `自购省钱，分享还能赚钱！
快来下载省赚客APP吧！
-----------------
请在微信中打开
-----------------
${url}`
  setClipboardData(templateText)
}

function savePlacard() {
  const granted = uni.getStorageSync('grant_disk') || false
  if (!granted) {
    console.log('用户未授权存储权限，触发弹窗')
    popupShow.value = true
    return false
  }
  savePlacardToDisk()
}

function savePlacardToDisk() {
  uni.showLoading({
    mask: true,
  })
  uni.canvasToTempFilePath({
    canvasId: `canvas-${bannerIndex.value}`,
    success: (res) => {
      // console.log('res', res.tempFilePath)
      uni.hideLoading()
      uni.saveImageToPhotosAlbum({
        filePath: res.tempFilePath,
        success() {
          uni.hideLoading()
          uni.showToast({
            icon: 'none',
            title: '海报保存成功',
          })
          uni.setStorageSync('grant_disk', true)
        },
        fail(err) {
          console.error('海报保存失败', err.errMsg)
          uni.hideLoading()
          uni.showToast({
            icon: 'none',
            title: '海报保存失败',
          })
          uni.setStorageSync('grant_disk', false)
        },
      })
    },
    fail: (err) => {
      console.error('转换失败', err)
      uni.hideLoading()
      uni.showToast({
        title: '转换失败',
        icon: 'none',
      })
    },
  })
}

const sharePic = ref<string>('')
const show = ref<boolean>(false)
const popupShow = ref<boolean>(false)
const panels = ref([
  {
    iconUrl: '/static/icon/weixin.png',
    title: '微信好友',
    provider: 'weixin',
    scene: 'WXSceneSession',
  },
  {
    iconUrl: '/static/icon/weixin_circle.png',
    title: '微信朋友圈',
    provider: 'weixin',
    scene: 'WXSceneTimeline',
  },
  // {
  //   iconUrl: '/static/icon/qq.png',
  //   title: 'QQ好友',
  //   provider: 'qq',
  //   scene: '',
  // },
  {
    iconUrl: '/static/icon/weixin_collect.png',
    title: '微信收藏',
    provider: 'weixin',
    scene: 'WXSceneFavorite',
  },
])
function sharePlacard() {
  // #ifdef H5
  show.value = true
  // #endif

  // #ifdef APP-PLUS
  uni.showLoading({
    mask: true,
  })
  uni.canvasToTempFilePath({
    canvasId: `canvas-${bannerIndex.value}`,
    success: (res) => {
      console.log('res', res.tempFilePath)
      // 上传文件
      uni.uploadFile({
        url: `${appInfo.serverApi}app/poster/upload`,
        filePath: res.tempFilePath,
        name: 'myfile',
        formData: {
          imgFullPath: posterListInfo.value.picture_urls[bannerIndex.value],
        },
        header: {
          token: uni.getStorageSync('userToken'),
        },
        success: (uploadFileRes) => {
          console.log('上传成功', uploadFileRes)
          uni.hideLoading()
          const { data } = JSON.parse(uploadFileRes.data)
          sharePic.value = data
          show.value = true
        },
        fail: (error) => {
          console.error('上传失败', error)
          uni.hideLoading()
        },
      })
    },
    fail: (err) => {
      console.error('转换失败', err)
      uni.hideLoading()
      uni.showToast({
        title: '转换失败',
        icon: 'none',
      })
    },
  })
  // #endif
}

function selectShare(e: { index: number, item: object }) {
  uni.share({
    provider: e.item.provider,
    scene: e.item.scene,
    type: 2,
    imageUrl: sharePic.value,
    success(res) {
      console.log(`success:${JSON.stringify(res)}`)
    },
    fail(err) {
      console.log(`fail:${JSON.stringify(err)}`)
    },
  })
}

async function openDiskAuth() {
  popupShow.value = false
  const granted = uni.getStorageSync('grant_disk')
  if (granted === '' || granted === true) {
    savePlacardToDisk()
  }
  else {
    uni.openAppAuthorizeSetting()
  }
}

async function closeDiskAuth() {
  popupShow.value = false
}
</script>

<template>
  <view class="nav-bar">
    <wd-navbar title="分享好友" left-arrow :bordered="false" @click-left="handleClickLeft" />
  </view>
  <view class="share-page">
    <view class="main bg-#edf2f5 pb-1rem pt-1.5rem">
      <view class="card-swiper">
        <swiper class="swiper h-60vh" :circular="true" :current="bannerIndex" @animationfinish="onChange">
          <swiper-item v-for="(item, index) in posterListInfo.picture_urls" :key="index">
            <view class="swiper-item">
              <view class="card relative overflow-hidden rounded-lg">
                <canvas :canvas-id="`canvas-${index}`" :style="{ width: `${vwToPx(80)}px`, height: `${vhToPx(60)}px` }" />
                <canvas :canvas-id="`sub-canvas-${index}`" :style="{ position: 'absolute', top: 0, left: 0, opacity: 0, width: `${qrcodeSize}px`, height: `${qrcodeSize}px` }" />
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>
    <view class="footer py-1rem">
      <view class="contain">
        <view class="mb-0.25rem">
          我的邀请码
        </view>
        <view class="code mb-1rem" @click="copyInvite(userInfo.vipInviteCode || userInfo.inviteCode)">
          <text>
            {{ userInfo.vipInviteCode || userInfo.inviteCode }}
          </text>
          <text class="copy-btn">
            复制
          </text>
        </view>
        <view class="description text-0.75rem">
          省赚客佣金高，如果您愿意，可以分享给朋友，跟好友一起省赚!
        </view>
        <view class="mt-0.75rem rounded-lg bg-[#FA4350] p-4 text-white">
          <text class="mx-2" @click="shareLink(posterListInfo.inviteUrl)">
            <wd-icon name="link" />
            分享链接
          </text>
          <text class="mx-2" @click="savePlacard">
            <wd-icon name="download" />
            保存海报
          </text>
          <text class="mx-2" @click="sharePlacard">
            <wd-icon name="share" />
            分享海报
          </text>
        </view>
      </view>
    </view>
  </view>
  <view class="share-menu w-full">
    <wd-action-sheet v-model="show" :panels="panels" title="选择分享渠道" cancel-text="取消" :close-on-click-modal="false" :z-index="102" @select="selectShare" />
  </view>
  <!-- 存储授权弹窗提示 -->
  <wd-popup v-model="popupShow" :close-on-click-modal="false" closable lock-scroll :z-index="102" custom-style="border-radius: 0.5rem" @close="closeDiskAuth">
    <view class="popu w-75vw p-0.5rem">
      <view class="mt-2rem flex justify-center">
        <view class="h-4rem w-4rem overflow-hidden bg-amber" style="border-radius: 50%">
          <wd-img src="/static/logo.png" class="h-full w-full" />
        </view>
      </view>
      <view class="mt-1rem text-center">
        <text class="text-1.05rem">
          省赚客需要以下权限
        </text>
      </view>
      <view class="mt-1rem flex justify-center px-1rem">
        <text class="text-0.85rem text-#333">
          为保证您正常、安全的使用省赚客APP，我们需要向您申请如下权限：
        </text>
      </view>
      <view class="mt-1rem flex px-1rem">
        <view>
          <view class="h-2rem w-2rem flex items-center justify-center bg-#FEE4ED" style="border-radius: 50%">
            <wd-icon name="phone" color="#ED3226" size="20px" />
          </view>
        </view>
        <view class="pl-0.5rem text-0.85rem">
          <view>
            <text class="text-#333">
              获取存储权限
            </text>
          </view>
          <view class="mt-0.5rem">
            <text class="text-#ACACAC">
              为了您能正常保存图片素材，我们需要访问您的存储权限，如您拒绝授权不影响您使用 APP 的其他功能。
            </text>
          </view>
        </view>
      </view>
      <view class="mb-0.5rem mt-1rem px-1rem">
        <wd-button block size="large" @click="openDiskAuth">
          开启权限
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}
.share-page {
  padding-top: calc(var(--status-bar-height) + 3rem);
  .main {
    .card-swiper {
      .swiper {
        uni-swiper-item {
          display: flex;
          align-items: center;
          width: 80vw !important;
          padding-left: 5vw !important;
          margin-left: 5vw !important;
        }
        .swiper-item {
          .card {
            uni-image {
              transition: height 0.5s ease;
            }
            .imgactive {
              height: 59vh;
            }
          }
          .sqrcode {
            position: absolute;
            left: 50%;
            bottom: 2.5rem;
            transform: translateX(-50%);
            text-align: center;
            background-color: #fff;
            padding: 0.75rem;
            border-radius: 0.25rem;
          }
        }
      }
    }
  }
  .contain {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .code {
      display: flex;
      align-items: center;
      color: #FA4350;
      font-weight: bold;
      font-size: 1.25rem;
      .copy-btn {
        border: 1px solid #FA4350;
        border-radius: 0.25rem;
        font-weight: normal;
        font-size: 0.75rem;
        padding: 0.1rem 0.25rem;
        margin-left: 0.5rem;
      }
    }
  }
}
.share-menu {
  :deep(.wd-action-sheet__popup) {
    .wd-action-sheet {
      margin: 0 !important;
      border-bottom-left-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
      .wd-action-sheet__header {
        .wd-action-sheet__close {
          display: none;
        }
      }
      .wd-action-sheet__panels {
        margin-top: 0 !important;
        .wd-action-sheet__panels-content {
          justify-content: space-around;
        }
      }
    }
  }
}
</style>

<route lang="json5" type="page">
{
style: {
"navigationBarTextStyle": "black"
}
}
</route>
