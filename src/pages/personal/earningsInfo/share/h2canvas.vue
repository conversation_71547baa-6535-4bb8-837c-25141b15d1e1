<script>
// http://mp.weixin.qq.com/s?__biz=MzUzMTMxODY3OQ==&mid=2247493986&idx=1&sn=79483ae89fa0beb3ea6d50770a37cd7c&chksm=fa46fb9acd31728c8f6464be65e1a4172934ccaa5fec6b87cb6ce1ef3c2f85d41ee148ead90a&mpshare=1&scene=1&srcid=0708F4gmYdj5r2SHJ8j58a9b&sharer_shareinfo=3d000a26e341bdc3fb5c7ee5ee8d9822&sharer_shareinfo_first=3d000a26e341bdc3fb5c7ee5ee8d9822#rd
// https://uniapp.dcloud.net.cn/tutorial/app-disagreemode.html#api
// https://uniapp.dcloud.net.cn/api/request/network-file.html#downloadfile
export default {
  data() {
    return {
      txts: '扫码关注-省赚客',
      lineBgHeight: '',
      erweimaPath: '',
      erweimaOnePath: '',
      myImgs: '', // 最终生成的图片地址
      saveImgInfo: '',
      infoMsg: {},
    }
  },
  onShow() {
    this.init()
  },
  methods: {
    // 1. 拿到二维码图片地址 用uni.getImageInfo格式化 存于data
    // 在这里可以换图片二维码 一般来自于接口返回或者本地上传
    init() {
      // todo: 服务端需要给个接口 生成二维码不跨域
      // var erweima = 'https://minico.qq.com/qrcode/get?type=2&r=1&size=300&text=https://szk.juwatech.cn/szk/register?spreadCode=VR8W8B'
      // var erweimaOne = 'https://minico.qq.com/qrcode/get?type=2&r=1&size=300&text=https://szk.juwatech.cn/szk/register?spreadCode=VR8W8B'
      var erweima = 'https://www.h7ml.cn/logo.png'
      var erweimaOne = 'https://www.h7ml.cn/logo.png'
      var that = this
      uni.getImageInfo({
        src: erweima,
        success(res) {
          console.log(JSON.stringify(res))
          that.erweimaPath = res.path
        },
      })
      uni.getImageInfo({
        src: erweimaOne,
        success(res) {
          console.log(JSON.stringify(res))
          that.erweimaOnePath = res.path
        },
      })
    },
    // 2. 生成图片
    setImg() {
      var that = this
      // 背景图片
      var bgc = 'https://szk-001.oss-cn-hangzhou.aliyuncs.com/poster/1.jpg'
      uni.getImageInfo({
        src: bgc,
        success(res) {
          // 创建canvas
          const getWindowInfo = uni.getWindowInfo()
          const winWidth = getWindowInfo.windowWidth // 屏幕宽度
          const bWidth = winWidth / 375
          that.lineBgHeight = parseInt(330 * bWidth)
          const ctx = uni.createCanvasContext('firstCanvas', that)
          // 二维码1 画出图片
          ctx.drawImage(that.erweimaPath, 180 * bWidth, winWidth, 80 * bWidth, 80
          * bWidth)
          setTimeout(() => {
            // var code = '省赚客'
            // ctx.setFontSize(16) // 设置字体大小，默认10
            // ctx.fillStyle = '#ffffff'
            // ctx.fillText(code, 10 * bWidth, 15 * bWidth) // 文字内容、x坐标，y坐标

            // var sizeName = that.txts// 可修改文字
            // ctx.setFontSize(parseInt(14 * bWidth)) // 设置字体大小，默认10
            // ctx.fillStyle = '#ffffff'
            // ctx.fillText(sizeName, 20 * bWidth, 85 * bWidth) // 文字内容、x坐标，y坐标
            // 开始制作
            ctx.draw(false, () => {
              // 使用定时是因为制作水印需要时间，设置定时才不会出bug
              setTimeout(() => {
                // 将画布中内容转成图片，即水印与图片合成
                uni.canvasToTempFilePath({
                  x: 0,
                  y: 0,
                  width: winWidth, // 截取的画布的宽
                  height: 330 * bWidth,
                  destWidth: winWidth * 10,
                  destHeight: parseInt(330 * bWidth)
                    * 10,
                  canvasId: 'firstCanvas',
                  success: (v) => {
                    console.log(`图片临时地址${
                      v.tempFilePath}`)
                    // 把绘制的地址存于data中 以供下载时传给下载函数
                    that.myImgs = v.tempFilePath
                    that.infoMsg.tempFilePath = v.tempFilePath
                  },
                  fail: (e) => {
                    that.infoMsg.drawError = e.errMsg
                    console.log('🚀 ~ setTimeout ~ e:', e)
                  },
                }, that)
              }, 200)
            })
          }, 500)
        },
      })
    },
    changeName(url, newFileName) {
      return new Promise((resolve) => {
        plus.io.resolveLocalFileSystemURL(url, (entry) => {
          entry.getParent((_oldFile) => {
            entry.moveTo(_oldFile, `/${newFileName}`, (newFilePath) => {
              console.log('newFilePath', newFilePath.fullPath)
              resolve(newFilePath.fullPath)
            })
          })
        })
      })
    },
    getTimeStamp() {
      return new Date().getTime()
    },
    // 保存图片
    saveImage(url) {
      var that = this
      uni.showLoading({
        title: '下载中...',
      })

      const name = `${that.getTimeStamp()}.png`
      uni.downloadFile({
        url,
        async success(res) {
          if (res) {
            console.log('下载成功', res)
            that.infoMsg.downTempFilePath = res.tempFilePath
            // 重命名为当前时间戳.png
            // https://www.cnblogs.com/symlove/p/17665541.html
            that.infoMsg.downTempFilename = name
            const tempFilePath = await that.changeName(res.tempFilePath, name)
            that.infoMsg.changeName = tempFilePath
            console.log('🚀 ~ success ~ tempFilePath:', tempFilePath)
            uni.hideLoading()
            uni.saveImageToPhotosAlbum({
              filePath: tempFilePath,
              // filePath: res.tempFilePath,
              success(res) {
                that.infoMsg.savePath = res.tempFilePath
                console.log(res)
                uni.showToast({
                  icon: 'none',
                  title: '保存成功',
                })
              },
              fail(res) {
                console.log(res)
                that.infoMsg.saveError = res.errMsg
                that.saveImgInfo = res.errMsg
                uni.showToast({
                  icon: 'none',
                  title: '保存失败',
                })
              },
            })
          }
        },
        fail: (err) => {
          if (err) {
            uni.showToast({
              icon: 'none',
              title: '下载失败',
            })
            console.log('下载失败', err)
            that.saveImgInfo = res.errMsg
            uni.hideLoading()
          }
        },
      })
    },
  },
}
</script>

<template>
  <view class="">
    <canvas
      style="background: url('https://szk-001.oss-cn-hangzhou.aliyuncs.com/poster/1.jpg') no-repeat; background-size: 100%"
      class="h-80vh w100vw"
      canvas-id="firstCanvas"
    />
    <view block>
      <wd-button @click="setImg()">
        生成海报
      </wd-button>
      <wd-button @click="saveImage(myImgs)">
        保存海报
      </wd-button>
    </view>
    <view v-if="saveImgInfo">
      {{ saveImgInfo }}
    </view>
    <wd-card>
      图片临时地址: <wd-textarea v-model="infoMsg.tempFilePath" />
      drawError:  <wd-textarea v-model="infoMsg.drawError" />
      downTempFilePath: <wd-textarea v-model="infoMsg.downTempFilePath" />
      downTempFilename: <wd-textarea v-model="infoMsg.downTempFilename" />
      changeName: <wd-textarea v-model="infoMsg.changeName" />
      savePath: <wd-textarea v-model="infoMsg.savePath" />
      saveImgInfo: <wd-textarea v-model="infoMsg.saveError" />
    </wd-card>
  </view>
</template>
