<script lang="ts" setup>
import { useMessage, useToast } from 'wot-design-uni'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { useGoodsStore } from '@/store/goods'
import { batchDeleteCollect, batchSearchCollect } from '@/services'
import GoodsCard2 from '@/components/goods-card/goods-card2.vue'

const message = useMessage()
const { show: showToast } = useToast()
const { setGoods } = useGoodsStore()
const favorites = ref<appGoodsCollectBatchSearchCollect.ResponseData[]>([])

const goodListPagination = reactive({
  index: 0,
  num: 20,
})
const goodsListLoadStatus = ref<number>(0)
const state = ref<LoadMoreState>('loading')
const loadingShow = ref<boolean>(false)
// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    handlesearchCollect()
  }
})

// todo: 优化分页下拉加载
onMounted(async () => {
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0)
    await handlesearchCollect()
})

async function handlesearchCollect(fresh = false) {
  if (goodsListLoadStatus.value === 1) {
    console.log('[favorites goods] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const Limit = goodListPagination.num
  let PageNo = goodListPagination.index + 1
  if (fresh)
    PageNo = 1
  try {
    const data = await batchSearchCollect({
      pageNo: PageNo,
      limit: Limit,
    })
    goodsListLoadStatus.value = 0

    if (!data || !data.list || data.list.length <= 0) {
      console.log('[favorites goods] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        favorites.value = []
      }
      if (favorites.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (data.list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh) {
      favorites.value = data.list || []
    }
    else {
      favorites.value = favorites.value.concat(data.list)
    }

    goodListPagination.index = PageNo
    goodListPagination.num = Limit
  }
  catch (err) {
    favorites.value = []
    goodsListLoadStatus.value = 3
  }
}

async function goodListClickHandle({ index }: { index: number }) {
  const goodsDetails = favorites.value[index]
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

function handleClickLeft() {
  uni.navigateBack()
}

const mangText = ref<string>('管理')
const openManage = ref<boolean>(false)
const selectIds = ref<string[]>([])
const selectAll = ref<boolean>(false)

function handleSelect() {
  selectAll.value = selectIds.value.length == favorites.value.length
}

function handleSelectAll() {
  if (selectAll.value) {
    favorites.value.forEach((item) => {
      if (!selectIds.value.includes(item.channelGoodsId)) {
        selectIds.value.push(item.channelGoodsId)
      }
    })
  }
  else {
    selectIds.value = []
  }
}

function handleManage() {
  if (openManage.value) {
    mangText.value = '管理'
    openManage.value = false
  }
  else {
    mangText.value = '完成'
    openManage.value = true
    selectIds.value = []
    selectAll.value = false
  }
}

async function cancelCollect() {
  message
    .confirm({
      title: `确认取消？`,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      zIndex: 101,
      closeOnClickModal: false,
    })
    .then(async (resp) => {
      loadingShow.value = true
      try {
        await batchDeleteCollect({
          ids: selectIds.value.join(),
        })
        // showToast('取消成功')
        mangText.value = '管理'
        openManage.value = false
        await handlesearchCollect(true)
        loadingShow.value = false
      }
      catch (e) {
        console.log('批量取消收藏失败', e)
        loadingShow.value = false
        showToast('取消失败')
      }
    })
    .catch((error) => {
      console.log('error', error)
    })
}
</script>

<template>
  <overlay-search-wraper :use-message-box="false" :use-overlay-search="false">
    <!-- loading -->
    <wd-overlay :show="loadingShow" :z-index="102" class="lloading">
      <view class="h-full flex items-center justify-center">
        <wd-loading />
      </view>
    </wd-overlay>
    <view class="nav-bar">
      <wd-navbar title="我的收藏" left-arrow :bordered="false" @click-left="handleClickLeft">
        <template #right>
          <text @click="handleManage">
            {{ mangText }}
          </text>
        </template>
      </wd-navbar>
    </view>
    <wd-checkbox-group v-model="selectIds" @change="handleSelect">
      <view class="favorites min-h-screen bg-#edf2f5">
        <view v-for="(item, index) in favorites" :key="index" class="favlist-item">
          <view v-if="openManage" class="ml-0.5rem mr-1rem">
            <wd-checkbox :model-value="item.channelGoodsId" />
          </view>
          <goods-card2 :data="item" @item-click="goodListClickHandle({ index })" />
        </view>
        <!-- loading -->
        <wd-loadmore v-if="goodsListLoadStatus == 1" :state="state" @reload="batchSearchCollect({ limit: 10, pageNo: 1 })" />
        <!-- no data -->
        <wd-status-tip v-if="goodsListLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
      </view>
    </wd-checkbox-group>
    <view v-if="openManage" class="botmobtn">
      <view class="flex justify-between p-0.5rem">
        <view class="flex items-center">
          <wd-checkbox v-model="selectAll" @change="handleSelectAll">
            全选
          </wd-checkbox>
        </view>
        <view class="text-right">
          <wd-button size="small" class="text-sm" @click="cancelCollect">
            取消收藏
          </wd-button>
        </view>
      </view>
    </view>
  </overlay-search-wraper>
</template>

<style lang="scss" scoped>
.lloading {
  background: transparent;
}
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}
.favorites {
  padding: calc(var(--status-bar-height) + 4rem) 0.5rem 0.5rem;

  .favlist-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
  }

  .favlist-item:first-child {
    margin-top: 0;
  }
}
.botmobtn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
}
::v-deep .wd-checkbox {
  margin-bottom: 0;
}
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
