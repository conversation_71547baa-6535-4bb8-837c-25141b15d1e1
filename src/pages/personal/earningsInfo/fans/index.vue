<script lang="ts" setup>
/* eslint-disable */
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { queryFansList } from '@/services'

const listPagination = reactive({
  index: 0,
  num: 20,
})
const fans = ref<appUserMyfans.ResponseData['list']>([])

const listLoadStatus = ref<number>(0)
const state = ref<LoadMoreState>('loading')
// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

async function handleSearchFans(fresh = false) {
  if (listLoadStatus.value === 1) {
    console.log('[search fans] 正在请求中~~~')
    return false
  }
  listLoadStatus.value = 1

  const Limit = listPagination.num
  let PageNo = listPagination.index + 1
  if (fresh)
    PageNo = 1
  try {
    const data = await queryFansList({
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
    })
    listLoadStatus.value = 0

    if (!data || !data.list || data.list.length <= 0) {
      console.log('[search fans] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        fans.value = []
      }
      if (fans.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (data.list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      fans.value = data.list || []
    else
      fans.value = fans.value.concat(data.list)

    // 随机头像
    fans.value?.map((v, i) => {
      v.fans_img = `/static/avatar/${getRandomInt(1, 250)}.jpg`
    })

    listPagination.index = PageNo
    listPagination.num = Limit
  }
  catch (err) {
    fans.value = []
    listLoadStatus.value = 3
  }
}

function getRandomInt(min: number, max: number) {
  min = Math.ceil(min)
  max = Math.floor(max)
  return Math.floor(Math.random() * (max - min + 1)) + min
}

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', listLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (listLoadStatus.value === 0 && noMoreData.value === 0) {
    handleSearchFans()
  }
})

onMounted(() => {
  handleSearchFans()
})

function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="nav-bar">
    <wd-navbar title="我的粉丝" left-arrow :bordered="false" @click-left="handleClickLeft" />
  </view>
  <view class="fans min-h-screen bg-#edf2f5 px-0.5rem">
    <view v-for="(f, n) in fans" :key="n" class="fins-list-item rounded-lg bg-#fff">
      <view class="info flex items-center">
        <view>
          <wd-img class="avatar mr-2" mode="scaleToFill" :src="f.fans_img" />
        </view>
        <view>
          <text>{{ f.fans_nick }}</text>
        </view>
        <view class="ml-0.5rem">
          <wd-tag class="mr-0.5rem" type="success" v-if="f.user_enable">正常</wd-tag>
          <wd-tag class="mr-0.5rem" v-if="!f.user_enable">禁用</wd-tag>
        </view>
      </view>
      <view class="time flex justify-between">
        <view class="flex flex-col items-center">
          <text>{{ f.create_time }}</text>
          <text class="mt-0.25rem text-gray-400">
            注册日期
          </text>
        </view>
        <view class="flex flex-col items-center">
          <text v-if="f.enabled" class="color-#34D19D">
            已激活
          </text>
          <text v-else>
            未激活
          </text>
          <text class="mt-0.25rem text-gray-400">
            激活状态
          </text>
        </view>
        <view class="flex flex-col items-center">
          <!--<text>Lv{{ f.tk_user_level }}</text>-->
          <text v-if="f.tk_user_level == 1">初级</text>
          <text v-if="f.tk_user_level == 2">钻石</text>
          <text v-if="f.tk_user_level == 3">超级</text>
          <text class="mt-0.25rem text-gray-400">
            用户等级
          </text>
        </view>
      </view>
    </view>
    <!-- loading -->
    <wd-loadmore v-if="listLoadStatus == 1" :state="state" @reload="handleSearchFans" />
    <!-- no data -->
    <wd-status-tip v-if="listLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nocontent.png" tip="暂无粉丝" />
  </view>
</template>

<style lang="scss" scoped>
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}
.fans {
  padding: calc(var(--status-bar-height) + 4rem) 0.5rem 0.5rem;
  .fins-list-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    .info {
      border-bottom: 1px solid #eee;
      padding-bottom: 0.5rem;
      .avatar {
        width: 3rem;
        height: 3rem;
        overflow: hidden;
        border-radius: 50%;
        border: 1px solid #edf2f5;
      }
      //.wd-tag {
      //  padding: 0.25rem 0.5rem;
      //  color: #fff;
      //  //background: #dd524d;
      //  border-radius: 0.25rem;
      //}
    }
    .time {
      padding-top: 0.5rem;
    }
  }
}
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
