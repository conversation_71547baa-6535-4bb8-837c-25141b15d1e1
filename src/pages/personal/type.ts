interface MenuItem {
  title: string
  tit: string
  url: string
  type: string
  icon?: string
}

export interface orderTag {
  title: string
  iconName: string
  orderNum: number
  tabType: number
  status: number
}

export interface totalTag {
  title: string
  value: number
}
export const menuData: MenuItem[][] = [
  [
    {
      title: '收货地址',
      tit: '',
      url: '',
      type: 'address',
    },
    {
      title: '优惠券',
      tit: '',
      url: '',
      type: 'coupon',
    },
    {
      title: '积分',
      tit: '',
      url: '',
      type: 'point',
    },
  ],
  [
    {
      title: '帮助中心',
      tit: '',
      url: '',
      type: 'help-center',
    },
    {
      title: '客服热线',
      tit: '',
      url: '',
      type: 'service',
      icon: 'service',
    },
    {
      title: '退出登录',
      tit: '',
      url: '/pages/login/index',
      type: 'logout',
      icon: '',
    },
  ],
]

export const orderTagInfos: orderTag[] = [
  {
    title: '邀请',
    iconName: 'share',
    orderNum: 0,
    tabType: 5,
    status: 0,
  },
  {
    title: '待发货',
    iconName: 'clear',
    orderNum: 0,
    tabType: 10,
    status: 0,
  },
  {
    title: '待收货',
    iconName: 'a-precisemonitor',
    orderNum: 0,
    tabType: 40,
    status: 0,
  },
  {
    title: '待评价',
    iconName: 'tips',
    orderNum: 0,
    tabType: 60,
    status: 0,
  },
  {
    title: '退款/售后',
    iconName: 'money-circle',
    orderNum: 0,
    tabType: 0,
    status: 0,
  },
]

export const totalTagInfos: totalTag[] = [
  {
    title: '今日预估',
    value: 0,
  },
  {
    title: '本月预估',
    value: 0,
  },
  {
    title: '本月收货',
    value: 0,
  },
  {
    title: '待结算',
    value: 0,
  },
]
