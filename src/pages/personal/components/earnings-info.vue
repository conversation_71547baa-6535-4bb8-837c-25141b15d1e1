<script lang="ts" setup>
import { hideTeam } from '@/services'
import { appInfo } from '@/common'

const emit = defineEmits(['click'])
function handleClick(contact: { name: string }) {
  emit('click', { ...contact })
}

const infos1: [] = ref(
  [
    { name: '我的订单', icon: 'i-carbon-order-details', img: '/static/user/order.png', link: 'order', module: 'earningsInfo' },
    { name: '提现记录', icon: 'i-carbon-user-withdraw', img: '/static/user/txrecord.png', link: 'withdrawalRecord', module: 'userProfile' },
    { name: '我的粉丝', icon: 'i-carbon-user-favorite', img: '/static/user/fans.png', link: 'fans', module: 'earningsInfo' },
    { name: '我的收藏', icon: 'i-carbon-favorite-filled', img: '/static/user/favorite.png', link: 'favorites', module: 'earningsInfo' },
    { name: '我要分享', icon: 'i-carbon-share', img: '/static/user/share.png', link: 'share', module: 'earningsInfo' },
  ],
)
const infos2: [] = ref(
  [
    { name: '我的订单', icon: 'i-carbon-order-details', img: '/static/user/order.png', link: 'order', module: 'earningsInfo' },
    { name: '提现记录', icon: 'i-carbon-user-withdraw', img: '/static/user/txrecord.png', link: 'withdrawalRecord', module: 'userProfile' },
    // { name: '我的粉丝', icon: 'i-carbon-user-favorite', img: '/static/user/fans.png', link: 'fans', module: 'earningsInfo' },
    { name: '我的收藏', icon: 'i-carbon-favorite-filled', img: '/static/user/favorite.png', link: 'favorites', module: 'earningsInfo' },
    { name: '我要分享', icon: 'i-carbon-share', img: '/static/user/share.png', link: 'share', module: 'earningsInfo' },
  ],
)
const infos: [] = ref([])

onMounted(async () => {
  const d = await hideTeam({
    current_version: appInfo.version,
  })
  console.log('d', d.data)
  if (d.data && d.data === 'hide') {
    infos.value = infos2.value
  }
  else {
    infos.value = infos1.value
  }
})
</script>

<template>
  <view class="basinfo">
    <wd-card title="基础信息" custom-class="basic-uinfo">
      <view class="mx-1rem flex items-center justify-between">
        <view v-for="info in infos" :key="info.name" class="flex flex-col items-center text-center" @click="handleClick(info)">
          <!-- <i :class="info.icon" class="icon mx-auto h-12 w-12" /> -->
          <wd-img width="2rem" height="2rem" :src="info.img" />
          <text class="mt-0.5rem overflow-hidden text-ellipsis whitespace-nowrap text-0.75rem text-gray-500">
            {{ info.name }}
          </text>
        </view>
      </view>
    </wd-card>
  </view>
</template>

<style lang="scss" scoped>
  .basinfo {
    .basic-uinfo {
      margin: 0 0.5rem 0.5rem;
      padding: 0;
      ::v-deep .wd-card__title-content {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
      }
    }
  }
</style>

<route type="page" lang="json">
{}
</route>
