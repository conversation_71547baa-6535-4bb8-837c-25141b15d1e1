<script lang="ts" setup>
/* eslint-disable */
import { reactive, ref } from 'vue'
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { useGoodsStore } from '@/store/goods'
import { queryGoodsList } from '@/services'

const { setGoods } = useGoodsStore()
const state = ref<LoadMoreState>('loading')

const goods = ref<any[]>([] as any[])
const goodsListLoadStatus = ref<number>(0)

// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

const goodListPagination = reactive({
  index: 0,
  num: 20,
})

onMounted(async () => {
  await init()
})

async function init() {
  await loadGoodsList()
}

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    loadGoodsList() // 调用加载更多的函数
  }
})

function onReTry() {
  loadGoodsList()
}

async function loadGoodsList(fresh = false) {
  if (goodsListLoadStatus.value === 1) {
    console.log('[freeshipping goods] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const Limit = goodListPagination.num
  let PageNo = goodListPagination.index + 1
  if (fresh)
    PageNo = 1

  try {
    const { list = [] } = await queryGoodsList({
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
      platformCode: 'JD',
      queryType: 'list',
      queryValue: '4'
    })
    goodsListLoadStatus.value = 0

    if (!list || list.length <= 0) {
      console.log('[freeshipping goods] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        goods.value = []
      }
      if (goods.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      goods.value = list || []
    else if (list && list.length > 0)
      goods.value = goods.value.concat(list) // 追加新加载的商品

    goodListPagination.index = PageNo // 更新当前页码
    goodListPagination.num = Limit
    // nextTick(async () => {
    // if (fresh)
    // await getElementScollTop('.freeshipping-main-tabs-content')
    // })
  }
  catch (err) {
    console.error('[freeshipping goods] 首页加载失败', err)
    goods.value = []
    goodsListLoadStatus.value = 3
  }
}

function goodListClickHandle({ index }: { index: number }) {
  const goodsDetails = goods.value[index]
  console.log(index, goodsDetails)
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}
</script>

<template>
  <view class="freeship">
    <view class="freeship-cont px2">
      <goods-list :goods="goods" @click="goodListClickHandle" />
    </view>
    <!-- loading -->
    <wd-loadmore :state="state" @reload="onReTry" />
    <!-- no data -->
    <wd-status-tip v-if="goodsListLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
  </view>
</template>

<style lang="scss" scoped>

</style>

<route type="freeshipping" lang="json">
{}
</route>
