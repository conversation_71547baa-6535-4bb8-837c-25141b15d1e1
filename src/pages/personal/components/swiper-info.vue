<script lang="ts" setup>
import {
  queryBannerList,
} from '@/services'

const swiperList = ref([])
const bannerFooter = ref([])
onMounted(async () => {
  const result = await queryBannerList({ position: 'personalCenter_down' })
  if (result)
    bannerFooter.value = result
})

watch(bannerFooter, (newBannerFooter) => {
  if (newBannerFooter.length > 0)
    swiperList.value = newBannerFooter.map(item => item.picture_url)
}, { immediate: true })

function handleClick(e: { index: string | number }) {
  try {
    const banner = bannerFooter.value[e.index]
    uni.navigateTo({
      url: `/pages/iframe?url=${encodeURIComponent(banner.jump_url)}&name=${banner.categoryname}`,
    })
  }
  catch (e) {
    console.log(e)
  }
}
function onChange(e) {
  console.log(e)
}
</script>

<template>
  <view class="swiper-info border-rd-4px">
    <wd-swiper height="6rem" class="lg:hidden" :list="swiperList" autoplay :current="0" image-mode="scaleToFill" @click="handleClick" @change="onChange" />
  </view>
</template>

<style lang="scss" scoped>
  .swiper-info {
    padding: 0 0.5rem 0.5rem;
  }
</style>

<route lang="json5" type="page">
{
  style: { navigationBarTitleText: 'swiper-info' },
}
</route>
