<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { appInfo } from '@/common'

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
})

const message = useMessage()

function handleWithdraw() {
  if (props.userInfo.token === appInfo.defaultTk) {
    uni.navigateTo({
      url: '/pages/login/index',
    })
  }
  // 检测是否绑定支付宝
  else if (props.userInfo.aliPayAccount) {
    uni.navigateTo({
      url: '/pages/personal/userProfile/withdraw/index',
    })
  }
  else {
    message
      .confirm({
        msg: '亲，您需要绑定支付宝才能体现哦',
        title: '体现提示',
        confirmButtonText: '去绑定',
        cancelButtonText: '下次吧',
        zIndex: 101,
        closeOnClickModal: false,
      })
      .then(() => {
        uni.navigateTo({
          url: '/pages/personal/userProfile/bindAlipay/index',
        })
      })
      .catch(() => {
        console.log('点击了取消按钮')
      })
  }
}
</script>

<template>
  <view class="moneyinfo moneyinfo-bg bg-cover bg-center">
    <view class="content px-0.5rem">
      <view class="balance-info flex rounded-md bg-[#373F57] p-1rem text-#fff">
        <view class="left-content flex-4 flex flex-col text-left">
          <view class="align-left text-1.2rem">
            <text text-1.5rem>
              {{ userInfo?.balance }}
            </text> 元
            <text text-xs>
              账户余额
            </text>
          </view>
          <text class="no-wrap mt-1rem text-xs text-[#FFD7A1]">
            每月25日，结算上月联盟已结算的订单佣金
          </text>
        </view>
        <view class="right-content flex flex-1 items-center justify-end">
          <text class="withdraw-button text-xs text-[#FFD7A1]" @click="handleWithdraw">
            去提现
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.moneyinfo-bg{
  background:url('../../../static/user/profile_bg.png');
}
:deep(.moneyinfo) {
  padding-top: calc(var(--status-bar-height) + 5.5rem);
  padding-bottom: 0.5rem;

  .balance-info {
    text-align: center;
    margin-top: 1rem;

    .balance {
      font-size: 24px;
      font-weight: bold;
    }

    .description {
      color: #888;
    }

    .withdraw-button {
      margin-top: 8px;
      padding: 8px;
      border:1px solid #FFD7A1;
      border-radius: 18px;
    }
  }
}
</style>

<route type="page" lang="json">
{}
</route>
