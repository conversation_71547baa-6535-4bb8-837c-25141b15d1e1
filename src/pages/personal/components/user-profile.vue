<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { setUserInfo as setSuperInfo } from '@/services'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => {},
  },
})
const message = useMessage()

onMounted(async () => {
  // 为null、为''代表没弹过，为0代表弹过，但是用户选了没有邀请人
  if (props.userInfo.superiorTkUserId == null || props.userInfo.superiorTkUserId === '') {
    await prompt()
  }
})

async function prompt() {
  message
    .prompt({
      title: '填写邀请码',
      inputValue: props.userInfo.superiorTkUserId,
      cancelButtonText: '无人邀请',
      zIndex: 101,
    })
    .then((resp) => {
      return setSuperiorTkUserId(resp.value)
    })
    .catch(() => {
      setSuperiorTkUserId('0')
    })
}

async function setSuperiorTkUserId(superiorTkUserId: string) {
  await setSuperInfo({
    data: superiorTkUserId,
    type: 'inviteCode',
  })
}

const hiddenInviteCode = ref<boolean>(true)
function handleServer(type: 'service' | 'setting') {
  if (type === 'service') {
    const url = `/pages/personal/material?materialId=169&name=客服`
    uni.navigateTo({ url })
  }
  else {
    uni.navigateTo({
      url: `/pages/setting/index`,
    })
  }
}

function handleCopy(text: string) {
  setClipboardData(text)
}

function clickToLogin() {
  if (props.userInfo.fansNick === '请登录APP') {
    uni.navigateTo({
      url: '/pages/login/index',
    })
  }
}
</script>

<template>
  <view class="user-profile user-profile-bg bg-cover bg-center">
    <view class="content px-0.5rem">
      <view class="flex items-center rounded-md">
        <view class="w-full flex justify-between">
          <view class="h-16 flex">
            <!-- <wd-badge :model-value="`s${userInfo?.tkUserLevel}`" bg-color="bg-[#FDD0AE]" custom-class="rounded-sm"> -->
            <wd-img class="avatar mr-2" mode="scaleToFill" :src="`/static/avatar/${userInfo?.fansImg}`">
              <template #error>
                <view class="error-wrap">
                  <image class="mr-2" src="/static/tuiguan/ren2.png" />
                </view>
              </template>
            </wd-img>
            <!-- </wd-badge> -->
            <view style="padding-top: 0.5rem">
              <text class="block text-lg font-bold" @click="clickToLogin">
                {{ userInfo?.fansNick }}
              </text>
              <text class="block text-gray-500" style="font-size:0.8rem;margin-top: 0.2rem;">
                邀请码：
                <text v-if="!hiddenInviteCode">
                  ******
                </text>
                <text v-else-if="hiddenInviteCode" @click="handleCopy(userInfo?.vipInviteCode || userInfo?.inviteCode)">
                  {{ userInfo?.vipInviteCode || userInfo?.inviteCode }}
                </text>
                <!-- <text style="text-decoration:underline;margin-left:1.5rem;" @click="handleCopy(userInfo?.vipInviteCode || userInfo?.inviteCode)">
                  复制
                </text> -->
                <wd-icon :name="hiddenInviteCode ? 'view' : 'eye-close'" custom-class="view-close" @click="hiddenInviteCode = !hiddenInviteCode" />
              </text>
            </view>
          </view>
          <view class="flex flex-col items-end justify-around space-x-4">
            <!-- <wd-icon name="setting1" size="22px" @click="handleServer('setting')" /> -->
            <image class="h-6 w-6" src="../../../static/set1.png" @click="handleServer('setting')" />
            <wd-tag v-if="userInfo?.tkUserLevel === 1" color="#48CFFF" bg-color="#48CFFF" plain>
              初级达人
            </wd-tag>
            <wd-tag v-if="userInfo?.tkUserLevel === 2" color="#FFCF65" bg-color="#FFCF65" plain>
              钻石达人
            </wd-tag>
            <wd-tag v-if="userInfo?.tkUserLevel === 3" color="#DD524D" bg-color="#DD524D" plain>
              超级达人
            </wd-tag>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.user-profile-bg{
  background:url('../../../static/user/profile_bg.png');
}
:deep(.user-profile) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  padding-top: calc(var(--status-bar-height) + 1.5rem);
  padding-bottom: 0.5rem;

  .avatar {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    overflow: hidden;
    background-color: #dd524d;
    image {
      width: 4rem;
      height: 4rem;
    }
  }
  .wd-card__title-content {
    display: none !important;
  }
  .user-center-header {
    background-image: url(https://cdn-we-retail.ym.tencent.com/miniapp/template/user-center-bg-v1.png);
    background-size: cover;
    background-repeat: no-repeat;
  }

  .view-close {
    font-size: 1.2rem;
    color: #000000;
    margin-left: 0.5rem;
  }

  .wd-badge__content[data-v-1eb01bf6] {
    display: inline-block;
    height: var(--wot-badge-height, 12px);
    line-height: var(--wot-badge-height, 12px);
    padding: var(--wot-badge-padding, 0 3px);
    background-color: var(--wot-badge-bg, var(--wot-color-danger, #fa4350));
    border-radius: calc(var(--wot-badge-height, 16px) / 2 + 2px);
    color: var(--wot-badge-color, #fff);
    font-size: var(--wot-badge-fs, 8px);
    text-align: center;
    white-space: nowrap;
    border: var(--wot-badge-border, 1px solid var(--wot-badge-color, #fff));
  }

  .header {
    margin-top: 20px;
    margin-bottom: 48rpx;
    height: 96rpx;
    line-height: 48rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #333;
    position: relative;

    &__name {
      font-size: 36rpx;
      line-height: 48rpx;
      color: #333;
      font-weight: bold;
      margin-left: 24rpx;
      margin-right: 16rpx;
    }

    &__transparent {
      position: absolute;
      left: 0;
      top: 0;
      background-color: transparent;
      height: 100%;
      width: 100%;
    }
  }

  .user-center-card__icon {
    line-height: 96rpx;
  }
}
</style>

<route type="page" lang="json">
{}
</route>
