<script lang="ts" setup>
import { hideTeam } from '@/services'
import { appInfo } from '@/common'

const emit = defineEmits(['click'])
function clickHandle(contact: any) {
  emit('click', { module: 'commonFunctions', ...contact })
}

const functions1: [] = ref(
  [
    { name: '达人升级', icon: 'i-carbon-direction-curve-filled', img: '/static/user/upgrades.png', link: 'upgrades' },
    // { name: '改邀请码', icon: 'i-carbon-edit', img: '/static/user/InvitationCode.png', link: 'InvitationCode', materialId: '175' },
    { name: '批量转链', icon: 'i-carbon-text-link', img: '/static/user/batchChainTurning.png', link: 'turnChain', module: 'search' },
    { name: '新手教程', icon: 'i-carbon-document-signed', img: '/static/user/beginnerTutorial.png', link: 'beginnerTutorial', materialId: '174' },
    { name: '常见问题', icon: 'i-carbon-help-filled', img: '/static/user/frequentlyAskedQuestions.png', link: 'frequentlyAskedQuestions' },
    { name: '关于我们', icon: 'i-carbon-map-boundary-vegetation', img: '/static/user/aboutUs.png', link: 'aboutUs', materialId: '170' },
    { name: '专属客服', icon: 'i-carbon-customer-service', img: '/static/user/dedicatedCustomerService.png', link: 'dedicatedCustomerService', materialId: '169' },
    { name: '商务合作', icon: 'i-carbon-ibm-cloud-security-compliance-center-workload-protection', img: '/static/user/extensionNorm.png', link: 'businessCooperation', materialId: '168' },
    { name: '隐私政策', icon: 'i-carbon-edit', img: '/static/user/privacy.png', link: 'InvitationCode', materialId: '171' },
    { name: '推广规范', icon: 'i-carbon-catalog-publish', img: '/static/user/businessCooperation.png', link: 'extensionNorm', materialId: '167' },
    { name: '建议反馈', icon: 'i-carbon-pricing-quick-proposal', img: '/static/user/suggestionFeedback.png', link: 'suggestionFeedback' },
  ],
)
const functions2: [] = ref(
  [
    // { name: '达人升级', icon: 'i-carbon-direction-curve-filled', img: '/static/user/upgrades.png', link: 'upgrades' },
    // { name: '改邀请码', icon: 'i-carbon-edit', img: '/static/user/InvitationCode.png', link: 'InvitationCode', materialId: '175' },
    { name: '批量转链', icon: 'i-carbon-text-link', img: '/static/user/batchChainTurning.png', link: 'turnChain', module: 'search' },
    { name: '新手教程', icon: 'i-carbon-document-signed', img: '/static/user/beginnerTutorial.png', link: 'beginnerTutorial', materialId: '174' },
    { name: '常见问题', icon: 'i-carbon-help-filled', img: '/static/user/frequentlyAskedQuestions.png', link: 'frequentlyAskedQuestions' },
    { name: '关于我们', icon: 'i-carbon-map-boundary-vegetation', img: '/static/user/aboutUs.png', link: 'aboutUs', materialId: '170' },
    { name: '专属客服', icon: 'i-carbon-customer-service', img: '/static/user/dedicatedCustomerService.png', link: 'dedicatedCustomerService', materialId: '169' },
    { name: '商务合作', icon: 'i-carbon-ibm-cloud-security-compliance-center-workload-protection', img: '/static/user/extensionNorm.png', link: 'businessCooperation', materialId: '168' },
    { name: '隐私政策', icon: 'i-carbon-edit', img: '/static/user/privacy.png', link: 'InvitationCode', materialId: '171' },
    { name: '推广规范', icon: 'i-carbon-catalog-publish', img: '/static/user/businessCooperation.png', link: 'extensionNorm', materialId: '167' },
    { name: '建议反馈', icon: 'i-carbon-pricing-quick-proposal', img: '/static/user/suggestionFeedback.png', link: 'suggestionFeedback' },
  ],
)
const functions: [] = ref([])

onMounted(async () => {
  const d = await hideTeam({
    current_version: appInfo.version,
  })
  console.log('d', d.data)
  if (d.data && d.data === 'hide') {
    functions.value = functions2.value
  }
  else {
    functions.value = functions1.value
  }
})
</script>

<template>
  <wd-card title="常用功能" custom-class="common-service">
    <view class="grid grid-cols-5">
      <view
        v-for="(func, index) in functions" :key="func.name" class="flex flex-col items-center text-center"
        :class="{ 'mt-1rem': index >= 5 }"
        @click="clickHandle(func)"
      >
        <!-- <i :class="func.icon" class="icon mx-auto mb-2 h-12 w-12" /> -->
        <wd-img width="2rem" height="2rem" :src="func.img" />
        <text class="mt-0.5rem overflow-hidden text-ellipsis whitespace-nowrap text-0.75rem text-gray-500">
          {{ func.name }}
        </text>
      </view>
    </view>
  </wd-card>
</template>

<style lang="scss" scoped>
  .common-service {
    margin: 0 0.5rem 0.5rem;
    padding: 0;
    ::v-deep .wd-card__title-content {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }
  }
</style>

<route type="page" lang="json">
{}
</route>
