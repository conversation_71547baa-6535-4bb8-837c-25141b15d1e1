<script setup lang="ts">
import { usePage } from '@uni-helper/uni-use'
import { appMaterialQueryById } from '@/services'

// 获取页面实例
const page = usePage()
const materialId = ref('')
const title = ref('')
const appMaterial = ref<string>('')
const currentPageUrl = ref<string>('')
const styleCustom = ref<string>('')

// 获取页面参数
onLoad((options: any) => {
  if (options) {
    materialId.value = options.materialId
    title.value = options.name
  }
})

// 页面加载时查询材料
onMounted(async () => {
  const query = uni.getStorageSync('pageQuery')
  if (query) {
    materialId.value = query.materialId
    title.value = query.name
  }
  await fetchMaterial()
})
/**
 * 格式化富文本
 * @param html 富文本内容
 * @returns 格式化后的富文本内容
 */
function formatRichText(html: string) {
  let newContent = html.replace(/<[^>]*>/g, (match) => { // 修复正则表达式
    match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '')
    match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '')
    match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '')
    return match
  })
  newContent = newContent.replace(/style="[^"]+"/gi, (match) => { // 修复重复的替换
    match = match.replace(/width:[^;]+;/gi, 'max-width:100%;')
    return match
  })
  newContent = newContent.replace(/<[^>]*\/>/g, '') // 修复正则表达式
  newContent = newContent.replace(/<img[^>]*src="([^"]*)"[^>]*>/gi, '<img src="$1" style="margin: 0 auto; max-width:100%;height:auto;display:block;margin: 0 auto; text-align: center;" />') // 修复替换逻辑，保留 src 属性并居中
  return newContent
}
// 查询材料的函数
async function fetchMaterial() {
  try {
    const { content = '' } = await appMaterialQueryById(materialId.value)
    styleCustom.value = `
    <style>
    img {
      max-width: 85% !important;
    }
    </style>
    `
    appMaterial.value = formatRichText(content) || '默认内容'
  }
  catch (error) {
    console.error('获取材料失败:', error)
    appMaterial.value = '加载失败，请稍后重试'
  }
}

// 返回上一页
function handleBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="extensionNorm" min-h-screen>
    <view class="nav-wrapper">
      <wd-navbar :title="title" left-arrow :bordered="false" @click-left="handleBack" />
    </view>
    <view v-if="materialId && appMaterial" class="cont-wrapper">
      <wd-card>
        <!-- https://www.cnblogs.com/huihuihero/p/12978903.html -->
        <!-- <rich-text space="nbsp" class="appMaterial" :nodes="appMaterial" /> -->
        <rich-text space="nbsp" class="appMaterial" :nodes="appMaterial" />
      </wd-card>
    </view>
    <view v-if="!materialId" text mt-2rem>
      没有可显示的材料内容。
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .extensionNorm {
    max-height: calc(100vh - var(--status-bar-height));
    height: calc(100vh - var(--status-bar-height));
    min-height: calc(100vh - var(--status-bar-height));

    .nav-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 100;
      padding-top: calc(var(--status-bar-height) + 0.5rem);
      //margin-top: calc(var(--status-bar-height));
      background-color: #fff;
    }

    .cont-wrapper {
      padding-top: calc(var(--status-bar-height) + 1.5rem);

      .appMaterial {
        width: 700rpx;
        max-width: 100%;
        height: auto;
        margin: 30rpx auto 80rpx;
        font-size: 28rpx;
        line-height: 40rpx;
      }
    }
  }
</style>

<route lang="json">
{
  "style": {
    "navigationBarTextStyle": "black"
  }
}
</route>
