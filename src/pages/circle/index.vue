<script setup lang="ts">
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import circleList from './circle-list.vue'
import { tabs } from './types'
import { queryFriendsMaterial } from '@/services'

const state = ref<LoadMoreState>('loading')
const tab = ref<number>(0)
const goodListPagination = reactive({
  index: 0,
  num: 20,
})
const privateData = reactive({
  tabIndex: 0,
})
const goodsListLoadStatus = ref<number>(0)
const platformCode = ref('TB')
const materials = ref<
  appCircleMaterialQueryPlatformFriendsMaterial.Response['data']
>([])

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    loadMaterial() // 调用加载更多的函数
  }
})

// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

// 加载发圈素材
async function loadMaterial(fresh = false) {
  if (goodsListLoadStatus.value === 1) {
    console.log('[circle materials] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const limit = goodListPagination.num
  let pageNo = privateData.tabIndex * limit + goodListPagination.index + 1
  if (fresh)
    pageNo = 1

  try {
    const queryParams = {
      limit,
      pageNo,
      type: platformCode.value === 'telegram' ? 0 : undefined,
      platformCode: platformCode.value !== 'telegram' ? platformCode.value : undefined,
    }

    // Remove undefined properties
    Object.keys(queryParams).forEach(key => queryParams[key] === undefined && delete queryParams[key])

    const { data } = await queryFriendsMaterial(queryParams)

    if (!data || !data.list || data.list.length <= 0) {
      console.log('[materials] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        materials.value = []
      }
      if (materials.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (data.list.length < limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      materials.value = data.list || []
    else
      materials.value = materials.value.concat(data.list) // 追加新加载的素材

    goodsListLoadStatus.value = 0
    goodListPagination.index = pageNo // 更新当前页
    goodListPagination.num = limit
  }
  catch {
    materials.value = []
    goodsListLoadStatus.value = 3
  }
}

onLoad(async () => {
  await loadMaterial()
})

function queryChange(e: { index: number }) {
  state.value = 'loading'
  isDataEmpty.value = 0
  noMoreData.value = 0
  platformCode.value = tabs[e.index].platformCode
  loadMaterial(true)
}
</script>

<template>
  <overlay-search-wraper class="circle bg-#edf2f5">
    <view class="circle-tabs min-h-screen">
      <wd-tabs v-model="tab" custom-class="circle-nav-tab" @change="queryChange">
        <block v-for="item in tabs" :key="item">
          <wd-tab :title="`${item.value}`" :name="item.value" />
        </block>
      </wd-tabs>
      <view class="circle-cont">
        <circle-list :materials="materials" :platform-code="platformCode" />
      </view>
      <!-- loading -->
      <wd-loadmore :state="state" @reload="loadMaterial" />
      <!-- no data -->
      <wd-status-tip v-if="goodsListLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
    </view>
  </overlay-search-wraper>
</template>

<route lang="json">
{
  "layout": "page",
  "style": {
    "navigationBarTextStyle": "black"
  }
}
</route>

<style lang="scss" scoped>
.circle-tabs{
  padding-bottom: 1rem;
}

.circle-nav-tab {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}

::v-deep .wd-tabs__nav-container {
  .wd-tabs__nav-item {
    font-size: 16px;
  }

  .is-active {
    color: red
  }
}

.circle-cont {
  margin-top: calc(var(--status-bar-height) + 3.5rem);
}
</style>
