<script lang="ts" setup>
import { useGoodsStore } from '@/store/goods'
import { getGoodsText } from '@/common/goods'
import { friendlyFormatTime } from '@/utils/formatTime'
import { chainTurning } from '@/services'
import { openOthers } from '@/common/jump'
import { setClipboardData } from '@/components/overlay-search/uni-clipboard'

const props = defineProps({
  materials: {
    type: Array,
    default: () => [],
  },
  platformCode: {
    type: String,
    default: '',
  },
})

const show = ref(false)
const picList = ref([])

const { setGoods } = useGoodsStore()

function changeImg(e: any, itemPic: string[], picIndex: number) {
  uni.previewImage({
    urls: itemPic,
    current: e.detail.index,
    loop: true,
  })
}

function formatPrice(price: any): string {
  // console.log('🚀 ~ formatPrice ~ price:', price)
  const numPrice = Number(price)
  if (!isNaN(numPrice)) {
    return numPrice.toFixed(2)
  }
  return '0.00' // 默认值，防止非数字类型导致错误
}

function formatContent(cont: string, defaultCont: string): string {
  if (cont == null || cont.length === 0) {
    return defaultCont
  }

  // 使用正则替换 &lt;br&gt; 为 <br>
  cont = cont.replace(/&lt;br&gt;/gi, '<br>')

  /* #ifdef H5 */
  // 通过 DOMParser 解析 HTML 实体
  const parser = new DOMParser()
  const doc = parser.parseFromString(cont, 'text/html')
  cont = doc.body.innerHTML
  /* #endif */

  return cont
}

function goodListClickHandle(goodsDetails: appGoodsGoodsDetail.ResponseData) {
  console.log('[share click goods]', goodsDetails)
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

function saveMaterials(pics: string[]) {
  // uni.showModal({
  //   title: '提示',
  //   content: '确定保存到相册吗',
  //   success(res) {
  //     if (res.confirm) {
  //       pics.forEach(e => saveSinglePic(e))
  //     }
  //   },
  // })
  picList.value = pics

  const granted = uni.getStorageSync('grant_disk') || false
  if (!granted) {
    console.log('用户未授权存储权限，触发弹窗')
    // uni.hideTabBar()
    show.value = true
    return false
  }
  picList.value.forEach(e => saveSinglePic(e))
}

function saveSinglePic(url: string) {
  uni.saveImageToPhotosAlbum({
    filePath: url,
    success() {
      uni.showToast({
        icon: 'none',
        title: '图片保存成功',
      })
      uni.setStorageSync('grant_disk', true)
    },
    fail(err) {
      console.error('图片保存失败', err.errMsg)
      uni.showToast({
        icon: 'none',
        title: '图片保存失败',
      })
      uni.setStorageSync('grant_disk', false)
    },
  })
}

function copyText(text: string) {
  text = text.replace(/&lt;br&gt;/gi, '')
  setClipboardData(text)
}

async function copyTkl(goods: object) {
  await uni.showLoading({
    mask: true,
  })

  const shareLink = await chainTurning({
    goodsId: goods.id,
    platformCode: goods.platformCode,
  })

  uni.hideLoading()

  if (!shareLink)
    return false

  if (goods.platformCode === 'TB' && shareLink.needAuth) {
    await openOthers(shareLink, goods.platformCode)
    return false
  }

  let templateText = `${goods?.title}
【在售价】${goods?.orgPrice}元
【到手价】${goods?.actualPrice}元
-----------------
`

  if (goods.platformCode === 'TB') {
    templateText += `${shareLink?.goodsUrl}\r\n${shareLink?.shortClickUrl}`
  }
  else if (goods.platformCode === 'DY') {
    templateText += `${shareLink?.goodsUrl}`
  }
  else {
    templateText += `${shareLink?.shortClickUrl}`
  }

  await setClipboardData(templateText, true)
}

async function shareGoods(goods: appGoodsGoodsDetail.ResponseData) {
  console.log('[circle share] goods', goods)
  const data = await chainTurning({
    goodsId: goods.id,
    platformCode: goods.platformCode,
  })
  await openOthers(data, goods.platformCode)
}

async function openDiskAuth() {
  // console.log(picList.value)
  // uni.showTabBar()
  show.value = false
  const granted = uni.getStorageSync('grant_disk')
  if (granted === '' || granted === true) {
    picList.value.forEach(e => saveSinglePic(e))
  }
  else {
    uni.openAppAuthorizeSetting()
  }
}

async function closeDiskAuth() {
  // uni.showTabBar()
  show.value = false
}
</script>

<template>
  <view class="circle-list">
    <view class="circle-content">
      <view v-for="(item, index) in materials" :key="index" class="circle-item rounded-lg">
        <wd-card>
          <template #title>
            <view class="title">
              <view class="flex items-center justify-between">
                <view class="flex items-center">
                  <wd-img v-if="platformCode == 'TB'" width="2rem" height="2rem" round class="mr-2" src="https://szk-001.oss-cn-hangzhou.aliyuncs.com/icon/jg_tbtm.gif" />
                  <wd-img v-if="platformCode == 'JD'" width="2rem" height="2rem" round class="mr-2" src="https://szk-001.oss-cn-hangzhou.aliyuncs.com/icon/jg_jd.png" />
                  <wd-img v-if="platformCode == 'telegram'" width="2rem" height="2rem" round class="mr-2" src="https://szk-001.oss-cn-hangzhou.aliyuncs.com/icon/jg_dxgy.gif" />
                  <view class="user-time">
                    <text class="text-lg">
                      省赚客
                    </text>
                    <text class="text-sm text-gray-500">
                      {{ item.showTime ? friendlyFormatTime(item.showTime) : '' }}
                    </text>
                  </view>
                  <text class="ml-2 text-red-500">
                    #{{ getGoodsText(platformCode, '实时线报') }}
                  </text>
                </view>
                <view style="text-align:right">
                  <wd-button size="small" icon="share" type="error" class="text-sm" @click="shareGoods(item)">
                    去分享
                  </wd-button>
                </view>
              </view>
            </view>
          </template>
          <view class="content">
            <view>
              <view class="text-gray-800">
                <view v-html="formatContent(item?.content, '没有内容')" />
                <view v-if="item?.itemPic" class="mt-2">
                  <uni-grid :column="3" :highlight="true" :square="false" :show-border="false" class="grid-container">
                    <uni-grid-item
                      v-for="(pic, picindex) in item.itemPic"
                      :key="picindex"
                      :index="picindex"
                      class="grid-item"
                      @click="(e) => changeImg(e, item.itemPic, picindex)"
                    >
                      <view v-if="picindex < 9" class="grid-item-box overflow-hidden rounded-lg bg-white" :style="{ height: '24vw', width: '24vw', margin: '1vw' }">
                        <image class="image h-full w-full object-cover" :src="pic" mode="aspectFill" />
                      </view>
                    </uni-grid-item>
                  </uni-grid>
                </view>
              </view>
              <view class="mt-2 flex rounded-lg bg-gray-100 p-2">
                <view class="mr-2 rounded-lg">
                  <wd-img mode="aspectFill" width="5.5rem" height="5.5rem" class="h-full w-full overflow-hidden rounded-lg" :src="item?.picUrl" />
                </view>
                <view class="flex-1" @click="goodListClickHandle(item)">
                  <text class="line-clamp-1 mb-2 text-ellipsis text-gray-800">
                    {{ item?.itemTitle }}
                  </text>
                  <view class="flex justify-between rounded-lg bg-[#fff]">
                    <view class="flex flex-col p-0.5rem text-center">
                      <text text-red-600>
                        ￥{{ formatPrice(item?.actualPrice) }}
                      </text>
                      <text text-0.75rem>
                        劵后价
                      </text>
                    </view>
                    <view class="flex flex-col p-0.5rem text-center">
                      <text text-red-600>
                        ￥{{ formatPrice(item?.couponAmount) }}
                      </text>
                      <text text-0.75rem>
                        优惠券
                      </text>
                    </view>
                    <view class="ygz flex flex-col bg-#fa4350 px-0.5rem text-center">
                      <text>￥{{ formatPrice(item?.maxProfit) }}</text>
                      <text text-0.75rem>
                        预估赚
                      </text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <template #footer>
            <view class="flex justify-between">
              // #ifndef H5
              <wd-button icon="picture" type="text" @click="saveMaterials(item.itemPic)">
                保存素材
              </wd-button>
              // #endif
              <wd-button icon="link" type="text" @click="copyTkl(item)">
                复制口令
              </wd-button>
              <wd-button icon="file-copy" type="text" @click="copyText(item.content)">
                复制文案
              </wd-button>
            </view>
          </template>
        </wd-card>
      </view>
    </view>
  </view>
  <!-- 存储授权弹窗提示 -->
  <wd-popup v-model="show" :close-on-click-modal="false" closable lock-scroll :z-index="102" custom-style="border-radius: 0.5rem" @close="closeDiskAuth">
    <view class="popu w-75vw p-0.5rem">
      <view class="mt-2rem flex justify-center">
        <view class="h-4rem w-4rem overflow-hidden bg-amber" style="border-radius: 50%">
          <wd-img src="/static/logo.png" class="h-full w-full" />
        </view>
      </view>
      <view class="mt-1rem text-center">
        <text class="text-1.05rem">
          省赚客需要以下权限
        </text>
      </view>
      <view class="mt-1rem flex justify-center px-1rem">
        <text class="text-0.85rem text-#333">
          为保证您正常、安全的使用省赚客APP，我们需要向您申请如下权限：
        </text>
      </view>
      <view class="mt-1rem flex px-1rem">
        <view>
          <view class="h-2rem w-2rem flex items-center justify-center bg-#FEE4ED" style="border-radius: 50%">
            <wd-icon name="phone" color="#ED3226" size="20px" />
          </view>
        </view>
        <view class="pl-0.5rem text-0.85rem">
          <view>
            <text class="text-#333">
              获取存储权限
            </text>
          </view>
          <view class="mt-0.5rem">
            <text class="text-#ACACAC">
              为了您能正常保存图片素材，我们需要访问您的存储权限，如您拒绝授权不影响您使用 APP 的其他功能。
            </text>
          </view>
        </view>
      </view>
      <view class="mb-0.5rem mt-1rem px-1rem">
        <wd-button block size="large" @click="openDiskAuth">
          开启权限
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
.circle-content {
  padding: 0.5rem 0;
  .circle-item {
    //min-height: 30rem;
    padding: 0 0.5rem;
  }
  .user-time {
    display: flex;
    flex-direction: column;
  }
  .wdcard-fot {
    text-align: center;
  }
  .wd-button.is-text {
    color: #000000;
  }

  .ygz {
    position: relative;
    color: #fff;
    border-radius: 0.5rem;
    justify-content: center;
  }

  .ygz::before {
    position: absolute;
    left: -0.25rem;
    content: '';
    width: 0;
    height: 0;
    border-top: 10rpx solid transparent;
    border-bottom: 10rpx solid transparent;
    border-right: 10rpx solid #fa4350;
  }
}

:deep(.wd-card) {
  border-radius: 0.5rem;
  margin: 0;
  margin-bottom: 0.5rem;
}

:deep(.wd-card .wd-card__content) {
  padding-top: 0;
  padding-bottom: 0;
}
</style>

<route lang="json5" type="page">
{
style: { navigationBarTitleText: 'circle-list' },
}
</route>
