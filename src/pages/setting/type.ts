export interface MenuItem {
  title: string
  type: string
  value?: string
  img?: string
  icon?: string
  bindKey: string // 绑定的key 解决用户信息接口返回的 key 与 设置用户信息的key 不一致的问题
  canEdit?: boolean
}

export interface orderTag {
  title: string
  iconName: string
  orderNum: number
  tabType: number
  status: number
}

export interface totalTag {
  title: string
  value: number
}
interface MenuGroup {
  title: string
  items: MenuItem[]
}

export const menuData: MenuGroup[] = [
  {
    title: '基础信息',
    items: [
      {
        title: '头像',
        type: 'avatar',
        bindKey: 'fansImg',
      },
      {
        title: '昵称',
        type: 'nick',
        bindKey: 'fansNick',
        canEdit: true,
      },
      // {
      //   title: '密码',
      //   type: 'pwd',
      //   bindKey: 'pwd',
      //   canEdit: true,
      // },
      {
        title: '注册时间',
        type: 'regDateTime',
        bindKey: 'regDateTime',
      },
    ],
  },
  {
    title: '授权信息',
    items: [
      {
        title: '微信号',
        type: 'wechat',
        bindKey: 'wechatId',
        canEdit: true,
      },
      {
        title: '手机号',
        type: 'account',
        bindKey: 'account',
      },
      {
        title: '清除缓存',
        type: 'clear',
        bindKey: 'clear',
      },
      {
        title: '隐私政策',
        type: 'policy',
        bindKey: 'policy',
      },
      {
        title: '注销账号',
        type: 'logoff',
        bindKey: 'logoff',
      },
      // {
      //   title: '淘宝渠道Id',
      //   type: 'relationId',
      //   bindKey: 'relationId',
      // },
      // {
      //   title: '账户余额',
      //   type: 'balance',
      //   bindKey: 'balance',
      // },
      // {
      //   title: '上级用户ID',
      //   type: 'superiorTkUserId',
      //   bindKey: 'superiorTkUserId',
      // },
      // {
      //   title: '用户等级',
      //   type: 'tkUserLevel',
      //   bindKey: 'tkUserLevel',
      // },
      // {
      //   title: '用户状态',
      //   type: 'userEnable',
      //   bindKey: 'userEnable',
      // },
    ],
  },
  {
    title: '提现相关',
    items: [
      // {
      //   title: '提现支付宝账号',
      //   type: 'aliPayAccount',
      //   bindKey: 'aliPayAccount',
      // },
      // {
      //   title: '支付宝真实姓名',
      //   type: 'realName',
      //   bindKey: 'realName',
      // },
      {
        title: '版本号',
        type: 'version',
        bindKey: 'version',
        value: '1.5.8',
      },
      {
        title: '开启位置权限',
        type: 'location',
        bindKey: 'location',
      },
      {
        title: '淘宝授权',
        type: 'taobaoAuth',
        bindKey: 'taobaoAuth',
        img: '/static/platform/tb-set.png',
      },
    ],
  },
]

export const orderTagInfos: orderTag[] = [
  {
    title: '邀请',
    iconName: 'share',
    orderNum: 0,
    tabType: 5,
    status: 0,
  },
  {
    title: '待发货',
    iconName: 'clear',
    orderNum: 0,
    tabType: 10,
    status: 0,
  },
  {
    title: '待收货',
    iconName: 'a-precisemonitor',
    orderNum: 0,
    tabType: 40,
    status: 0,
  },
  {
    title: '待评价',
    iconName: 'tips',
    orderNum: 0,
    tabType: 60,
    status: 0,
  },
  {
    title: '退款/售后',
    iconName: 'money-circle',
    orderNum: 0,
    tabType: 0,
    status: 0,
  },
]

export const totalTagInfos: totalTag[] = [
  {
    title: '今日预估',
    value: 0,
  },
  {
    title: '本月预估',
    value: 0,
  },
  {
    title: '本月收货',
    value: 0,
  },
  {
    title: '待结算',
    value: 0,
  },
]
