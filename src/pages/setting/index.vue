<script lang="ts" setup>
import { useMessage, useToast } from 'wot-design-uni'
import { menuData } from './type'
import { useAuthStore } from '@/store/user'
import { checkVersion, logoff, setUserInfo as setSuperInfo } from '@/services'
import { openTaobaoApp } from '@/common/jump'
import { appInfo } from '@/common'

const emit = defineEmits(['onClickItem'])
const editForm = ref<any>({})
const { userInfo, userToken, logout, restore } = useAuthStore()
console.log('🚀 ~ userInfo:', userInfo.value, userToken.value)
const { show: showToast } = useToast()
const message = useMessage()
function onClickCell(currentTarget: {
  type: string
  title: string
  value: string
  canEdit: boolean
  img?: string
  icon?: string
  bindKey: string
}) {
  console.log('currentTarget', currentTarget)
  if (currentTarget.type === 'clear') {
    message
      .confirm({
        title: '提示',
        msg: '确定清除缓存吗？',
        zIndex: 101,
        closeOnClickModal: false,
      })
      .then(() => {
        uni.clearStorage()
        restore(userToken.value, userInfo.value)
        uni.showToast({
          icon: 'none',
          title: '缓存清理完成',
        })
      })
      .catch(() => {
        console.log('点击了取消按钮')
      })
    return false
  }
  if (currentTarget.type === 'policy') {
    uni.navigateTo({
      url: '/pages/personal/material?materialId=171&name=隐私政策',
    })
    return false
  }
  if (currentTarget.type === 'logoff') {
    message
      .confirm({
        title: '提示',
        msg: '账号注销之后无法恢复，确定注销吗？',
        zIndex: 101,
        closeOnClickModal: false,
      })
      .then(() => {
        logoff({ account: userInfo.value.account })
        uni.clearStorage()
        uni.reLaunch({
          url: '/pages/home/<USER>',
        })
      })
      .catch(() => {
        console.log('点击了取消按钮')
      })
    return false
  }
  if (currentTarget.type === 'version') {
    // #ifdef APP-PLUS
    if (plus.os.name === 'Android') {
      checkUpdate()
    }
    else if (plus.os.name === 'iOS') {
      message.alert('已是最新版本，请前往苹果应用商店更新')
    }
    // #endif
    // #ifdef H5
    checkUpdate()
    // #endif
    return false
  }
  if (currentTarget.type === 'taobaoAuth') {
    const rid = userInfo.value.relationId
    if (rid && rid !== '') {
      uni.showToast({
        icon: 'none',
        title: '已完成授权，请勿重复操作',
      })
    }
    else {
      openTaobaoApp()
    }
    return false
  }
  if (!currentTarget.canEdit)
    return false

  editForm.value = currentTarget

  message
    .prompt({
      title: `请输入${currentTarget.title}`,
      inputValue: currentTarget.value,
      inputPlaceholder: '请输入',
      inputType: 'text',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      zIndex: 101,
      closeOnClickModal: false,
    })
    .then((resp) => {
      console.log('then', resp)
      if (resp.value)
        setUserInfo(`${resp.value}`)
    })
    .catch((error) => {
      console.log('error', error)
    })
}
async function checkUpdate() {
  const data = await checkVersion({
    current_version: appInfo.version,
    device_id: uni.getSystemInfoSync().deviceId,
    package_name: appInfo.packageName,
    system_type: 'android',
  })
  console.log('checkVersion', data)
  if (data && data.update_available) {
    uni.showModal({
      title: '提示',
      content: '检测到新版本，是否立即更新？',
      success(res) {
        if (res.confirm) {
          // #ifdef APP-PLUS
          plus.runtime.openURL(data.update_url, (e) => {
            console.log('调用手机浏览器失败', e)
            uni.showToast({
              icon: 'none',
              title: '浏览器暂不可打开，请前往商店下载应用',
            })
          })
          // #endif
          // #ifdef H5
          location.href = data.update_url
          // #endif
        }
      },
      fail: () => {},
      complete: () => {},
    })
  }
  else {
    await uni.showToast({
      icon: 'none',
      title: '当前已是最新版本',
    })
  }
}
async function setUserInfo(value: string) {
  const { data } = await setSuperInfo({
    data: value,
    type: editForm.value.type,
  })

  if (data) {
    showToast(`${editForm.value.title} 修改成功`)
    // 更新
    userInfo.value[editForm.value.bindKey] = value
  }
}
onMounted(() => {
  console.log(toRaw(userInfo.value))
})

function onClickItem(e: any) {
  emit('onClickItem', e)
}

function loginOut() {
  logout()
  uni.reLaunch({
    url: '/pages/home/<USER>',
  })
}

function handleClickLeft() {
  uni.navigateBack()
}

function transferVal(xitem) {
  let val = xitem.value || userInfo.value[xitem.bindKey]
  if (xitem.type === 'avatar') {
    if (val && !val.startsWith('http://') && !val.startsWith('https://')) {
      val = `/static/avatar/${val}`
    }
  }
  if (xitem.type === 'taobaoAuth') {
    const rid = userInfo.value.relationId
    if (rid && rid !== '') {
      val = '已授权'
    }
    else {
      val = '未授权'
    }
  }
  return val
}

function isOpenLocation() {
  // #ifdef APP-PLUS
  const appAuthorizeSetting = uni.getAppAuthorizeSetting()
  console.log('appAuthorizeSetting', appAuthorizeSetting)
  return appAuthorizeSetting.locationAuthorized !== 'denied'
  // #endif
  return false
}

const switchlocation = ref<boolean>(isOpenLocation())

function handleSwitchLocation() {
  // console.log(switchlocation.value)
  if (!switchlocation.value) {
    return false
  }
  // #ifdef APP-PLUS
  const appAuthorizeSetting = uni.getAppAuthorizeSetting()
  if (appAuthorizeSetting.locationAuthorized == 'denied') {
    // message
    //     .confirm({
    //       title: 'APP位置权限未开启',
    //       msg: '您已拒绝获取定位权限，是否去设置中打开？',
    //       zIndex: 101,
    //       closeOnClickModal: false,
    //     })
    //     .then(() => {
    //       uni.openAppAuthorizeSetting()
    //     })
    //     .catch(() => {
    //       console.log('点击了取消按钮')
    //     })
    uni.openAppAuthorizeSetting()
  }
  // #endif
  return false
}
</script>

<template>
  <overlay-search-wraper :use-message-box="false" :use-overlay-search="false" warp-class="bg-#edf2f5 min-h-screen">
    <view class="nav-bar">
      <wd-navbar title="设置中心" left-arrow :bordered="false" @click-left="handleClickLeft" />
    </view>
    <view class="sett-cont">
      <view v-for="(item, findex) in menuData" :key="findex">
        <wd-card>
          <!-- <wd-divider color="#9f9a9a">
            {{ item.title }}
          </wd-divider> -->
          <view v-for="(xitem, index) in item.items" :key="index" class="celw overflow-hidden pb-1 pt-1">
            <wd-cell-group>
              <wd-cell
                :key="xitem.type" :title="xitem.title" :value="transferVal(xitem)" is-link
                :class="{ avatarh: xitem.type == 'avatar', locat: xitem.type == 'location' }"
                @click="onClickCell({ ...xitem, value: xitem.value || userInfo[xitem.bindKey] })"
              >
                <template v-if="xitem.icon || xitem.img" #icon>
                  <view mr-0.5rem lh-0.5rem>
                    <wd-img v-if="xitem.img" :src="xitem.img" class="h-1.25rem w-1.25rem" />
                    <wd-icon v-if="xitem.icon" size="20px" :name="xitem.icon" />
                  </view>
                </template>
                <view v-if="xitem.type == 'avatar'">
                  <wd-img :src="transferVal(xitem)" class="h-2.5rem w-2.5rem overflow-hidden rounded-[50%] bg-#dd524d" />
                </view>
                <view v-if="xitem.type == 'location'">
                  <wd-switch v-model="switchlocation" size="20px" @change="handleSwitchLocation" />
                </view>
              </wd-cell>
            </wd-cell-group>
          </view>
        </wd-card>
      </view>
      <view m-1rem>
        <wd-button block @click="loginOut()">
          退出登录
        </wd-button>
      </view>
    </view>
  </overlay-search-wraper>
</template>

<style lang="scss" scoped>
  .nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    background: #fff;
    padding-top: calc(var(--status-bar-height) + 0.5rem);
  }
  .sett-cont {
    padding: calc(var(--status-bar-height) + 4rem) 0.5rem 0.5rem;

    .wd-card {
      margin: 0 0 0.5rem;
      padding: 0;
      ::v-deep .wd-card__title-content {
        display: none;
      }
      ::v-deep .wd-card__content {
        border-radius: 0.5rem;
        overflow: hidden;
        .celw {
          border-bottom: 1px solid #eee;
          .avatarh {
            line-height: 12px;
            .wd-cell__wrapper {
              padding-top: 0;
              padding-bottom: 0;
            }
          }
          .locat {
            line-height: 12px;
          }
          .wd-cell.is-hover {
            background: none;
          }
          .wd-cell__wrapper, .wd-cell__left, .wd-cell__body {
            align-items: center;
          }
        }
        .celw:last-child {
          border-bottom: none;
        }
      }
      ::v-deep .wd-card__footer {
        display: none;
      }
    }
    ::v-deep .wd-cell__value {
      white-space: nowrap;
    }
  }
</style>

<route lang="json5" type="page">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
