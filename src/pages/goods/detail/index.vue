<!--
 * @Author: dongpenghu
 * @Date: 2024-05-07 21:21:53
 * @LastEditors: dongpenghu
 * @LastEditTime: 2024-05-08 00:02:11
 * @Description: file content
 * @FilePath: \juwatech\src\pages\goods\detail\index.vue
-->
<script lang="ts" setup>
import { useGoodsStore } from '@/store/goods'

// console.log(goods.value)

const props = defineProps({
  goods_id: {
    type: String,
    default: '',
  },
  platformCode: {
    type: String,
    default: '',
  },
  clip: {
    type: Number,
    default: 0,
  },
})
console.log('[goods detail] props', props)

const { goods } = useGoodsStore()

function handleClickLeft() {
  uni.navigateBack()
}

function handleRefresh() {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  uni.reLaunch({
    url: `/${currentPage.route}`,
  })
}
</script>

<template>
  <overlay-search-wraper class="min-h-screen bg-#edf2f5">
    <view class="goods-wraper">
      <wd-navbar title="商品详情" left-arrow :bordered="false" @click-left="handleClickLeft" />
    </view>
    <view class="goods-cont">
      <goodsinfo class="content" :goods="JSON.parse(goods)" :clip="props.clip" />
    </view>
  </overlay-search-wraper>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTextStyle": "black"
  }
}
</route>

<style lang="scss" scoped>
@import '@/plugins-status-bar.scss';
</style>

<style>
.goods-wraper{
  /* margin-top: calc(var(--status-bar-height) + var(--status-bar-height)/2); */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #fff;
  padding-top: calc(var(--status-bar-height) + 0.5rem);
}

.goods-cont {
  padding-top: calc(var(--status-bar-height) + 3.35rem);
  // #ifdef H5
  padding-top: calc(var(--status-bar-height) + 2.9rem);
  // #endif
}
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin: 200rpx auto 50rpx auto;
}

.text-area {
  display: flex;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  color: #8f8f94;
}
</style>
