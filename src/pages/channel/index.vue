<script setup lang="ts">
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { channelProps, channelTypes } from './types'
import { queryGoodsList } from '@/services'
import GoodsCard2 from '@/components/goods-card/goods-card2.vue'
import { useGoodsStore } from '@/store/goods'

const props = defineProps(channelProps)
// console.log('props', props)

const { setGoods } = useGoodsStore()

// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

const goodListPagination = reactive({
  index: 0,
  num: 20,
})
const state = ref<LoadMoreState>('loading')
const goodsListLoadStatus = ref<number>(0)
const goods = ref<any[]>([] as any[])

async function loadGoodsList(fresh = false) {
  if (goodsListLoadStatus.value === 1) {
    console.log('[channel goods] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const Limit = goodListPagination.num
  let PageNo = goodListPagination.index + 1
  if (fresh)
    PageNo = 1

  try {
    let tbParam = {}
    if (props.platformCode === 'tb') {
      tbParam = {
        queryValue: '5',
        queryType: 'list',
      }
    }
    if (props.platformCode === 'dy') {
      tbParam = {
        queryValue: '0',
        queryType: 'list',
      }
    }
    const { list = [] } = await queryGoodsList({
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
      platformCode: channelTypes[props.platformCode].code,
      ...tbParam,
    })
    goodsListLoadStatus.value = 0

    if (!list || list.length <= 0) {
      console.log('[channel goods] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        goods.value = []
      }
      if (goods.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      goods.value = list || []
    else if (list && list.length > 0)
      goods.value = goods.value.concat(list) // 追加新加载的商品

    goodListPagination.index = PageNo // 更新当前页码
    goodListPagination.num = Limit
  }
  catch (err) {
    console.error('[channel goods] 首页加载失败', err)
    goods.value = []
    goodsListLoadStatus.value = 3
  }
}

onMounted(async () => {
  await loadGoodsList()
})

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    loadGoodsList() // 调用加载更多的函数
  }
})

const scrollTop = ref<number>(0)
onPageScroll((e) => {
  scrollTop.value = e.scrollTop
  // console.log('当前页面滚动距离', e.scrollTop)
})

async function goodListClickHandle({ index }: { index: number }) {
  const goodsDetails = goods.value[index]
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

function handleClickLeft() {
  uni.navigateBack()
}

function navToSearchPage() {
  uni.navigateTo({
    url: `/pages/search/index?platformCode=${channelTypes[props.platformCode].code}`,
  })
}
</script>

<template>
  <view :class="props.platformCode">
    <view class="nav-bar" :class="{ 'nav-show': scrollTop >= 10 }">
      <wd-navbar :title="props.title" left-arrow :bordered="false" @click-left="handleClickLeft" />
    </view>
    <view class="channel min-h-screen">
      <view class="top">
        <view class="tiimg">
          <wd-img :src="`${channelTypes[props.platformCode].icon}`" width="5rem" height="5rem" />
        </view>
        <view class="midtxt">
          <text>{{ channelTypes[props.platformCode].show }} 90%的商品都有佣金</text>
        </view>
        <view class="botsearch pb-0.5rem">
          <view class="search-cont" @click="navToSearchPage">
            <wd-search :placeholder="`搜索${channelTypes[props.platformCode].name}返利商品`" :hide-cancel="true" placeholder-left />
            <wd-button>搜商品</wd-button>
          </view>
        </view>
      </view>
      <view class="middle">
        <view class="middle-item flex justify-center">
          <text class="issue text-1rem font-bold">
            简单三步 轻松领佣金
          </text>
        </view>
        <view class="middle-item px-0.5rem">
          <view class="guide">
            <view class="flex flex-1 flex-col items-center">
              <wd-img :src="`${channelTypes[props.platformCode].icon}`" width="3rem" height="3rem" />
              <text>
                1.在<text class="issue">
                  {{ channelTypes[props.platformCode].name }}APP
                </text>打开
              </text>
              <text>
                你想购买的商品
              </text>
            </view>
            <view class="flex flex-1 flex-col items-center">
              <view class="wd-img bg-#fa8b36">
                <wd-icon name="link" size="30" color="#fff" />
              </view>
              <text>
                2.点击<text class="issue">
                  分享
                </text>
              </text>
              <text>
                然后<text class="issue">
                  复制链接
                </text>
              </text>
            </view>
            <view class="relative flex flex-1 flex-col items-center">
              <wd-img src="/static/logo.png" width="3rem" height="3rem" />
              <text>
                3.返回<text class="issue">
                  省赚客
                </text>
              </text>
              <text>
                领取佣金
              </text>
            </view>
          </view>
        </view>
        <view class="middle-item flex justify-center">
          <text class="issue text-1rem font-bold">
            热销商品
          </text>
        </view>
      </view>
      <view class="bottom">
        <view v-for="(item, index) in goods" :key="index" class="glist-item">
          <goods-card2 :data="item" @item-click="goodListClickHandle({ index })" />
        </view>
      </view>
      <!-- loading -->
      <wd-loadmore :state="state" @reload="loadGoodsList" />
      <!-- no data -->
      <wd-status-tip v-if="goodsListLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
    </view>
  </view>
</template>

<style scoped lang="scss">
  .tb {
    .nav-bar {
      background: #fa3c00;
      :deep(.wd-navbar) {
        background: #fa3c00;
      }
    }
    .channel {
      .top {
        .tiimg, .midtxt, .botsearch {
          background: #fa3c00;
        }
        .botsearch {
          .search-cont {
            :deep(.wd-search) {
              background-color: #fa3c00;
              .wd-search__block {
                .wd-search__search-left-icon {
                  color: #fa3c00;
                }
              }
            }
          }
        }
      }
    }
  }
  .jd {
    .nav-bar {
      background: #fb3414;
      :deep(.wd-navbar) {
        background: #fb3414;
      }
    }
    .channel {
      .top {
        .tiimg, .midtxt, .botsearch {
          background: #fb3414;
        }
        .botsearch {
          .search-cont {
            :deep(.wd-search) {
              background-color: #fb3414;
              .wd-search__block {
                .wd-search__search-left-icon {
                  color: #fb3414;
                }
              }
            }
          }
        }
      }
    }
  }
  .pdd {
    .nav-bar {
      background: #ff4b8a;
      :deep(.wd-navbar) {
        background: #ff4b8a;
      }
    }
    .channel {
      .top {
        .tiimg, .midtxt, .botsearch {
          background: #ff4b8a;
        }
        .botsearch {
          .search-cont {
            :deep(.wd-search) {
              background-color: #ff4b8a;
              .wd-search__block {
                .wd-search__search-left-icon {
                  color: #ff4b8a;
                }
              }
            }
          }
        }
      }
    }
  }
  .dy {
    .nav-bar {
      background: #b09fff;
      :deep(.wd-navbar) {
        background: #b09fff;
      }
    }
    .channel {
      .top {
        .tiimg, .midtxt, .botsearch {
          background: #b09fff;
        }
        .botsearch {
          .search-cont {
            :deep(.wd-search) {
              background-color: #b09fff;
              .wd-search__block {
                .wd-search__search-left-icon {
                  color: #b09fff;
                }
              }
            }
          }
        }
      }
    }
  }
  .wph {
    .nav-bar {
      background: #ff91b9;
      :deep(.wd-navbar) {
        background: #ff91b9;
      }
    }
    .channel {
      .top {
        .tiimg, .midtxt, .botsearch {
          background: #ff91b9;
        }
        .botsearch {
          .search-cont {
            :deep(.wd-search) {
              background-color: #ff91b9;
              .wd-search__block {
                .wd-search__search-left-icon {
                  color: #ff91b9;
                }
              }
            }
          }
        }
      }
    }
  }

  .nav-show {
    :deep(.wd-navbar) {
      .wd-navbar__content {
        .wd-navbar__title {
          opacity: 1;
        }
      }
    }
  }

  .nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    padding-top: calc(var(--status-bar-height) + 0.5rem);

    :deep(.wd-navbar) {

      .wd-navbar__arrow, .wd-navbar__title {
        color: #fff;
      }

      .wd-navbar__title {
        opacity: 0;
        transition: .5s;
      }
    }
  }
  .channel {
    padding-top: calc(var(--status-bar-height) + 2.5rem);

    .top {
      .tiimg, .midtxt, .botsearch {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 0.5rem;
      }

      .tiimg {
        padding-top: 1rem;

        :deep(.wd-img) {
          border: 0.2rem solid #fff;
          border-radius: 50%;
          background-color: #fff;
        }
      }

      .midtxt {
        padding-top: 2rem;
        color: #fff;
        font-weight: bold;
        font-size: 1.4rem;
      }

      .botsearch {
        .search-cont {
          position: relative;
          width: 100%;

          :deep(.wd-search) {

            .wd-search__block {
              border-radius: 1.5rem;

              .wd-search__input {
                padding: 1.5rem 1.5rem 1.5rem 2.5rem;
              }
            }
          }

          :deep(.wd-button) {
            position: absolute;
            right: 1.5rem;
            bottom: 1rem;
            background: #feb301;
            color: #fff;
            min-width: auto;
          }
        }
      }
    }

    .middle {
      background-color: #edf2f5;
      .middle-item {
        padding-top: 1rem;

        .issue {
          color: #ff1c1c;
          margin: 0 0.2rem;
        }

        .guide {
          display: flex;
          justify-content: space-between;
          background-color: #fff;
          padding: 1rem 0.5rem;
          border-radius: 0.5rem;
          font-size: 0.75rem;
          color: #666;

          .wd-img {
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #fff;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 0.5rem;
          }
        }
      }
    }

    .bottom {
      background-color: #edf2f5;
      padding: 1rem 0.5rem 0.5rem;

      .glist-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
      }

      .glist-item:first-child {
        margin-top: 0;
      }
    }
  }
</style>

<route lang="json5" type="page">
{}
</route>
