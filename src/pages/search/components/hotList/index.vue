<script lang="ts" setup>
import { hostListProps } from './type'
</script>

<template>
  <view class="hot-list">
    <scroll-view scroll-x="true" style="overflow: scroll; white-space: nowrap;">
      <view v-for="(item, index) in hostListProps" :key="index" class="hot-list-view" style="height: 500rpx;width: 100%;display: inline-block;">
        <hot-list-goods
          :platform-code="item.platformCode"
          :params="item.params"
        />
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.hot-list {
  .hot-list-view{
    width: auto !important;
  }
}
</style>

<route lang="json5" type="page">
{
  style: { navigationBarTitleText: 'hot-list' },
}
</route>
