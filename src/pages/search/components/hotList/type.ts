interface HotListProps {
  platformCode: appGoodsList.Params['platformCode']
  params: appGoodsList.Params
}

export const hostListProps: HotListProps[] = [
  {
    platformCode: 'TB',
    params: {
      queryType: 'list',
      pageNo: '1',
      limit: '10',
      queryValue: '11',
    },
  },
  {
    platformCode: 'JD',
    params: {
      queryType: 'list',
      pageNo: '1',
      limit: '10',
      queryValue: '1',
    },
  },
  {
    platformCode: 'PDD',
    params: {
      queryType: 'list',
      pageNo: '1',
      limit: '10',
      queryValue: '1',
    },
  },
  {
    platformCode: 'DY',
    params: {
      queryType: 'list',
      pageNo: '1',
      limit: '10',
      queryValue: '1',
    },
  },
  {
    platformCode: 'WPH',
    params: {
      queryType: 'list',
      pageNo: '1',
      limit: '10',
      queryValue: '1',
    },
  },
]
