export interface searchTabs {
  platformCode: appGoodsList.Params['platformCode']
  platformName: string
}

interface searchTab {
  name: string
  value: string
  platformCode?: appGoodsList.Params['platformCode']
}

export const searchProps = {
  platformCode: {
    type: String,
    default: '',
  },
}
export const tabbar: searchTab[] = [
  {
    name: '综合',
    value: '',
  },
  {
    name: '券后价',
    value: 'price',
  },
  {
    name: '销量',
    value: 'sales',
  },
  {
    name: '高佣金',
    value: 'rate',
  },
]
export const searchTab: searchTabs[] = [
  {
    platformCode: 'QW',
    platformName: '全网',
  },
  {
    platformCode: 'TB',
    platformName: '淘宝',
  },
  {
    platformCode: 'JD',
    platformName: '京东',
  },
  // {
  //   platformCode: 'DY',
  //   platformName: '抖音',
  // },
  {
    platformCode: 'WPH',
    platformName: '唯品会',
  },
  {
    platformCode: 'PDD',
    platformName: '拼多多',
  },
  // {
  //   platformCode: 'KL',
  //   platformName: '考拉',
  // },
]
