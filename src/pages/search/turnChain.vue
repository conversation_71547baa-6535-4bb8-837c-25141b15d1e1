<script lang="ts" setup>
import { chainTransfer } from '@/services'

const { value: text } = useQuery('text')

const value = ref<string>('')
const transfercontent = ref<appGoodsChainTransfer.ResponseData>({})

const loadingShow = ref<boolean>(false)

async function handleChian() {
  loadingShow.value = true

  const data = await chainTransfer({ keyword: value.value })

  loadingShow.value = false

  transfercontent.value = data

  if (data.content && data.content != '') {
    value.value = data.content
    await uni.showToast({
      icon: 'none',
      title: '转链成功',
    })
  }
  else {
    await uni.showToast({
      icon: 'none',
      title: '转链失败，请重新转链',
    })
  }

  if (data.needAuth) {
    await uni.showToast({
      icon: 'none',
      title: '该平台需要授权，请前往授权',
    })
  }
}
function handleClear() {
  value.value = ''
}

onMounted(() => {
  console.log('-------------------------------------onMounted')
  value.value = text.value
})

function handleBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="turnchain">
    <!-- 一键转链是实时接口，点击分享按钮时就会有点卡顿 -->
    <wd-overlay :show="loadingShow" :z-index="102">
      <view class="h-full flex items-center justify-center">
        <wd-loading />
      </view>
    </wd-overlay>

    <view class="nav-wrapper">
      <wd-navbar title="智能转链" left-arrow :bordered="false" @click-left="handleBack" />
    </view>
    <view class="tiop-wr">
      <wd-notice-bar direction="vertical" text="通过转链所产生的订单，才能计入您的佣金中！" :speed="0.5" :delay="3" custom-class="space" />
    </view>
    <view class="cont-wrapper">
      <view class="s-row m-2 bg-#fff">
        <view class="pasarea">
          <wd-textarea v-model="value" :maxlength="1000" custom-textarea-container-class="textarea-container" placeholder="请粘贴要转换的口令或链接&#10;支持淘口令，淘宝/京东/拼多多/唯品会/苏宁" />
        </view>
        <view class="transbtn">
          <wd-button plain @click="handleClear">
            清空内容
          </wd-button>
          <wd-button @click="handleChian">
            一键转链
          </wd-button>
        </view>
      </view>
    </view>
    <view class="rule-wrapper">
      <text class="r-title">
        <wd-icon name="pin" style="margin-right: 0.2rem" />转链规则
      </text>
      <view class="r-cont">
        <text class="r-cont-item">
          1、支持淘口令，淘宝/京东/拼多多/唯品会/苏宁。
        </text>
        <text class="r-cont-item">
          2、仅支持转链1000字以内的内容，且每个链接后必须以空格或换行结尾。
        </text>
        <text class="r-cont-item">
          3、请勿频繁转链，否则会视为恶意刷接口，平台有权收回此功能的使用。
        </text>
        <text class="r-cont-item">
          4、转链成功后，建议打开链接查看链接跳转是否正确。
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.turnchain {

  .wd-overlay {
    background: transparent;
  }

  .nav-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    padding-top: calc(var(--status-bar-height) + 0.5rem);
    background-color: #fff;
  }
  .tiop-wr {
    padding-top: calc(var(--status-bar-height) + 3.3rem);

    .wd-notice-bar {
      text-align: center;
    }
  }
  .cont-wrapper {
    margin-top: 1rem;

    .wd-textarea {
      background-color: rgb(240 240 240);
      border-radius: 0.5rem;
    }

    .transbtn {
      margin-top: 1rem;
      display: flex;
      justify-content: space-between;
    }
  }
  .rule-wrapper {
    margin-top: 2rem;

    .r-title {
      font-weight: bold;
      display: block;
      width: 100%;
      padding: 0.5rem;
    }

    .r-cont {
      .r-cont-item {
        display: block;
        padding: 0.5rem;
        color: #999;
        font-size: 0.9rem;
      }
    }
  }
}

:deep(.textarea-container) {
  background: var(--wot-button-info-bg-color, #F0F0F0);

  .wd-textarea__value {
    width: 100%;
    height: 100%;
  }
}

.content-bg {
  background-image: url('@/static/chain/chain-teach3.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: auto;
  height: 100vh;
}
</style>

<route lang="json5">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
