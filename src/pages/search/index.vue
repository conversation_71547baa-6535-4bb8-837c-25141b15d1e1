<script lang="ts" setup>
import type { LoadMoreState } from 'wot-design-uni/components/wd-loadmore/types'
import { searchProps, searchTab, tabbar } from './type'
import { useSearchStore } from '@/store/search'
import { searchGoods } from '@/services/goods'
import { useGoodsStore } from '@/store/goods'
import { queryHotKey } from '@/services'

const props = defineProps(searchProps)
console.log('props', props)

const { setGoods } = useGoodsStore()
const {
  search,
  addSearch,
  setSearch,
  clearSearch,
  deleteSearch,
} = useSearchStore()
const { value: type } = useQuery('type')
const { value: text } = useQuery('text')
const goodsListLoadStatus = ref<number>(0)
const privateData = reactive({
  tabIndex: 0,
})
const goodListPagination = reactive({
  index: 0,
  num: 20,
})

const searchVal = ref<string>('')
const childtabbar = ref<number>(0)
const historySearch = ref<string[]>([])
const showHistory = ref<boolean>(false)
const goodlists = ref<appGoodsSearch.ResponseData['list']>([])

// 接口是否有数据返回：0、默认；1、无值
const isDataEmpty = ref<number>(0)
// 分页数据是否加载完毕：0、否；1、是
const noMoreData = ref<number>(0)

function handleSearch() {
  if (!searchVal.value)
    return
  addSearch(searchVal.value)
  historySearch.value = search.value
  showHistory.value = true

  state.value = 'loading'
  noMoreData.value = 0
  isDataEmpty.value = 0
  goodlists.value = []

  handlesearchGoods()
}

function handleClearSearch() {
  state.value = 'loading'
  noMoreData.value = 0
  isDataEmpty.value = 0
  goodlists.value = []
}

const platformIndex = ref<number>(0)

async function handleChange() {
  await loadHotKey()
}

function handleClear() {
  clearSearch()
  historySearch.value = []
  showHistory.value = false
}

function handleTag(tag: string) {
  searchVal.value = tag
  handleSearch()
}

const hotKeys = ref<string[]>([])
// 加载热门话题
async function loadHotKey() {
  const { data = [] } = await queryHotKey({ minId: '1', tag: 'search' })
  console.log('hotKey', data)
  hotKeys.value = data
}

const state = ref<LoadMoreState>('loading')

onReachBottom(() => {
  console.log('--------------------------------------------------------------onReachBottom', goodsListLoadStatus.value, noMoreData.value, isDataEmpty.value)
  if (goodsListLoadStatus.value === 0 && noMoreData.value === 0) {
    handlesearchGoods()
  }
})

onLoad(async () => {
  if (searchVal.value !== '')
    await handlesearchGoods()
  await loadHotKey()
})

async function handleSearchTbas(index: number) {
  platformIndex.value = index
  goodlists.value = []
  state.value = 'loading'
  isDataEmpty.value = 0
  noMoreData.value = 0
  await handlesearchGoods(true)
}

async function handlesearchGoods(fresh = false) {
  if (goodsListLoadStatus.value === 1) {
    console.log('[search goods] 正在请求中~~~')
    return false
  }
  goodsListLoadStatus.value = 1

  const Limit = goodListPagination.num
  let PageNo = privateData.tabIndex * Limit + goodListPagination.index + 1
  if (fresh)
    PageNo = 1
  try {
    const data = await searchGoods({
      platformCode: searchTab[platformIndex.value].platformCode as appGoodsSearch.Params['platformCode'],
      sortAsc: false,
      keyword: searchVal.value,
      pageNo: `${PageNo}`,
      limit: `${Limit}`,
      /** 排序字段：销量-sales，价格-price，佣金比例-rate */
      sortColumn: tabbar[childtabbar.value].value,
    })
    goodsListLoadStatus.value = 0

    if (!data || !data.list || data.list.length <= 0) {
      console.log('[search goods] 没有更多啦~~~')
      state.value = 'finished'
      noMoreData.value = 1
      if (fresh) {
        goodlists.value = []
      }
      if (goodlists.value.length <= 0) {
        isDataEmpty.value = 1
      }
      return false
    }
    isDataEmpty.value = 0

    // if (data.list.length < Limit) {
    //   state.value = 'finished'
    //   noMoreData.value = 1
    // }

    if (fresh)
      goodlists.value = data.list || []
    else
      goodlists.value = goodlists.value.concat(data.list)

    goodListPagination.index = PageNo
    goodListPagination.num = Limit
  }
  catch (err) {
    console.log('[search goods] error', err)
    goodlists.value = []
    goodsListLoadStatus.value = 3
  }
}

async function goodListClickHandle(goodsDetails: object) {
  setGoods(goodsDetails)
  uni.navigateTo({
    url: `/pages/goods/detail/index?goods_id=${goodsDetails.id}&platformCode=${goodsDetails.platformCode}`,
    success: (goodsChannel) => {
      console.log('跳转成功')
      goodsChannel.eventChannel.emit('goodsDetails', {
        ...goodsDetails,
      })
    },
    fail: () => {
      console.log('跳转失败')
    },
    complete: () => {
      console.log('跳转完成')
    },
  })
}

onMounted(() => {
  historySearch.value = search.value
  showHistory.value = historySearch.value.length > 0
  // 弹窗过来的第一时间搜一次
  if (type.value && type.value !== '') {
    console.log('剪贴板弹窗搜索', type.value, searchVal.value)
    platformIndex.value = getIndexForTab(type.value)
    // console.log(platformIndex.value)
    searchVal.value = text.value
    if (searchVal.value !== '') {
      addSearch(searchVal.value)
      handlesearchGoods()
    }
  }
  // 兄弟页面跳转
  if (props.platformCode && props.platformCode !== '') {
    platformIndex.value = getIndexForTab(props.platformCode)
  }
})

function getIndexForTab(code: string) {
  return searchTab.findIndex(item => (item.platformCode === code))
}

function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <page-wraper class="search-page bg-#edf2f5">
    <view class="search-nav-wraper">
      <wd-navbar title="搜索" left-arrow :bordered="false" @click-left="handleClickLeft" />
      <wd-search v-model="searchVal" placeholder="请输入商品关键字" cancel-txt="搜索" placeholder-left @cancel="handleSearch" @clear="handleClearSearch" />
    </view>
    <view class="search-cont-wrapper">
      <view class="cate-btn">
        <wd-button v-for="(item, index) in searchTab" :key="index" size="small" :class="{ 'btn-active': index == platformIndex }" @click="handleSearchTbas(index)">
          {{ item.platformName }}
        </wd-button>
      </view>
      <view class="his-cont">
        <view v-if="!showHistory" class="hot-search px-0.5rem">
          <view class="flex justify-between">
            <text>热门搜索</text>
            <text class="optbtn text-gray-400" @click="handleChange">
              <wd-icon name="translate-bold" class="mr-0.25rem" />换一批
            </text>
          </view>
          <view class="mt-1rem">
            <wd-tag v-for="(k, index) in hotKeys" :key="index" round color="#e0a654" @click="handleTag(k)">
              {{ k }}
            </wd-tag>
          </view>
        </view>
        <view v-if="showHistory" class="history-search px-0.5rem">
          <view class="flex justify-between">
            <text>历史搜索</text>
            <text class="optbtn text-gray-400">
              <wd-icon name="delete" class="text-gray-400" @click="handleClear" />
            </text>
          </view>
          <view class="mt-1rem">
            <wd-tag v-for="item in historySearch" :key="item" round @click="handleTag(item)">
              {{ item }}
            </wd-tag>
          </view>
        </view>
      </view>
      <view class="result-cont">
        <view
          v-for="good in goodlists" v-if="goodlists.length > 0" :key="good.id"
          class="list-item-details flex rounded-lg" mb-3
          flex p-2 @click="goodListClickHandle(good)"
        >
          <view flex-0.4>
            <wd-img :src="good.picUrl" width="7rem" height="7rem" mr-0.5rem radius="0.5rem" />
          </view>
          <view flex-0.4 md:mx-1rem>
            <text line-clamp-2 text-ellipsis whitespace-pre-line style="position:relative">
              <wd-img v-if="good.platformCode == 'TB' && good.goodsType == 0" src="/static/platform/TB.png" class="h-1rem w-1rem" style="position:absolute" />
              <wd-img v-if="good.platformCode == 'TB' && good.goodsType == 1" src="/static/platform/TM.png" class="h-1rem w-1rem" style="position:absolute" />
              <wd-img v-if="good.platformCode != 'TB'" :src="`/static/platform/${good.platformCode}.png`" class="h-1rem w-1rem" style="position:absolute" />
              <text style="margin-left: 1.25rem">
                {{ good.title }}
              </text>
            </text>
            <view class="mt-0.5rem items-center" flex>
              <view flex-1>
                <view flex items-center>
                  <wd-img src="/static/img/dp.png" class="mr-0.15rem h-0.95rem w-0.95rem" />
                  <text text-gray-400>
                    {{ good.shopTitle }}
                  </text>
                </view>
                <view class="mb-1 mt-0.5rem items-end justify-center space-x-2" md:my-1rem md:space-x-4>
                  <text v-if="good.couponAmount > 0" class="bg-#fa4126 text-red-500" style="color:#fff;border-radius:0.2rem;padding:0.1rem 0.3rem">
                    券 <text class="pl-0.2rem" style="border-left:1px dashed #fff">
                      {{ good.couponAmount }}
                    </text>
                  </text>
                  <text class="px-0.2rem text-red-500" style="border:1px solid #fa4126;border-radius:0.1rem">
                    约返{{ good.maxProfit }}
                  </text>
                </view>
                <view class="mt-0.5rem">
                  <text class="text-red-500 font-bold">
                    ￥{{ good.actualPrice }}
                  </text>
                  <text class="ml-0.5rem text-gray-400 line-through">
                    原价￥{{ good.orgPrice }}
                  </text>
                  <text class="float-right text-gray-400">
                    {{ good.salesCount }}人付款
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- loading -->
      <wd-loadmore
        v-if="goodsListLoadStatus == 1"
        :state="state" :style="{ 'background-color': goodsListLoadStatus == 1 ? '#fff' : 'transparent' }"
        @reload="handlesearchGoods"
      />
      <!-- no data -->
      <wd-status-tip v-if="goodsListLoadStatus == 0 && isDataEmpty === 1" image="/static/search-nodata.png" tip="当前搜索无结果" />
    </view>
  </page-wraper>
</template>

<style lang="scss" scoped>
.wd-overlay {
  background-color: transparent !important;
}
:deep(.search-page) {

  ::-webkit-scrollbar {
    background: transparent;
    /* 将滚动条背景色设为透明 */
  }

  .wd-search__search-left-icon {
    color: #fa4126;
  }
  .search-nav-wraper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    background: #fff;
    padding-top: calc(var(--status-bar-height) + 0.5rem);
    .wd-search__input {
      height: 35px;
    }
  }

  .search-cont-wrapper {
    padding-top: calc(var(--status-bar-height) + 7.5rem);
    // #ifdef H5
    padding-top: calc(var(--status-bar-height) + 6.5rem);
    // #endif

    .cate-btn {
      position: sticky;
      top: calc(var(--status-bar-height) + 6.5rem);
      // #ifdef H5
      top: calc(var(--status-bar-height) + 5.5rem);
      // #endif
      z-index: 100;
      padding: 0.5rem 1rem;
      white-space: nowrap;
      overflow-x: scroll;
      background-color: #fff;

      .wd-button {
        margin-right: 1rem;
        color: #6c6c70;
        background-color: #f7f6fb;
      }
      .wd-button:last-child {
        margin-right: 0;
      }
      .btn-active {
        color: #fa4126;
        background-color: #fde4e7;
      }
    }

    .his-cont {
      padding: 0.5rem;
      background: #fff;

      .wd-tag {
        background-color: #f7f6fb;
        border: none;
        margin-right: 1rem;
        margin-bottom: 1rem;
        padding: 0.5rem;
        border-radius: 1rem;
        max-width: 95%;

        .wd-tag__text {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .optbtn {
        font-size: 0.85rem;
      }
    }

    .result-cont {
      margin-top: 0.5rem;
      padding: 0 0.5rem;
      font-size: 0.75rem;

      .list-item-details {
        background: #fff;
      }
    }

    .wd-status-tip {
      background: #fff;
      //margin-top: -0.5rem;
    }
  }
}
</style>

<route lang="json5">
{
  style: {
    "navigationBarTextStyle": "black"
  }
}
</route>
