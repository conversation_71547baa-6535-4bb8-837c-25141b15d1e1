<script lang="ts">
/* eslint-disable */
import { getPhoneCode, loginByCode, loginByPwd } from "@/services";
import { useToast } from "wot-design-uni";
const toast = useToast();
import { useAuthStore } from "@/store/user";
const { setUserInfo, setUserToken } = useAuthStore();
let clear: string | number | NodeJS.Timeout | undefined;
// 手机号验证正则表达式
const phoneRegular = /^1\d{10}$/;
export default {
  data() {
    return {
      tabArr: ["账号密码登录", "短信注册登录"],
      title: "Hello",
      type: 0,
      isSee: false,
      code: "",
      phone: "",
      password: "",
      // 验证码
      codeText: "获取验证码",
      // 验证码已发
      readonlyType: false,
      btnShow: false,
      btnText: '登录',
      btnActive: false,
      logo: "",
      agree: false,
      isIos: true,
      isWeixin: true,
      system: 13,
      clearTime: null,
    };
  },
  methods: {
    // tab点击序列赋值
    tabClick(tag: number) {
      this.type = tag;
    },

    onPageJump(url: string) {
      uni.switchTab({
        url,
      });
    },

    onInput() {
      // 清除之前的定时器
      if (this.clearTime) {
        clearTimeout(this.clearTime)
      }

      // 使用防抖处理输入
      this.clearTime = setTimeout(() => {
        // 根据登录类型判断按钮状态
        this.btnShow = this.type === 1
          ? Boolean(this.phone && this.code)
          : Boolean(this.phone && this.password)
      }, 500)
    },
    // 验证码按钮文字状态
    async getCodeState() {
      // 定义倒计时时长
      const COUNTDOWN_SECONDS = 60
      const INITIAL_TEXT = "获取验证码"

      // 清除已有定时器
      if (this.timer) {
        clearInterval(this.timer)
      }

      // 设置初始状态
      this.readonlyType = true
      this.codeText = `${COUNTDOWN_SECONDS}S`

      let remainingSeconds = COUNTDOWN_SECONDS

      // 创建新定时器
      this.timer = setInterval(() => {
        remainingSeconds--
        this.codeText = `${remainingSeconds}S`

        if (remainingSeconds <= 0) {
          clearInterval(this.timer)
          this.codeText = INITIAL_TEXT
          this.readonlyType = false
        }
      }, 1000)

      try {
        // 发送验证码请求
        const res = await getPhoneCode({ account: this.phone })

        // 显示接口返回消息
        if (res.msg) {
          await uni.showToast({
            title: res.msg,
            icon: "none"
          })
        }
      } catch (error) {
        console.error("获取验证码失败:", error)

        // 显示错误提示
        await uni.showToast({
          title: "获取验证码失败",
          icon: "none"
        })

        // 重置状态
        clearInterval(this.timer)
        this.codeText = INITIAL_TEXT
        this.readonlyType = false
      }
    },
    // 发送验证码
    onSetCode() {
      if (this.readonlyType) return;

      if (!this.phone) {
        uni.showToast({
          title: "请输入手机号",
          icon: "none",
        });
        return;
      }
      if (!phoneRegular.test(this.phone)) {
        uni.showToast({
          title: "手机号格式不正确",
          icon: "none",
        });
        return;
      }
      if (!this.agree) {
        this.showToast('请先同意《省赚客隐私政策》');
        return false;
      }
      this.getCodeState();
    },
    async onSubmit() {
      if (this.btnActive) return;

      if (!await this.validateInputs()) return;

      this.btnText = '登录中...'
      this.btnActive = true

      const { phone, code, password } = this.getHttpData();

      try {
        await uni.showLoading({
          mask: true,
        })

        const userInfo = await this.loginUser(phone, code, password);

        uni.hideLoading()

        if (!userInfo) {
          this.btnText = '登录'
          this.btnActive = false
          return;
        }

        await this.handleSuccessfulLogin(userInfo);
      } catch (error) {
        console.error('登录失败:', error);
        this.btnText = '登录'
        this.btnActive = false
        await this.showToast('登录失败，请稍后重试');
      }
    },

    async validateInputs() {
      if (!this.agree) {
        await this.showToast('请先同意《省赚客隐私政策》');
        return false;
      }
      if (!this.phone || !phoneRegular.test(this.phone)) {
        await this.showToast(this.phone ? '手机号格式不正确' : '请输入手机号');
        return false;
      }
      if (this.type === 1 && !this.code) {
        await this.showToast('请输入验证码');
        return false;
      }
      if (this.type === 0 && !this.password) {
        await this.showToast('请输入密码');
        return false;
      }
      return true;
    },

    getHttpData() {
      return {
        phone: this.phone,
        code: this.type === 1 ? this.code : '',
        password: this.type === 0 ? this.password : ''
      };
    },

    async loginUser(phone: string, code: string, password: string) {
      let userInfo;
      if (this.type === 0) {
        userInfo = await loginByPwd({ account: phone, pwd: password });
      } else {
        userInfo = await loginByCode({ account: phone, smsCode: code });
      }

      if (userInfo.msg && userInfo.msg !== "成功") {
        await this.showToast(userInfo.msg);
        return null;
      }
      return userInfo;
    },

    async handleSuccessfulLogin(userInfo: any) {
      try {
        const token = userInfo.data.token;

        const results = await Promise.allSettled([
          uni.setStorageSync('userToken', token),
          setUserToken(token),
          setUserInfo(userInfo.data)
        ]);

        const failedOperations = results.filter(result => result.status === 'rejected');
        if (failedOperations.length > 0) {
          console.error('部分操作失败:', failedOperations);
          throw new Error('部分用户信息设置失败');
        }

        await this.navigateToHome();
      } catch (error) {
        console.error('设置用户信息失败:', error);
        await this.showToast('登录失败，请稍后重试');
      }
    },

    async navigateToHome() {
      try {
        await uni.reLaunch({
          url: "/pages/home/<USER>"
        });
        console.log("跳转完成");
      } catch (error) {
        console.log("跳转失败", error);
      }
    },

    async showToast(title: string ) {
      await uni.showToast({
        title,
        icon: 'none'
      });
    }
  },
};
</script>
<script setup lang="ts">
import { materials } from "@/common/material";
type AgreementProps = {
  [key: string]: string;
};
const protocolArr = ["省赚客隐私政策"];
function protocolClick() {
  const url = `/pages/iframe?url=${encodeURIComponent(materials[4].url)}`;
  uni.navigateTo({
    url,
    success: () => {
      console.log("跳转成功");
    },
    fail: () => {
      console.log("跳转失败");
    },
  });
}
</script>
<template>
  <wd-toast />
  <view class="register_page py-1rem pt-0">
    <view text-center>
      <wd-img :width="80" :height="80" class="logo-img mt-20vh bottom-[20px]" src="../../static/logo.png" />
    </view>
    <view class="login-form mx-auto sm:max-w-90vw md:max-w-80vw lg:max-w-60vw xl:max-w-50vw">
      <!-- selIndex: 选中类型  tabs-arr：tabs数组  @tabClick:tab点击事件 -->
      <login-tabs :sel-index="type" :tabs-arr="tabArr" @tab-click="tabClick" />
      <!-- triangle设置三角形 -->
      <view class="input_box triangle mx-2rem" :class="[type === 0 ? 'left_triangle' : 'right_triangle']">
        <view class="icon-wrapper">
        <wd-icon name="mobile" size="22px"></wd-icon>
        </view>
        <input v-model="phone" :maxlength="11" type="text" placeholder="请输入手机号" placeholder-class="grey"
          @input="onInput" />
      </view>
      <view v-if="type === 1" class="input_box mx-2rem">
        <view class="icon-wrapper">
         <wd-icon name="mail" size="22px"></wd-icon>
        </view>
        <input v-model="code" type="number" placeholder="请输入验证码" placeholder-class="grey" :maxlength="4"
          @input="onInput" @confirm="onSubmit" />
        <button class="code-btn" :class="{disabled: readonlyType}" @click="onSetCode">
          {{ codeText }}
        </button>
      </view>
      <view v-if="type === 0" class="input_box mx-2rem">
        <view class="icon-wrapper">
         <wd-icon name="lock-on" size="22px"></wd-icon>
        </view>
        <input v-model="password" class="input_item" :password="!isSee" placeholder="请输入密码" placeholder-class="grey"
          @input="onInput" @confirm="onSubmit" />
        <image v-if="isSee" src="@/static/icon/ic_logon_display.png" mode="aspectFit" class="eye-icon" @click="isSee = false" />
        <image v-else-if="!isSee" src="@/static/icon/ic_logon_hide.png" mode="aspectFit" class="eye-icon" @click="isSee = true" />
      </view>

      <!-- agree：是否同意勾选  protocolArr：协议数组 @click：勾选同意点击 @protocolClick：协议点击 -->
      <protocol-box :agree="agree" :protocol-arr="protocolArr" @click="agree = !agree"
        @protocol-click="protocolClick" />

      <view class="btn_box mx-2rem">
        <button v-if="btnShow" class="active" @click="onSubmit">{{ btnText }}</button>
        <button v-else class="noactive">登录</button>
      </view>
    </view>
    <view class="footer absolute w-full bottom-0">
      <div class="col-md-12 mx-auto text-center my-4">
          <text text-sm class="color-#666 text-xs"> APP备案号：浙ICP备2023022469号-3A </text>
      </div>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import "@/style/mixin.scss";
$head-color: #cd3b33;
$white-color: #fff;
$radius: 20rpx;
$border-color: #efefef;
$color-1: #007aff;
$color-2: #4cd964;
$color-3: #f0ad4e;
$color-4: #dd524d;
$list-item-height: 100rpx;
$list-margin: 20rpx;
$leftColor: #fdd447;
$rightColor: #fa886a;

.register_page {
  // background-image: linear-gradient(to bottom, $leftColor, $rightColor);
  // background: url("./static/login-bg.png");
  min-height: calc(96vh);
  position: relative;

  .logo {
    image {
      margin: 0 auto;
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
    }
  }

  .login-bg-img {
    width: 100vw;
    margin-top: 22rpx;
  }

  .triangle {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: -20rpx;
      border-left: 18rpx solid transparent;
      border-right: 18rpx solid transparent;
      border-bottom: 18rpx solid #efeef4;
      transition: all 0.4s;
    }

    &::after {
      content: "";
      position: absolute;
      top: -18rpx;
      border-left: 18rpx solid transparent;
      border-right: 18rpx solid transparent;
      border-bottom: 18rpx solid #f8f9fb;
      transition: all 0.4s;
    }
  }

  .left_triangle {
    &::before {
      left: 140rpx;
    }

    &::after {
      left: 140rpx;
    }
  }

  .right_triangle {
    &::before {
      left: 470rpx;
    }

    &::after {
      left: 470rpx;
    }
  }

  .input_box {
    display: flex;
    align-items: center;
    height: 104rpx !important;
    background-color: #f8f9fb;
    border-radius: 50rpx;
    border: solid 2rpx #efeef4;
    padding: 0rpx 40rpx;
    margin-top: 20rpx;

    .icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      width: 48rpx;
      height: 48rpx;
      flex-shrink: 0;
      position: relative;
    }

    .input-icon-svg {
      flex-shrink: 0;
      display: block;
      width: 100%;
      height: 100%;
    }

    image.eye-icon {
      width: 40rpx;
      height: 40rpx;
      padding: 8rpx;
      flex-shrink: 0;
      opacity: 0.7;
      transition: opacity 0.2s;

      &:active {
        opacity: 1;
      }
    }

    image {
      width: 36rpx;
      height: 24rpx;
    }

    input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      height: 60rpx;
    }

    .input_item {
      font-size: 28rpx;
      border: 0px;
      flex: 1;
      background-color: #f8f9fb;
      height: 88rpx;
      width: 100%;
      outline: none;
      //margin-left: 32rpx;
    }

    button {
      height: 60rpx;
      background-color: #f8f9fb;
      font-size: 28rpx;
      padding: 0 14rpx;
      color: $themeColor;
      line-height: 60rpx;
      margin-left: 20rpx;
      //margin-right: 40rpx;
    }

    .code-btn {
      height: 75rpx;
      background: red;
      font-size: 26rpx;
      padding: 0 30rpx;
      color: #fff;
      line-height: 75rpx;
      margin-left: 15rpx;
      border-radius: 50rpx;
      transition: all 0.3s;
      white-space: nowrap;
      min-width: 170rpx;
      text-align: center;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 15rpx rgba(255, 0, 0, 0.25);
      font-weight: 500;
      letter-spacing: 1rpx;

      &.disabled {
        opacity: 0.7;
        box-shadow: none;
      }

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(255, 0, 0, 0.2);
      }
    }

    .grey {
      color: #999999;
    }
  }

  .btn_box {
    margin-top: 60rpx;

    button {
      width: 100%;
      font-size: 34rpx;
      font-weight: 600;
      background-color: red;
      color: #fff;
      height: 100rpx;
      line-height: 100rpx;
      border-radius: 50rpx;
    }

    .active {
      background-color: red;
    }

    .button-hover {
      background-color: red;
      color: #fff;
    }

    .noactive {
      opacity: 0.7;
      background-color: red;
      box-shadow: 0 5rpx 15rpx rgba(255, 0, 0, 0.15);

      &::after {
        display: none;
      }
    }
  }

  .password_register {
    margin-top: 40rpx;
    display: flex;
    justify-content: space-between;
    //text-align: center;

    text {
      font-size: 28rpx;
      color: #333333;
      //text-decoration: underline;
    }
  }
}

.station {
  height: 230rpx;
}

.footer {
  background-color: $white-color;
  position: fixed;
  z-index: 5;
  bottom: 60rpx;

  .footer-text {
    font-size: 24rpx;
    color: #999;

    &:active {
      opacity: 0.8;
    }
  }
}

.third_party_login_box {
  position: fixed;
  bottom: 60rpx;
  width: 100%;
  left: 0;
  padding: 0 30rpx;

  .third_party_title {
    display: flex;
    align-items: center;

    &:before,
    &:after {
      content: "";
      flex: 1;
      height: 2rpx;
      background-color: #f5f5f5;
    }

    text {
      font-size: 24rpx;
      color: #999999;
      flex-shrink: 0;
      padding: 0 20rpx;
    }
  }

  .third_party_content {
    margin-top: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 80upx;
      height: 80upx;
      margin: 0 20rpx;
    }
  }
}
</style>

<route type="page" lang="json">
{
  "style": {
    "navigationBarTextStyle": "black"
  }
}
</route>
