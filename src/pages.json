{"pages": [{"path": "pages/home/<USER>", "type": "home"}, {"path": "pages/iframe", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/category/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/channel/index", "type": "page"}, {"path": "pages/circle/circle-list", "type": "page", "style": {"navigationBarTitleText": "circle-list"}}, {"path": "pages/circle/index", "type": "page", "layout": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/home/<USER>", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/life/index", "type": "page", "style": {"navigationBarTextStyle": "white"}}, {"path": "pages/life/search", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/login/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/material", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/rank/index", "type": "page", "layout": "page"}, {"path": "pages/search/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/search/turn<PERSON>hain", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/setting/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/shop/index", "type": "page"}, {"path": "pages/uzk/index", "type": "page"}, {"path": "pages/goods/detail/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/components/common-functions", "type": "page"}, {"path": "pages/personal/components/earnings-info", "type": "page"}, {"path": "pages/personal/components/free-shipping", "type": "freeshipping"}, {"path": "pages/personal/components/money-info", "type": "page"}, {"path": "pages/personal/components/swiper-info", "type": "page", "style": {"navigationBarTitleText": "swiper-info"}}, {"path": "pages/personal/components/user-profile", "type": "page"}, {"path": "pages/rank/components/rank-activity", "type": "page"}, {"path": "pages/rank/components/rank-content", "type": "page", "layout": "home"}, {"path": "pages/rank/components/rank-header", "type": "page", "layout": "default"}, {"path": "pages/shop/order/index", "type": "page"}, {"path": "pages/personal/commonFunctions/frequentlyAskedQuestions/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/commonFunctions/suggestionFeedback/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/commonFunctions/upgrades/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/earningsInfo/fans/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/earningsInfo/favorites/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/earningsInfo/order/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/earningsInfo/share/h2canvas", "type": "page"}, {"path": "pages/personal/earningsInfo/share/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/userProfile/bindAlipay/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/userProfile/withdraw/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/personal/userProfile/withdrawalRecord/index", "type": "page", "style": {"navigationBarTextStyle": "black"}}, {"path": "pages/search/components/hotList/index", "type": "page", "style": {"navigationBarTitleText": "hot-list"}}], "globalStyle": {"enablePullDownRefresh": false, "onReachBottomDistance": 100, "backgroundColor": "@bgColor", "backgroundColorBottom": "@bgColorBottom", "backgroundColorTop": "@bgColorTop", "backgroundTextStyle": "@bgTxtStyle", "navigationBarBackgroundColor": "#000000", "navigationBarTextStyle": "@navTxtStyle", "navigationBarTitleText": "省赚客", "navigationStyle": "custom", "app-plus": {"scrollIndicator": "none", "nvue": {"animation": {"duration": 0, "timingFunc": "linear"}}}}, "easycom": {"autoscan": true, "custom": {"^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue", "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "condition": {"current": 0, "list": [{"name": "11111111", "path": "pages/home/<USER>", "query": ""}]}, "tabBar": {"color": "#7A7E83", "selectedColor": "#fa4126", "borderStyle": "white", "iconWidth": "18px", "backgroundColor": "#fff", "list": [{"pagePath": "pages/home/<USER>", "iconPath": "static/shop.png", "selectedIconPath": "static/shop2.png", "text": "首页"}, {"pagePath": "pages/rank/index", "iconPath": "static/cat.png", "selectedIconPath": "static/cat2.png", "text": "榜单"}, {"pagePath": "pages/life/index", "iconPath": "static/life.png", "selectedIconPath": "static/life2.png", "text": "吃喝玩乐"}, {"pagePath": "pages/circle/index", "iconPath": "static/cart.png", "selectedIconPath": "static/cart2.png", "text": "分享"}, {"pagePath": "pages/personal/index", "iconPath": "static/mine.png", "selectedIconPath": "static/mine2.png", "text": "我的"}]}, "subPackages": []}