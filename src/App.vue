<script setup lang="ts">
import { queryBannerList, queryHotKey, queryJinGangList } from '@/services'

onLaunch(() => {
  console.log('App launched')
  initPermission()
  preloadHomePageData()
})

// 预加载一下首页的数据，接口再快也会有1秒左右的延迟
async function preloadHomePageData() {
  const [banner, jingang, cps, cps2, center, hotKey] = await Promise.all([
    queryBannerList({ position: 'home_top' }),
    queryJinGangList({ marketType: 'jinGang' }),
    queryBannerList({ position: 'home_center_right' }),
    queryBannerList({ position: 'home_center_left' }),
    queryBannerList({ position: 'home_center' }),
    queryHotKey({ minId: '1', tag: 'cycle' }),
  ])

  const homePageData = {
    banner,
    jingang,
    cps,
    cps2,
    center,
    hotKey: hotKey.data,
  }
  console.log('preload homePageData', homePageData)

  uniStorage.setItem('preload_home_page_data', homePageData)
}
</script>
