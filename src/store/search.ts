export const useSearchStore = createGlobalState(() => {
  const search = useStorage<string[]>('search', [], uniStorage)
  const setSearch = (_search: string) => {
    search.value = [...search.value, _search]
  }
  const clearSearch = () => {
    search.value = []
  }
  const addSearch = (searchText: string) => {
    if (!searchText)
      return
    // 如果存在就不添加
    if (search.value.includes(searchText))
      return
    setSearch(searchText)
  }

  const deleteSearch = (searchText: string) => {
    const index = search.value.indexOf(searchText)
    if (index !== -1) {
      const newArray = [...search.value.slice(0, index), ...search.value.slice(index + 1)]
      search.value = newArray
    }
  }

  return {
    search,
    addSearch,
    setSearch,
    clearSearch,
    deleteSearch,
  }
})
