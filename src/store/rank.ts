// 榜单组件相关状态
export interface RankStore {
  rankType: string
  rankTabs: string
}
export const useRankStore = createGlobalState(() => {
  const rankType = useStorage<RankStore['rankType']>('rankType', 'single')
  const rankTabs = useStorage<RankStore['rankTabs']>('rankTabs', 'TB')
  const setRankType = (_rank: RankStore['rankType']) => {
    rankType.value = _rank
  }
  const setRankTabs = (_rank: RankStore['rankTabs']) => {
    rankTabs.value = _rank
  }
  return {
    rankType,
    rankTabs,
    setRankTabs,
    setRankType,
  }
})
