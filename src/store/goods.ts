export const useGoodsStore = createGlobalState(() => {
  const goods = useStorage<appGoodsGoodsDetail.ResponseData | '{}'>('goods', '{}', uniStorage)
  const setGoods = (_goods: appGoodsGoodsDetail.ResponseData) => {
    goods.value = JSON.stringify(_goods)
  }

  const clipboardData = useStorage<string>('clipboardData', '', uniStorage)
  const setClipboardStore = (data: string) => {
    clipboardData.value = data
  }
  const delClipboardStore = () => {
    uniStorage.removeItem('clipboardData')
  }

  return {
    goods,
    setGoods,
    clipboardData,
    setClipboardStore,
    delClipboardStore,
  }
})
