export const useOverlaySearchStore = createGlobalState(() => {
  const overlaySearch = useStorage<boolean>('OverlaySearch', false, uniStorage)
  const setOverlaySearch = (_overlaySearch: boolean) => {
    overlaySearch.value = _overlaySearch
  }
  const overlaySearchList = useStorage<string[]>('OverlaySearchList', [], uniStorage)
  const setOverlaySearchList = (_search: string) => {
    overlaySearchList.value = [...overlaySearchList.value, _search]
  }
  const addOverlaySearchList = (searchText: string) => {
    if (!searchText)
      return false
    // 去除制表符
    searchText = searchText.replace(/\s+/g, '')
    // 如果存在就不添加
    if (containsOverlaySearchList(searchText))
      return false
    setOverlaySearchList(searchText)
  }
  const deleteOverlaySearchList = (searchText: string) => {
    const index = overlaySearchList.value.indexOf(searchText)
    if (index !== -1) {
      overlaySearchList.value = [...overlaySearchList.value.slice(0, index), ...overlaySearchList.value.slice(index + 1)]
    }
  }
  const clearOverlaySearchList = () => {
    overlaySearchList.value = []
  }
  const containsOverlaySearchList = (searchText: string) => {
    // 去除制表符
    searchText = searchText.replace(/\s+/g, '')
    return overlaySearchList.value.includes(searchText)
  }
  return {
    overlaySearch,
    setOverlaySearch,
    overlaySearchList,
    setOverlaySearchList,
    addOverlaySearchList,
    deleteOverlaySearchList,
    clearOverlaySearchList,
    containsOverlaySearchList,
  }
})
