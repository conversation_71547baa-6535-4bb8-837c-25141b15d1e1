import { appInfo } from '@/common'

type UserInfo = appUserLoginAccountPwd.Response['data']
export const useAuthStore = createGlobalState(() => {
  // https://stackoverflow.com/questions/********/vueuse-usestorage-not-updating-stored-object-values 解决pinia 存储json格式序列化问题
  const userInfo = useStorage<UserInfo>('userInfo', {}, localStorage, { mergeDefaults: true })
  // 这个设计，有待斟酌嗷~
  // const userToken = useStorage<string>('userToken', '', uniStorage)
  const userToken = useStorage<string>('userToken', appInfo.defaultTk, uniStorage)

  const isLogin = computed(() => !!userInfo.value?.token)

  const setUserInfo = (newUserInfo?: UserInfo) => {
    if (newUserInfo)
      userInfo.value = newUserInfo
  }

  const setUserToken = (newUserToken?: string) => {
    if (newUserToken)
      userToken.value = newUserToken
  }

  const restore = (token: string, info?: object) => {
    console.log('restore', token, info)
    uniStorage.setItem('userToken', token)
    if (info)
      uniStorage.setItem('userInfo', info)
  }

  const logout = () => {
    userInfo.value = {}
    // userToken.value = ''
    userToken.value = appInfo.defaultTk
  }

  return {
    userInfo,
    isLogin,
    userToken,
    setUserInfo,
    setUserToken,
    logout,
    restore,
  }
})
