<script setup lang="ts">
import { usePage } from '@uni-helper/uni-use'

const page = usePage()
function handleClickLeft() {
  if (page.value.$page.meta.navigateBack || page.value.options?.navigateBack) {
    uni.navigateBack()
  }
  else {
    uni.switchTab({
      url: '/pages/home/<USER>',
    })
  }
}
</script>

<template>
  <page-wraper
    class="no-footer-layout"
  >
    <!-- navbar docs  https://wot-design-uni.cn/component/navbar.html  -->
    <!-- 关于设置 style 和 class 的优先级 -->
    <!-- 在 CSS 中，style 属性（内联样式）的优先级高于通过类选择器、ID选择器或元素选择器应用的样式。这意味着内联样式会覆盖外部样式表或内部样式表中定义的样式。
      以下是 CSS 优先级规则的简要概述：
      内联样式：style 属性中的样式（最高优先级）。
      ID 选择器：#id（例如，#example）。
      类、伪类、属性选择器：.class、:hover、[type="text"]（例如，.example）。
      元素、伪元素选择器：div、p、::before（例如，div）。 -->

    <!-- 当前设置custom-class 无效？？？？ -->
    <wd-navbar
      :custom-style="`${page?.$page.meta.navbarStyle} z-999 !important`"
      :custom-class="`${page?.$page.meta.navbarClass} z-998 !important`"
      :title="page?.$page.meta.navigationBar.titleText"
      :left-text="page?.$page.meta.leftText" left-arrow :bordered="false"
      @click-left="handleClickLeft"
    />
    <slot />
  </page-wraper>
</template>

<style lang="scss" scoped>
// :deep(.wd-navbar){
//     background-color: 'red' ;
// }
</style>
