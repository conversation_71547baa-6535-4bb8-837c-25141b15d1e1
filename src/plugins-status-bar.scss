/* #region  设置状态栏样式
   @doc https://blog.csdn.net/ppl_Rrj/article/details/*********
*/
.status-bar {
  background-color: #fff;
  width: 100%;
  height: var(--status-bar-height);
  position: fixed;
  top: 0;
  z-index: 5320;
  // padding-top: var(--status-bar-height);
  /* #ifdef H5 */
  height: 25px; // 模拟小程序状态栏高度。https://zh.uniapp.dcloud.io/tutorial/syntax-css.html
  /* #endif */
}

.status-bar-padding {
  padding-top: calc(var(--status-bar-height)/2);
  /* #ifdef H5 */
  padding-top: 12px;
  /* #endif */
}
