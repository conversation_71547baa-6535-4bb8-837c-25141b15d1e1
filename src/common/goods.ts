interface Goods {
  PlatformCode: appGoodsList.Params['platformCode'] // 平台编码
  icon: string
  text: string
}

type GoodsList = Goods[]

export const goodsList: GoodsList = [
  {
    PlatformCode: 'DY',
    icon: 'https://img.yzcdn.cn/v2/image/DY_logo.png',
    text: ' 抖音',
  },
  {
    PlatformCode: 'JD',
    icon: 'https://img.yzcdn.cn/v2/image/DY_logo.png',
    text: ' 京东',
  },
  // {
  //   PlatformCode: 'KL',
  //   icon: 'https://img.yzcdn.cn/v2/image/DY_logo.png',
  //   text: ' 考拉',
  // },
  {
    PlatformCode: 'PDD',
    icon: 'https://img.yzcdn.cn/v2/image/DY_logo.png',
    text: ' 拼多多',

  },
  {
    PlatformCode: 'TB',
    icon: 'https://img.yzcdn.cn/v2/image/DY_logo.png',
    text: ' 淘宝',
  },
  {
    PlatformCode: 'WPH',
    icon: 'https://img.yzcdn.cn/v2/image/DY_logo.png',
    text: ' 唯品会',
  },
]

export function getGoodsText(platformCode: appGoodsList.Params['platformCode'] | string, defaultText: string = ''): string {
  const goods = goodsList.find(item => item.PlatformCode === platformCode)
  return goods?.text || defaultText
}
