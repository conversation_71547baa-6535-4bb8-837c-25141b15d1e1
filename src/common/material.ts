// 1、关于我们:https://szk.juwatech.cn/material/detail/170，
// 2、商务合作:https://szk.juwatech.cn/material/detail/168，
// 3、推广规范:https://szk.juwatech.cn/material/detail/167，
// 5、专属客服:https://szk.juwatech.cn/material/detail/169，
// 6、隐私政策:https://szk.juwatech.cn/material/detail/171
// 下面是两个定死的URL
// 赚佣金攻略:https://szk.juwatech.cn/material/detail/172
// 赚佣金视频教程:https://szk.juwatech.cn/material/detail/174

import { appInfo } from './appInfo'

interface material {
  type: appMaterialQueryByType.Params['type']
  name: string
  url: string
}
export const materials: material[] = [
  { type: '1', name: '关于我们', url: `${appInfo.serverApi}material/detail/170` },
  { type: '2', name: '商务合作', url: `${appInfo.serverApi}material/detail/168` },
  { type: '3', name: '推广规范', url: `${appInfo.serverApi}material/detail/167` },
  { type: '5', name: '专属客服', url: `${appInfo.serverApi}material/detail/169` },
  { type: '6', name: '隐私政策', url: `${appInfo.serverApi}material/detail/171` },
]
