interface AppInfo {
  appName: string
  version: string
  company: string
  legalEntity: string
  author?: string
  description?: string
  serverApi: string
  baseUrl: string
  taobaoId: string
  defaultTk: string
  packageName: string
}

export const appInfo: AppInfo = {
  appName: '省赚客',
  version: '1.5.8',
  company: 'juwatech.cn',
  legalEntity: 'juwatech',
  author: 'h7ml',
  description: '省赚客是一款集成了淘宝、天猫、京东、拼多多、唯品会、苏宁、抖音、快手、美团、饿了么、话费、电费、打车、加油、电影票等本地生活主流消费购物领券平台，满足用户日常吃喝玩乐衣食住行的超级聚合导购省钱赚钱APP',
  serverApi: 'https://szk.juwatech.cn/',
  baseUrl: 'https://app.juwatech.cn',
  taobaoId: '34494988',
  defaultTk: 'TVRJek5EVTI=',
  packageName: 'cn.juwatech.szk',
}
