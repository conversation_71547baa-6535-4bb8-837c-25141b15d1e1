/* eslint-disable */

import { fetchUserCenter, privPubBind, privPubBindInfo } from "@/services"
import { appInfo } from "@/common/appInfo"
import { useAuthStore } from "@/store/user"

const { setUserInfo } = useAuthStore()

async function openOthers(data: {}, platform: string, title?: string) {
  if (!data)
    return false

  console.log('[jump >>>] 方法入参', platform, title, data)

  let openUrl = data.shortClickUrl || data.clickUrl || data.goodsUrl
  // H5中任何一个url都能访问
  // #ifdef H5
  console.log('[h5 jump] 跳转地址', platform, openUrl)
  location.href = openUrl
  // #endif

  // APP中需要替换scheme协议，本来也可以统一处理，但是接口返回的数据格式无法统一
  // #ifdef APP-PLUS
  if (platform === 'TB') {
    // 淘宝需要单独授权
    if (data.needAuth) {
      const info = await privPubBindInfo()
      console.log('查询淘宝授权结果', info)
      if (!info || !info.relation_id) {
        console.log('用户还未进行过淘宝授权，立即触发淘宝授权')
        await openTaobaoApp()
        return false
      }
    }
    // 兼容一下活动转链
    if (data.shortClickUrl) {
      openUrl = 'taobao' + data.shortClickUrl.substring(data.shortClickUrl.indexOf(':'))
    } else {
      openUrl = 'taobao' + data.clickUrl.substring(data.clickUrl.indexOf(':'))
    }
  }
  if (platform === 'JD') {
    const parm = {
      category: 'jump',
      des: 'getCoupon',
      url: data.shortClickUrl || data.clickUrl,
      sourceType: 'PCUBE_CHANNEL',
    }
    openUrl = `openApp.jdMobile://virtual?params=${encodeURI(
      JSON.stringify(parm),
    )}`
  }
  if (platform === 'PDD') {
    openUrl = `pinduoduo:${encodeURI(data.clickUrl.substring(data.clickUrl.indexOf(':')))}`
  }
  // if (platform === 'KL') {
  //   openUrl = 'kaola' + data.clickUrl.substring(data.clickUrl.indexOf(':'))
  // }
  if (platform === 'WPH') {
    openUrl = data.clickUrl
  }
  if (platform === 'DYGROUP') {
    openUrl = data.shortClickUrl
  }
  if (platform === 'SN') {
    openUrl = `suning:${encodeURI(data.clickUrl.substring(data.clickUrl.indexOf(':')))}`
  }

  console.log('[app jump] 跳转地址', platform, openUrl)

  if (!platform || platform === '') {
    // 跳小程序
    if (data.wxMiniprogramPath && data.wxMiniprogramPath !== '') {
      console.log('[app jump] platform为空，说明是活动转链，wxMiniprogramPath不为空，尝试跳转微信小程序', data.wxMiniAppId, data.wxMiniprogramPath)
      await openWeChatMiniProgram(data.wxMiniAppId, data.wxMiniprogramPath, data.clickUrl)
    }
    // 如果小程序打不开，那就走iframe
    else {
      console.log('[app jump] platform为空，说明是活动转链，wxMiniprogramPath为空，直接走iframe打开页面', title, openUrl)
      uni.navigateTo({
        url: `/pages/iframe?url=${encodeURIComponent(openUrl)}&name=${title}`,
      })
    }
  } else {
    // 上面的逻辑都走了，说明APP已经被处理过，按照优先级，再处理一次小程序
    if (data.wxMiniprogramPath && data.wxMiniprogramPath !== '') {
      console.log('[app jump] APP打不开，降级为跳转微信小程序', data.wxMiniprogramPath)
      await openWeChatMiniProgram(data.wxMiniAppId, data.wxMiniprogramPath, data.clickUrl)
    } else {
      plus.runtime.openURL(openUrl, function (e) {
        console.log(`[app jump] 通过scheme协议调用手机浏览器无法打开${platform}，可能不是一个完整的协议URL`, e)
        // 再次兜底，跳小程序
        if (data.wxMiniprogramPath && data.wxMiniprogramPath !== '') {
          console.log('[app jump] APP打不开，降级为跳转微信小程序', data.wxMiniprogramPath)
          openWeChatMiniProgram(data.wxMiniAppId, data.wxMiniprogramPath, data.clickUrl)
        }
        // 上面都没用的话，到这里就只能浏览器访问了
        if (data.shortClickUrl.startsWith('http://') || data.shortClickUrl.startsWith('https://')) {
          console.log('[app jump] APP打不开，微信小程序也打不开，兜底为跳转H5', data.shortClickUrl)
          plus.runtime.openURL(data.shortClickUrl)
        }
        else if (data.clickUrl.startsWith('http://') || data.clickUrl.startsWith('https://')) {
          console.log('[app jump] APP打不开，微信小程序也打不开，兜底为跳转H5', data.clickUrl)
          plus.runtime.openURL(data.clickUrl)
        }
        else {
          console.log('[app jump] APP打不开，微信小程序也打不开，H5也打不开，fuckyou！', platform, data)
        }
      })
    }
  }
  // #endif
}

// 拉起手淘进行授权
async function openTaobaoApp() {
  // 加载阿里百川插件
  const Alibcsdk = uni.requireNativePlugin('UZK-Alibcsdk')

  Alibcsdk.init((result) => {
    console.log('[uzk] init结果', result)
    if (!result.status) {
      uni.showToast({
        icon: 'none',
        title: '授权失败，请稍后重试',
      })
      return false
    }
    Alibcsdk.login((result) => {
      console.log('[uzk] login结果', result)
      if (!result.status) {
        uni.showToast({
          icon: 'none',
          title: '授权失败，请稍后重试',
        })
        return false
      }
      Alibcsdk.qdByhide({
        url: `https://oauth.m.taobao.com/authorize?response_type=token&client_id=${appInfo.taobaoId}&redirect_uri=${encodeURI(`${appInfo.serverApi}tao/tbCallBack`)}&state=123456&view=wap`,
      }, (result) => {
        console.log('[uzk] qdByhide结果', result)
        if (!result.status || result.data === '') {
          uni.showToast({
            icon: 'none',
            title: '授权失败，请稍后重试',
          })
          return false
        }
        privPubBind({
          session: result.data.access_token
        }).then((res) => {
          console.log('绑定淘宝授权结果', res)
          if (res.code === 1001) {
            uni.showToast({icon: 'none', title: '授权成功',})
            // 刷新缓存中的user数据
            const info = fetchUserCenter()
            if (info) {
              setUserInfo(info)
            }
          }
        }).catch((err) => {
          console.log('绑定淘宝授权异常', err)
        })
      })
    })
  })
}

// 跳转到微信小程序，需要去微信开放平台申请appid
async function openWeChatMiniProgram(miniProgramId: string, path: string, clickUrl: string) {
  console.log('微信小程序跳转', miniProgramId, path, clickUrl)
  // 获取分享服务列表
  plus.share.getServices((res) => {
    console.log('shares', res)
    const weChatService = res.find(i => i.id === 'weixin')
    if (!weChatService) {
      uni.showModal({
        title: '提示',
        content: '本机未检测到对应客户端，是否打开浏览器访问页面？',
        success: function (res) {
          if (res.confirm) {
            // 这个不一定有值，也可以直接内嵌web-view打开
            if (clickUrl) {
              plus.runtime.openURL(clickUrl, function (e) {
                console.log('调用手机浏览器失败', e)
                uni.showToast({
                  title: '应用未安装，浏览器暂不可打开，请前往商店下载应用',
                  icon: 'none'
                })
              })
            }
          }
        }
      })
    } else {
      weChatService.launchMiniProgram({
        id: miniProgramId,
        path: path,
        // type: 0,
        success: () => console.log('打开小程序成功'),
        fail: (e) => console.error('打开小程序失败', e)
      })
    }
  }, (e) => console.error('获取分享服务列表失败', e))
}

export {
  openOthers,
  openTaobaoApp,
  openWeChatMiniProgram
}
