/**
 * @name     广告位信息列表 (省赚客)
 * @base     /
 * @path     /app/banner/bannerList
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/5/18 22:59:57
 */

declare namespace appBannerBannerList {
  interface Params {
    /** banner广告位位置 */
    position: 'home_center' | 'home_center_left' | 'home_center_right' | 'home_top' | 'personalCenter_down' | 'personalCenter_top'
  }

  interface ResponseDataItem {
    /** 推广活动id */
    activityid?: string
    /** 分类名称 */
    categoryname?: string
    /** banner id */
    id?: number
    /** 金刚位名称 */
    jingangname?: string
    /** 跳转类型 */
    jump_type?: string
    /** 跳转地址 */
    jump_url?: string
    /** 图片地址 */
    picture_url?: string
    /** 平台类型 */
    platformcode?: string
    /** 图片位置 */
    position?: string
    /** 标签地址 */
    tagurl?: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseDataItem[]
    /** 返回消息 */
    msg?: string
  }
}
