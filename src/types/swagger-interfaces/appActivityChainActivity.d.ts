/**
 * @name     联盟CPS活动转链 (省赚客)
 * @base     /
 * @path     /app/activity/chainActivity
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/5/12 18:56:56
 */

declare namespace appActivityChainActivity {
  interface Params {
    /** 活动id(主键ID) */
    id: string
  }

  interface ResponseData {
    /** 商品转链(可点击) */
    clickUrl?: string
    /** 商品转链（口令） */
    goodsUrl?: string
    /** 是否需要备案授权 */
    needAuth?: boolean
    /** 短链接(可点击) */
    shortClickUrl?: string
    wxMiniAppId?: string
    wxMiniAppSource?: string
    wxMiniprogramPath?: string
    wxQrcodeUrl?: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
