/**
 * @name     每日线报 (省赚客)
 * @base     /
 * @path     /app/circleMaterial/queryTelegram
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/22 20:32:00
 */

declare namespace appCircleMaterialQueryTelegram {
  interface Params {
    /** 关键词 */
    keyword?: string
    /** 分类 */
    limit: string
    /** 分页 */
    pageNo: string
    /** 默认0全部、10000券优惠、10001天猫超市、10002整点抢购、10003淘宝实时线报、10004京东实时线报 */
    type: string
  }

  interface ResponseDataListItem {
    /** 线报内容列表 */
    contentList?: any[]
    /** 线报多图 */
    images?: string[]
    /** 平台code */
    platCode?: string
    /** 平台类型：1. 淘宝、2. 京东 */
    platform?: number
    /** 开始时间 */
    startTime?: string
  }

  interface ResponseData {
    endRow?: number
    hasNextPage?: boolean
    hasPreviousPage?: boolean
    isFirstPage?: boolean
    isLastPage?: boolean
    list?: ResponseDataListItem[]
    navigateFirstPage?: number
    navigateLastPage?: number
    navigatePages?: number
    navigatepageNums?: number[]
    nextPage?: number
    pageNum?: number
    pageSize?: number
    pages?: number
    prePage?: number
    size?: number
    startRow?: number
    total?: number
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
