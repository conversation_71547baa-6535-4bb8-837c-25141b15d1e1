/**
 * @name     团购CPS转链&外卖红包转链 (省赚客)
 * @base     /
 * @path     /app/groupBuy/chainGroupBuy
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   12/30/2024, 10:46:06 PM
 */

declare namespace appGroupBuyChainGroupBuy {
  interface Params {
    /** 活动物料ID(与商品ID二选一)；7(美团红包，channel传MTCPS)，10144(饿了么天天领红包，channel传ELMCPS)，10607(饿了么消费日红包，channel传ELMCPS)，10817(饿了么特价外卖红包，channel传ELMCPS) */
    actId?: string
    /** 只有输入skuViewId时才需要传本字段；当字段platform为1，选择到家及其他业务类型时：5 医药，不填则默认null，表示外卖商品券；当字段platform为2，选择到店业务类型时：1、美食，2、休闲生活 ，3、酒店 ，4、门票 */
    bizLine?: string
    /** 渠道：MTCPS,ELMCPS,DYCPS */
    channel: string
    /** 1、 H5长链接；2、 H5短链接；3、 deeplink(唤起)链接；4、 微信小程序唤起路径 */
    linkType: number
    /** 输入skuViewId时才需要传本字段：1 到家及其他业务类型，2 到店业务类型 */
    platform: number
    /** 商品id(与物料ID二选一) */
    skuViewId?: string
  }

  interface ResponseData {
    /** 商品转链(可点击) */
    clickUrl?: string
    /** 商品转链（口令） */
    goodsUrl?: string
    /** 是否需要备案授权 */
    needAuth?: boolean
    /** 平台编号 */
    platformCode?: string
    /** 短链接(可点击) */
    shortClickUrl?: string
    wxMiniAppId?: string
    wxMiniAppSource?: string
    wxMiniprogramPath?: string
    wxQrcodeUrl?: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
