/**
 * @name     我的粉丝 (省赚客)
 * @base     /
 * @path     /app/user/myfans
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/22 20:32:06
 */

declare namespace appUserMyfans {
  interface Params {
    /** 分页条数 */
    limit: number
    /** 当前页数 */
    pageNo: number
  }

  interface ResponseDataListItem {
    /** 用户账号 */
    account?: string
    /** 注册时间 */
    create_time?: string
    /** 用户是否激活(一个以上有效订单视为激活)，true：已激活，false：未激活 */
    enabled?: string
    /** 用户头像 */
    fans_img?: string
    /** 用户昵称 */
    fans_nick?: string
    /** 用户等级，1初级达人，2钻石达人，3超级达人 */
    tk_user_level?: number
    /** 用户状态 1-正常、2-禁用 */
    user_enable?: number
  }

  interface ResponseData {
    endRow?: number
    hasNextPage?: boolean
    hasPreviousPage?: boolean
    isFirstPage?: boolean
    isLastPage?: boolean
    list?: ResponseDataListItem[]
    navigateFirstPage?: number
    navigateLastPage?: number
    navigatePages?: number
    navigatepageNums?: number[]
    nextPage?: number
    pageNum?: number
    pageSize?: number
    pages?: number
    prePage?: number
    size?: number
    startRow?: number
    total?: number
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
