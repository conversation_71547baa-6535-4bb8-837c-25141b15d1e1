/**
 * @name     CPS活动集合列表 (省赚客)
 * @base     /
 * @path     /app/activity/queryActivityList
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/22 20:31:58
 */

declare namespace appActivityQueryActivityList {
  interface Params {
    /** 活动集合ID:dstop:电商会场榜集合;bybt:百亿补贴集合;cjhb:超级红包集合;dcjy:打车加油集合;sqwm:省钱外卖集合;mpjpcp:门票机票车票集合 */
    activeid: 'bybt' | 'cjhb' | 'dcjy' | 'dstop' | 'mpjpcp' | 'sqwm'
  }

  interface ResponseDataItem {
    actid?: string
    /** app是否展示  1展示   2不展示 */
    appshow?: number
    create_time?: string
    description?: string
    /** 活动结束时间 */
    end_time?: string
    icon?: string
    /** 活动ID（联盟cps活动转链必传参数） */
    id?: number
    /** 活动大图 */
    img?: string
    /** 电商平台编号 */
    platform?: number
    /** 活动开始时间 */
    start_time?: string
    /** 状态 1 已上线  2已下线 */
    state?: number
    /** 活动名称 */
    title?: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseDataItem[]
    /** 返回消息 */
    msg?: string
  }
}
