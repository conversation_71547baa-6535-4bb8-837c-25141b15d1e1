/**
 * @name     批量删除收藏的商品 (省赚客)
 * @base     /
 * @path     /app/goodsCollect/batchDeleteCollect
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/5/12 18:56:58
 */

declare namespace appGoodsCollectBatchDeleteCollect {
  interface Params {
    /** 商品id */
    ids: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: any
    /** 返回消息 */
    msg?: string
  }
}
