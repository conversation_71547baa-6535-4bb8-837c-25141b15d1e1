/**
 * @name     查询用户备案信息 (省赚客)
 * @base     /
 * @path     /app/privatePublisher/bindInfo
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/5 06:16:12
 */

declare namespace appPrivatePublisherBindInfo {
  interface ResponseData {
    account_name?: string
    app_key?: string
    code?: string
    create_time?: string
    id?: number
    relation_id?: number
    session?: string
    session_expiration_time?: string
    special_id?: number
    status?: number
    user_id?: number
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
