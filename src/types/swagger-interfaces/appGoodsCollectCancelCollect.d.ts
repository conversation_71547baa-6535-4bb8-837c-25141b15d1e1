/**
 * @name     取消收藏的商品 (省赚客)
 * @base     /
 * @path     /app/goodsCollect/cancelCollect
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/5/12 18:56:58
 */

declare namespace appGoodsCollectCancelCollect {
  interface Params {
    /** 商品Id */
    goodsId: string
    /** 平台类型 */
    platformCode: 'DY' | 'JD' | 'PDD' | 'TB' | 'WPH'
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: any
    /** 返回消息 */
    msg?: string
  }
}
