/**
 * @name     商品列表（榜单） (省赚客)
 * @base     /
 * @path     /app/goods/list
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/5/18 22:59:57
 * @ignore
 * @docs https://github.com/lanten/swagger-to-types/tree/1ec2ea100049dbe74323bb268b80450182b19880?tab=readme-ov-file#%E5%BF%BD%E7%95%A5%E4%B8%80%E9%94%AE%E6%9B%B4%E6%96%B0
 */

declare namespace appGoodsList {
  interface Params {
    /** 分页条数 */
    limit: string
    /** 当前页数 */
    pageNo: string
    /** 平台编号 */
    platformCode: 'DY' | 'JD' | 'KL' | 'PDD' | 'TB' | 'WPH' | 'QW'
    /** 新增价格类型(1.精选专区；2.9.9专区；3.6.9专区；4.3.9专区（默认1）) */
    priceType?: string
    /** 查询类型：list-榜单，tmall-天猫，lowprice - 精选低价，tmallchaoshi - 天猫超市 */
    queryType?: string
    /** 查询值：榜单（1(品牌榜)、5(高佣榜)、6(品牌实时榜)、7(定向计划)、8(细分类目榜-待使用)、11(热销榜)、12(今日榜)） */
    queryValue?: string
    /** 子查询的值(子类目) */
    subQuery?: string
  }

  interface ResponseDataListItem {
    /** 实际/到手价格（去除券） */
    actualPrice?: string
    /** 渠道侧的商品ID */
    channelGoodsId?: string
    /** 分类ID */
    cid?: string
    /** 比价状态(主要针对淘宝拼多多)：true存在比价,false为正常 */
    comparePriceState?: boolean
    /** 优惠券金额 */
    couponAmount?: string
    /** 优惠券信息-优惠券结束时间 yyyy-MM-dd HH:mm:ss */
    couponEndTime?: string
    /** 优惠券信息-优惠券开始时间 yyyy-MM-dd HH:mm:ss */
    couponStartTime?: string
    /** 优惠券地址 */
    couponUrl?: string
    /** 商品描述/推荐理由 */
    description?: string
    /** 折扣 */
    discount?: string
    /** 商品类型-淘宝：0-淘宝，1-天猫，京东：0-非自营，1-自营 */
    goodsType?: number
    /** 商品地址 */
    goodsUrl?: string
    /** 用户等级 */
    grade?: number
    /** 商品ID */
    id?: string
    /** 商品小图列表 */
    imageUrls?: string[]
    /** 最大预估收益 */
    maxProfit?: string
    /** 是否需要备案授权 */
    needAuth?: boolean
    /** 初始价格（电商平台的销售金额） */
    orgPrice?: string
    /** 商品主图 */
    picUrl?: string
    /** 平台编号 */
    platformCode?: string
    /** 预估收益 */
    profit?: string
    /** 佣金比例 */
    ratio?: number
    /** 销售数量 */
    salesCount?: string
    /** 购买省 */
    savedAmount?: string
    /** 店铺名称 */
    shopTitle?: string
    /** 商品副标题 */
    subTitle?: string
    /** 商品所属细分类目id */
    subdivisionId?: number
    /** 商品所属细分类目名称 */
    subdivisionName?: string
    /** 商品所属细分类目排名 */
    subdivisionRank?: number
    /** 商品标题 */
    title?: string
    /** 口令 */
    tkl?: string
    /** 微信小程序用户名 */
    wxAppUserName?: string
  }

  interface ResponseData {
    endRow?: number
    hasNextPage?: boolean
    hasPreviousPage?: boolean
    isFirstPage?: boolean
    isLastPage?: boolean
    list?: ResponseDataListItem[]
    navigateFirstPage?: number
    navigateLastPage?: number
    navigatePages?: number
    navigatepageNums?: number[]
    nextPage?: number
    pageNum?: number
    pageSize?: number
    pages?: number
    prePage?: number
    size?: number
    startRow?: number
    total?: number
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
