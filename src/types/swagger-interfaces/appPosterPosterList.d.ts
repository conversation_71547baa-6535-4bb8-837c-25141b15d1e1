/**
 * @name     分享海报列表 (省赚客)
 * @base     /
 * @path     /app/poster/posterList
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/22 20:27:29
 */

declare namespace appPosterPosterList {
  interface ResponseData {
    /** 邀请码 */
    inviteCode?: string
    /** 邀请链接 */
    inviteUrl?: string
    /** 海报图片地址集合 */
    picture_urls?: string[]
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
