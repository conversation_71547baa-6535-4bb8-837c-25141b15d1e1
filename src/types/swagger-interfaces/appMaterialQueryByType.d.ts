/**
 * @name     特殊类型资源详情 (省赚客)
 * @base     /
 * @path     /app/material/queryByType
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/5/12 23:20:41
 */

declare namespace appMaterialQueryByType {
  interface Params {
    /** 资源类型：1、关于我们，2、商务合作，3、推广规范，5、专属客服，6、隐私政策 */
    type: '1' | '2' | '3' | '5' | '6'
    /** 资源类型：1、关于我们:https://szk.juwatech.cn/material/detail/170，2、商务合作:https://szk.juwatech.cn/material/detail/168，3、推广规范:https://szk.juwatech.cn/material/detail/167，5、专属客服:https://szk.juwatech.cn/material/detail/169，6、隐私政策:https://szk.juwatech.cn/material/detail/171 */
    url?: string
  }

  interface ResponseData {
    /** 内容 */
    content?: string
    /** 封面 */
    cover_url?: string
    created_by?: string
    created_time?: string
    /** 是否激活  0激活  1未激活 */
    enabled?: number
    id?: number
    /** 备注 */
    remark?: string
    sort?: number
    /** 标题 */
    title?: string
    /** 类型(1、关于我们，2、商务合作，3、推广规范，4、常见问题，5、专属客服，6、隐私政策) */
    type?: string
    /** 视频链接 */
    video_link?: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
