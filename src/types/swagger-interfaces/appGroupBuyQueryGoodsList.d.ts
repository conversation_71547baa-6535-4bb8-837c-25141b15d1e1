/**
 * @name     团购cps商品列表&搜索 (省赚客)
 * @base     /
 * @path     /app/groupBuy/queryGoodsList
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   12/30/2024, 10:46:22 PM
 */

declare namespace appGroupBuyQueryGoodsList {
  interface Params {
    /** 当字段platform为1，到家及其他业务类型；当字段platform为2，到店业务类型时：1、美食，2、休闲生活，3、酒店，4、门票 不填则默认1 */
    bizLine: number
    /** 渠道：MTCPS,DYCPS,ELMCPS */
    channel: string
    /** 搜索关键词 */
    keyword: string
    /** 纬度(保留3为小数)；针对到店、到家业务类型 */
    latitude: string
    /** 分页大小,默认20 */
    limit: number
    /** 经度(保留3为小数)；针对到店、到家业务类型 */
    longitude: string
    /** 页数，默认页数1 */
    pageNo: number
    /** 1 到家及其他业务类型，2 到店业务类型 */
    platform: number
  }

  interface ResponseDataListItem {
    /** 实际/到手价格（去除券） */
    actualPrice?: string
    /** 渠道侧的商品ID */
    channelGoodsId?: string
    /** 分类ID */
    cid?: string
    /** 比价状态(主要针对淘宝拼多多)：true存在比价,false为正常 */
    comparePriceState?: boolean
    /** 优惠券金额 */
    couponAmount?: string
    /** 优惠券信息-优惠券结束时间 yyyy-MM-dd HH:mm:ss */
    couponEndTime?: string
    /** 优惠券信息-优惠券开始时间 yyyy-MM-dd HH:mm:ss */
    couponStartTime?: string
    /** 优惠券地址 */
    couponUrl?: string
    /** 商品描述/推荐理由 */
    description?: string
    /** 折扣 */
    discount?: string
    /** 商品类型-淘宝：0-淘宝，1-天猫，京东：0-非自营，1-自营 */
    goodsType?: number
    /** 商品地址 */
    goodsUrl?: string
    /** 用户等级 */
    grade?: number
    /** 商品ID */
    id?: string
    /** 商品小图列表 */
    imageUrls?: string[]
    /** 最大预估收益 */
    maxProfit?: string
    /** 是否需要备案授权 */
    needAuth?: boolean
    /** 初始价格（电商平台的销售金额） */
    orgPrice?: string
    /** 商品主图 */
    picUrl?: string
    /** 平台编号 */
    platformCode?: string
    /** 预估收益 */
    profit?: string
    /** 佣金比例 */
    ratio?: number
    /** 销售数量 */
    salesCount?: string
    /** 购买省 */
    savedAmount?: string
    /** 店铺名称 */
    shopTitle?: string
    /** 商品副标题 */
    subTitle?: string
    /** 商品所属细分类目id */
    subdivisionId?: number
    /** 商品所属细分类目名称 */
    subdivisionName?: string
    /** 商品所属细分类目排名 */
    subdivisionRank?: number
    /** 商品标题 */
    title?: string
    /** 口令 */
    tkl?: string
    /** 微信小程序ID */
    wxMiniAppId?: string
    /** 微信小程序路径 */
    wxMiniprogramPath?: string
  }

  interface ResponseData {
    endRow?: number
    hasNextPage?: boolean
    hasPreviousPage?: boolean
    isFirstPage?: boolean
    isLastPage?: boolean
    list?: ResponseDataListItem[]
    navigateFirstPage?: number
    navigateLastPage?: number
    navigatePages?: number
    navigatepageNums?: number[]
    nextPage?: number
    pageNum?: number
    pageSize?: number
    pages?: number
    prePage?: number
    size?: number
    startRow?: number
    total?: number
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
