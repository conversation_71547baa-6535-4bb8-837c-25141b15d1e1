/**
 * @name     私域用户备案 (省赚客)
 * @base     /
 * @path     /app/privatePublisher/bind
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/5 06:16:12
 */

declare namespace appPrivatePublisherBind {
  interface Params {
    /** 授权应用的appkey(可为空) */
    appId?: string
    /** 用户授权登录后返回的code */
    code?: string
    /** 用户授权登录的session(token) */
    session?: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: any
    /** 返回消息 */
    msg?: string
  }
}
