/**
 * @name     订单列表 (省赚客)
 * @base     /
 * @path     /app/order/orderList
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/5/18 22:59:57
 */

declare namespace appOrderOrderList {
  interface Params {
    /** 分页条数 */
    limit: number
    /** 订单类型,0全部、1我的订单、2推广订单 */
    orderType: 0 | 1 | 2
    /** 当前页数 */
    pageNo: number
    /** 订单状态,0全部、3结算、12付款、13失效、16收货 */
    status: number
  }

  interface ResponseDataListItem {
    /** 商品子订单号 */
    childOrderId?: string
    /** 商品id */
    itemId?: string
    /** 商品图片 */
    itemImg?: string
    /** 商品数量 */
    itemNum?: number
    /** 商品标题 */
    itemTitle?: string
    /** 订单结算时间 */
    orderEarningTime?: string
    /** 商品主订单号 */
    orderId?: string
    /** 订单付款的时间，该时间同步淘宝，可能会略晚于买家在淘宝的订单创建时间 */
    orderPaidTime?: string
    /** 订单状态( 3结算  12付款 13退款  15维权  16已收货) */
    payStatus?: number
    /** 买家拍下付款的金额 */
    payTotalPrice?: number
    /** 平台id  1 淘宝 2 京东 3拼多多 4 美团  5唯品会  6抖音  7快手  8考拉  9苏宁 10CPS活动 11滴滴  12饿了么 */
    platformId?: number
    /** 0 含义为非维权 1 含义为维权订单 */
    refundTag?: number
    /** 预估佣金 */
    shareFee?: number
  }

  interface ResponseData {
    endRow?: number
    hasNextPage?: boolean
    hasPreviousPage?: boolean
    isFirstPage?: boolean
    isLastPage?: boolean
    list?: ResponseDataListItem[]
    navigateFirstPage?: number
    navigateLastPage?: number
    navigatePages?: number
    navigatepageNums?: number[]
    nextPage?: number
    pageNum?: number
    pageSize?: number
    pages?: number
    prePage?: number
    size?: number
    startRow?: number
    total?: number
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
