/**
 * @name     文案转链 (省赚客)
 * @base     /
 * @path     /app/goods/chainTransfer
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/5/12 18:56:59
 */

declare namespace appGoodsChainTransfer {
  interface Params {
    /** 转链文案 */
    keyword: string
  }

  interface ResponseDataGoodsListVoListItem {
    /** 实际/到手价格（去除券） */
    actualPrice?: string
    /** 渠道侧的商品ID */
    channelGoodsId?: string
    /** 分类ID */
    cid?: string
    /** 比价状态(主要针对淘宝拼多多)：true存在比价,false为正常 */
    comparePriceState?: boolean
    /** 优惠券金额 */
    couponAmount?: string
    /** 优惠券信息-优惠券结束时间 yyyy-MM-dd HH:mm:ss */
    couponEndTime?: string
    /** 优惠券信息-优惠券开始时间 yyyy-MM-dd HH:mm:ss */
    couponStartTime?: string
    /** 优惠券地址 */
    couponUrl?: string
    /** 商品描述/推荐理由 */
    description?: string
    /** 折扣 */
    discount?: string
    /** 商品类型-淘宝：0-淘宝，1-天猫，京东：0-非自营，1-自营 */
    goodsType?: number
    /** 商品地址 */
    goodsUrl?: string
    /** 用户等级 */
    grade?: number
    /** 商品ID */
    id?: string
    /** 商品小图列表 */
    imageUrls?: string[]
    /** 最大预估收益 */
    maxProfit?: string
    /** 是否需要备案授权 */
    needAuth?: boolean
    /** 初始价格（电商平台的销售金额） */
    orgPrice?: string
    /** 商品主图 */
    picUrl?: string
    /** 平台编号 */
    platformCode?: string
    /** 预估收益 */
    profit?: string
    /** 佣金比例 */
    ratio?: number
    /** 销售数量 */
    salesCount?: string
    /** 购买省 */
    savedAmount?: string
    /** 店铺名称 */
    shopTitle?: string
    /** 商品副标题 */
    subTitle?: string
    /** 商品所属细分类目id */
    subdivisionId?: number
    /** 商品所属细分类目名称 */
    subdivisionName?: string
    /** 商品所属细分类目排名 */
    subdivisionRank?: number
    /** 商品标题 */
    title?: string
    /** 口令 */
    tkl?: string
    /** 微信小程序用户名 */
    wxAppUserName?: string
  }

  interface ResponseData {
    /** 授权URL */
    authUrl?: string
    /** 转链完成后的内容：如果该字段为空则标识转链失败 */
    content?: string
    /** 转链失败条数 */
    failureCount?: number
    /** 转链成功的商品列表 */
    goodsListVoList?: ResponseDataGoodsListVoListItem[]
    /** 是否需要授权：true标识且平台编号不为空则需要授权 */
    needAuth?: boolean
    /** 转链平台编号 */
    platformCode?: string
    /** 转链成功条数 */
    successCount?: number
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
