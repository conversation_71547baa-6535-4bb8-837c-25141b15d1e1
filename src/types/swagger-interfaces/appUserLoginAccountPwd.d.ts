/**
 * @name     手机号+密码登录 (省赚客)
 * @base     /
 * @path     /app/user/login/accountPwd
 * @method   POST
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/22 20:32:06
 */

declare namespace appUserLoginAccountPwd {
  interface Params {
    /** 手机号 */
    account: string
    /** 密码 */
    pwd: string
  }

  interface ResponseData {
    /** 账号/电话号码 */
    account?: string
    /** 提现支付宝账号 */
    aliPayAccount?: string
    /** 账户余额 */
    balance?: number
    /** 用户图像 */
    fansImg?: string
    /** 用户昵称 */
    fansNick?: string
    /** 普通邀请码 */
    inviteCode?: string
    /** 提现支付宝真实姓名 */
    realName?: string
    /** 注册时间 */
    regDateTime?: string
    /** 淘宝渠道ID，不为空代表已授权，为空代表未授权 */
    relationId?: string
    /** 上级用户ID */
    superiorTkUserId?: number
    /** 用户等级，1初级达人，2钻石达人，3超级达人 */
    tkUserLevel?: number
    /** token */
    token?: string
    /** 用户状态，1-正常、2-禁用 */
    userEnable?: number
    /** VIP邀请码(普通邀请码和vip邀请码都可以邀请，vip邀请码有值时展示vip邀请码) */
    vipInviteCode?: string
    /** 微信号 */
    wechatId?: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
