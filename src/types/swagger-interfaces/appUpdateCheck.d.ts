/**
 * @name     检查新版本(仅用于安卓) (省赚客)
 * @base     /
 * @path     /app/update/check
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/22 20:31:57
 */

declare namespace appUpdateCheck {
  interface Params {
    /** app当前版本号 */
    current_version: string
    /** 当前设备号 */
    device_id: string
    /** 包名 */
    package_name: string
    /** 手机操作系统类型 */
    system_type: 'android'
  }

  interface ResponseData {
    /** 最新版本号 */
    latest_version?: string
    /** 是否强制更新 */
    mandatory?: boolean
    /** 提示信息，当没有更新时返回说明信息 */
    message?: string
    /** 是否有新版本 */
    update_available?: boolean
    /** 更新内容的描述 */
    update_description?: string
    /** 新版本APK文件的下载地址 */
    update_url?: string
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
