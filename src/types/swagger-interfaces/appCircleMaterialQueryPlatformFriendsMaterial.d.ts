/**
 * @name     分享发圈素材接口 (省赚客)
 * @base     /
 * @path     /app/circleMaterial/queryPlatformFriendsMaterial
 * @method   GET
 * @savePath src/types/swagger-interfaces
 * @update   2024/6/22 20:32:00
 */

declare namespace appCircleMaterialQueryPlatformFriendsMaterial {
  interface Params {
    /** 分页条数 */
    limit: number
    /** 当前页数 */
    pageNo: number
    /** 平台类型 */
    platformCode: string
  }

  interface ResponseDataListItem {
    /** 券后价 */
    actualPrice?: string
    /** 预估到手（去除券和返利） */
    actualPriceEnd?: string
    /** 朋友圈评论内容（表情未处理），多条评论用“|”做区分 */
    comment?: string
    /** 单品导购内容（表情未处理） */
    content?: string
    /** 优惠券金额 */
    couponAmount?: number
    /** 优惠券结束时间 */
    couponEndTime?: string
    /** 优惠券开始时间 */
    couponStartTime?: string
    /** 优惠券链接 */
    couponUrl?: string
    /** 推荐描述 */
    description?: string
    /** 折扣 */
    discount?: string
    /** 预估收益（分享赚） */
    estimateProfit?: string
    /** 商品主标题 */
    goodsItemTitle?: string
    /** 商品id */
    id?: string
    /** 商品小图列表 */
    imageUrls?: string[]
    /** 多张宝贝图片，含实拍图 */
    itemPic?: string[]
    /** 月销量 */
    itemSale?: string
    /** 发圈素材中的宝贝标题 */
    itemTitle?: string
    /** 最大预估收益 */
    maxProfit?: string
    /** 作为请求地址中获取下一页的参数值 */
    minId?: number
    /** 原价 */
    orgPrice?: string
    /** 商品主图 */
    picUrl?: string
    /** 平台编号 */
    platformCode?: string
    /** 购买省 */
    savedAmount?: string
    /** 店铺名称 */
    shopTitle?: string
    /** 店铺种类 天猫B，淘宝店C */
    shopType?: string
    /** 展示时间 */
    showTime?: string
    /** 单张图片 */
    soLaImage?: string
    /** 发圈素材中的宝贝标题 */
    title?: string
    /** 佣金比例 */
    tkRates?: string
  }

  interface ResponseData {
    endRow?: number
    hasNextPage?: boolean
    hasPreviousPage?: boolean
    isFirstPage?: boolean
    isLastPage?: boolean
    list?: ResponseDataListItem[]
    navigateFirstPage?: number
    navigateLastPage?: number
    navigatePages?: number
    navigatepageNums?: number[]
    nextPage?: number
    pageNum?: number
    pageSize?: number
    pages?: number
    prePage?: number
    size?: number
    startRow?: number
    total?: number
  }

  interface Response {
    /** 返回状态码 */
    code?: number
    /** 返回内容 */
    data?: ResponseData
    /** 返回消息 */
    msg?: string
  }
}
