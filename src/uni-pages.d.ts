/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/home/<USER>" |
       "/pages/iframe" |
       "/pages/category/index" |
       "/pages/channel/index" |
       "/pages/circle/circle-list" |
       "/pages/circle/index" |
       "/pages/home/<USER>" |
       "/pages/life/index" |
       "/pages/life/search" |
       "/pages/login/index" |
       "/pages/personal/index" |
       "/pages/personal/material" |
       "/pages/rank/index" |
       "/pages/search/index" |
       "/pages/search/turnChain" |
       "/pages/setting/index" |
       "/pages/shop/index" |
       "/pages/uzk/index" |
       "/pages/goods/detail/index" |
       "/pages/personal/components/common-functions" |
       "/pages/personal/components/earnings-info" |
       "/pages/personal/components/free-shipping" |
       "/pages/personal/components/money-info" |
       "/pages/personal/components/swiper-info" |
       "/pages/personal/components/user-profile" |
       "/pages/rank/components/rank-activity" |
       "/pages/rank/components/rank-content" |
       "/pages/rank/components/rank-header" |
       "/pages/shop/order/index" |
       "/pages/personal/commonFunctions/frequentlyAskedQuestions/index" |
       "/pages/personal/commonFunctions/suggestionFeedback/index" |
       "/pages/personal/commonFunctions/upgrades/index" |
       "/pages/personal/earningsInfo/fans/index" |
       "/pages/personal/earningsInfo/favorites/index" |
       "/pages/personal/earningsInfo/order/index" |
       "/pages/personal/earningsInfo/share/h2canvas" |
       "/pages/personal/earningsInfo/share/index" |
       "/pages/personal/userProfile/bindAlipay/index" |
       "/pages/personal/userProfile/withdraw/index" |
       "/pages/personal/userProfile/withdrawalRecord/index" |
       "/pages/search/components/hotList/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/home/<USER>" | "/pages/rank/index" | "/pages/life/index" | "/pages/circle/index" | "/pages/personal/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
