.table_box {
	background-color: #FFFFFF;
	.table_title {
		padding: 0rpx 20rpx;
		font-size: 30rpx;
		height: 60rpx;
		line-height: 60rpx;
		color: #FFF;
		text-align: center;
		font-weight: bold;
		background-color: rgba($color: $themeColor, $alpha: 1);
	}
	.table_head {
		display: flex;
		align-content: center;
		background-color: rgba($color: $themeColor, $alpha: 0.35);
		text {
			padding: 15rpx 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1;
			flex-shrink: 0;
			font-size: 28rpx;
			color: #333;
			font-weight: bold;
			border-right: 2rpx solid rgba($color: $themeColor, $alpha: 0.2);
			&:last-child {
				border-right: 0;
				flex: 2;
			}
		} 
	}
	.table_content {
		display: flex;
		align-content: center;
		border-top: 2rpx solid rgba($color: $themeColor, $alpha: 0.2);
		background-color: rgba($color: $themeColor, $alpha: 0.2);
		text {
			padding: 15rpx 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1;
			font-size: 28rpx;
			color: #999;
			border-right: 2rpx solid rgba($color: $themeColor, $alpha: 0.3);
			&:last-child {
				border-right: 0;
				flex: 2;
			}
		} 
	}
}