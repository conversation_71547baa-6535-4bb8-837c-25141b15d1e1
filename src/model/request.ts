import type { UnConfig, UnHeaders, UnProfile, UnTask } from '@uni-helper/uni-network'

type UnData = string | Record<string, any> | ArrayBuffer

export interface Response {
  /** 错误信息 */
  errMsg?: string
  /** 错误代码 */
  errno?: number
  /** 网络请求过程中的调试信息 */
  profile?: UnProfile
  /** 请求的配置信息 */
  config?: UnConfig<UnData, UnData>
  /** 对应的 task 信息 */
  task?: UnTask
  /** 服务器响应的 HTTP 状态码 */
  status?: number
  /** 服务器响应的 HTTP 状态信息 */
  statusText?: string
  /** 服务器响应头 */
  headers?: UnHeaders
  /** 服务器响应数据 */
  data: any
  /**
   * Request 特有
   *
   * 服务器提供的 cookies 数据
   */
  cookies?: string[]
  /**
   * Download 特有
   *
   * 临时本地文件路径
   *
   * 没传入 filePath 指定文件存储路径时会返回，下载后的文件会存储到一个临时文件
   */
  tempFilePath?: string
  /**
   * Download 特有
   *
   * 用户本地文件路径
   *
   * 传入 filePath 时会返回，跟传入的 filePath 一致
   */
  filePath?: string
}

/**
 * 请求接口白名单
 * @docs https://szk.juwatech.cn/doc.html#/%E7%9C%81%E8%B5%9A%E5%AE%A2App%E5%BC%80%E5%8F%91%E7%BB%84/%E7%94%A8%E6%88%B7%E6%A8%A1%E5%9D%97/loginAccountPwdUsingPOST
 */
export const whiteList: Set<string> = new Set(['app/user/login/accountPwd', '/app/user/login/getSmsCode'])
/**
 * 设置请求头
 * @param config
 * @param token
 * @param isWhiteList
 * @returns config
 */
export function setHeaders(config: UnConfig<UnData, UnData>, token: string, isWhiteList: boolean) {
  // 保持原有headers的同时，添加或修改必要的字段
  config.headers = {
    ...config.headers,
    token: token || '', // 使用token || '' 来确保token有值，避免undefined
    whiteList: isWhiteList,
    url: config.url, // 确保url字段存在
  }
  return config
}
