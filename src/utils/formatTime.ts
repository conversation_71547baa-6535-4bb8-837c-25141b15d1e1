/**
 * 格式化时间为多久前
 * @param stringTime 时间字符串
 * @returns 格式化后的时间
 */
export function friendlyFormatTime(stringTime: string): string {
  // 容错处理：检查输入是否为有效的日期字符串
  if (!stringTime || isNaN(Date.parse(stringTime))) {
    return '' // 返回“无效时间”以指示输入错误
  }

  // 将字符串转换成时间格式
  const timePublish = new Date(stringTime) // 发布的时间
  const timeNow = new Date() // 当前时间

  // 定义时间单位
  const minute = 1000 * 60 // 一分钟的毫秒数
  const hour = minute * 60 // 一小时的毫秒数
  const day = hour * 24 // 一天的毫秒数
  const month = day * 30 // 一个月的毫秒数（按30天计算）
  const year = month * 12 // 一年的毫秒数

  // 计算时间差值
  const diffValue = timeNow.getTime() - timePublish.getTime() // 当前时间与发布时间的差值
  const diffYear = diffValue / year // 差值转换为年
  const diffMonth = diffValue / month // 差值转换为月
  const diffWeek = diffValue / (7 * day) // 差值转换为周
  const diffDay = diffValue / day // 差值转换为天
  const diffHour = diffValue / hour // 差值转换为小时
  const diffMinute = diffValue / minute // 差值转换为分钟

  // 根据时间差值返回相应的时间描述
  if (diffValue < 0) {
    return '刚刚' // 如果时间差值为负，返回“刚刚”
  }
  else if (diffYear >= 1) {
    return `${Math.floor(diffYear)}年前` // 如果差值大于等于一年，返回“X年前”
  }
  else if (diffMonth >= 1) {
    return `${Math.floor(diffMonth)}月前` // 如果差值大于等于一个月，返回“X月前”
  }
  else if (diffWeek >= 1) {
    return `${Math.floor(diffWeek)}周前` // 如果差值大于等于一周，返回“X周前”
  }
  else if (diffDay >= 1) {
    return `${Math.floor(diffDay)}天前` // 如果差值大于等于一天，返回“X天前”
  }
  else if (diffHour >= 1) {
    return `${Math.floor(diffHour)}小时前` // 如果差值大于等于一小时，返回“X小时前”
  }
  else if (diffMinute >= 1) {
    return `${Math.floor(diffMinute)}分钟前` // 如果差值大于等于一分钟，返回“X分钟前”
  }
  else {
    return '刚刚' // 如果差值小于一分钟，返回“刚刚”
  }
}
