/**
 * 防抖函数，在指定的延迟时间内只执行一次函数。
 *
 * @param func 要防抖的函数。
 * @param delay 延迟时间，单位为毫秒。
 * @returns 返回防抖后的函数。
 */
// debounce.ts
export async function debounce<T extends (...args: any[]) => any>(func: T, delay: number): Promise<(...args: Parameters<T>) => void> {
  let timeoutId: NodeJS.Timeout | null = null

  return function debounced(...args: Parameters<T>) {
    if (timeoutId)
      clearTimeout(timeoutId)

    timeoutId = setTimeout(() => {
      func(...args)
    }, delay)
  }
}
