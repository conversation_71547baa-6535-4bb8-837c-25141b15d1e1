/**
 * 节流函数，限制函数执行频率
 *
 * @param func 需要进行节流的函数
 * @param limit 函数执行间隔（毫秒）
 * @returns 返回一个经过节流处理的新函数
 */
export function throttle<T extends (...args: any[]) => any>(func: T, interval: number): (...args: Parameters<T>) => void {
  let lastExecTime = 0

  return function throttledFunction(...args: Parameters<T>) {
    const now = Date.now()

    if (now - lastExecTime >= interval) {
      func(...args)
      lastExecTime = now
    }
  }
}
