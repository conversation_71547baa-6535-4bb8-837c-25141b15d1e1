import { timestamp } from '@vueuse/core'
import { un } from '@uni-helper/uni-network'
import { setHeaders, whiteList } from '@/model/request'

import { appInfo } from '@/common'
import { useAuthStore } from '@/store/user'

const { userToken } = useAuthStore()

/**
 * 检查值是否为空
 * @param value 要检查的值
 * @returns 如果值为null、undefined、空字符串、空数组或空对象则返回true，否则返回false
 */
function isEmpty(value: any): boolean {
  if (value === null || value === undefined) {
    return true
  }

  if (typeof value === 'string' && value.trim() === '') {
    return true
  }

  if (Array.isArray(value) && value.length === 0) {
    return true
  }

  if (typeof value === 'object' && Object.keys(value).length === 0) {
    return true
  }

  return false
}

function getToken(): string {
  try {
    return uni.getStorageSync('userToken') || userToken.value
  }
  catch (error) {
    console.error('Failed to get userToken from storage:', error)
    return ''
  }
}

// 配置对象
const queryConfig = {
  baseUrl: appInfo.serverApi,
  timeout: 60 * 1000,
  headers: {
    author: appInfo.author,
    company: appInfo.company,
    version: appInfo.version,
    timestamp: timestamp(),
  },
}

export const request = un.create(queryConfig)

// 请求拦截器
request.interceptors.request.use(
  async (config) => {
    const { url = '' } = config
    const isWhiteListed = whiteList.has(url)
    const token = getToken()

    setHeaders(config, isWhiteListed ? '' : token, isWhiteListed)

    return config
  },
  (error) => {
    console.error(`请求错误: ${error.message} - 请求URL: ${error.config.url}`)
    uni.hideLoading()
    return Promise.reject(error)
  },
)

// 响应拦截器
request.interceptors.response.use(
  (response: any) => {
    console.log('[gloabl] response', response)
    // 使用自定义isEmpty方法判断data是否为空
    const hasData = 'data' in response.data && !isEmpty(response.data.data)
    const responseData = hasData
      ? { ...response, data: response.data.data }
      : { ...response, ...response.data }
    // unauthorized
    if (response.data.code === 4001) {
      console.log('[need login api]', `[${response.config.url}]`)
      uni.navigateTo({
        url: '/pages/login/index',
      })
    }
    // 统一处理接口异常情况
    else if (response.data.code !== 1001) {
      uni.hideLoading()
      uni.showToast({
        icon: 'none',
        title: responseData.msg,
      })
    }
    return Promise.resolve(responseData)
  },
  (error) => {
    console.log('[gloabl] error', error)
    uni.hideLoading()
    return Promise.reject(error)
  },
)
