// #ifdef H5
/** clipboard.js v2.0.4 */
!(function (t, e) {
  try {
    window.ClipboardJS = e()
  }
  catch (e) {
  }
  ;typeof exports == 'object' && typeof module == 'object' ? module.exports = e() : typeof define == 'function' && define.amd ? define([], e) : typeof exports == 'object' ? exports.ClipboardJS = e() : t.ClipboardJS = e()
}(this, () => {
  return (function (n) {
    const o = {}

    function r(t) {
      if (o[t])
        return o[t].exports
      const e = o[t] = { i: t, l: !1, exports: {} }
      return n[t].call(e.exports, e, e.exports, r), e.l = !0, e.exports
    }

    return r.m = n, r.c = o, r.d = function (t, e, n) {
      r.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: n })
    }, r.r = function (t) {
      typeof Symbol != 'undefined' && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, { value: 'Module' }), Object.defineProperty(t, '__esModule', { value: !0 })
    }, r.t = function (e, t) {
      if (1 & t && (e = r(e)), 8 & t)
        return e
      if (4 & t && typeof e == 'object' && e && e.__esModule)
        return e
      const n = Object.create(null)
      if (r.r(n), Object.defineProperty(n, 'default', {
        enumerable: !0,
        value: e,
      }), 2 & t && typeof e != 'string') {
        for (const o in e) {
          r.d(n, o, ((t) => {
            return e[t]
          }).bind(null, o))
        }
      }
      return n
    }, r.n = function (t) {
      const e = t && t.__esModule
        ? function () {
          return t.default
        }
        : function () {
          return t
        }
      return r.d(e, 'a', e), e
    }, r.o = function (t, e) {
      return Object.prototype.hasOwnProperty.call(t, e)
    }, r.p = '', r(r.s = 0)
  }([function (t, e, n) {
    'use strict'
    const r = typeof Symbol == 'function' && typeof Symbol.iterator == 'symbol'
      ? function (t) {
        return typeof t
      }
      : function (t) {
        return t && typeof Symbol == 'function' && t.constructor === Symbol && t !== Symbol.prototype ? 'symbol' : typeof t
      }; const i = (function () {
      function o(t, e) {
        for (let n = 0; n < e.length; n++) {
          const o = e[n]
          o.enumerable = o.enumerable || !1, o.configurable = !0, 'value' in o && (o.writable = !0), Object.defineProperty(t, o.key, o)
        }
      }

      return function (t, e, n) {
        return e && o(t.prototype, e), n && o(t, n), t
      }
    }()); const a = o(n(1)); const c = o(n(3)); const u = o(n(4))

    function o(t) {
      return t && t.__esModule ? t : { default: t }
    }

    const l = (function (t) {
      function o(t, e) {
        !(function (t, e) {
          if (!(t instanceof e))
            throw new TypeError('Cannot call a class as a function')
        }(this, o))
        const n = (function (t, e) {
          if (!t)
            throw new ReferenceError('this hasn\'t been initialised - super() hasn\'t been called')
          return !e || typeof e != 'object' && typeof e != 'function' ? t : e
        }(this, (o.__proto__ || Object.getPrototypeOf(o)).call(this)))
        return n.resolveOptions(e), n.listenClick(t), n
      }

      return (function (t, e) {
        if (typeof e != 'function' && e !== null)
          throw new TypeError(`Super expression must either be null or a function, not ${typeof e}`)
        t.prototype = Object.create(e && e.prototype, {
          constructor: {
            value: t,
            enumerable: !1,
            writable: !0,
            configurable: !0,
          },
        }), e && (Object.setPrototypeOf ? Object.setPrototypeOf(t, e) : t.__proto__ = e)
      }(o, c.default)), i(o, [{
        key: 'resolveOptions',
        value() {
          const t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}
          this.action = typeof t.action == 'function' ? t.action : this.defaultAction, this.target = typeof t.target == 'function' ? t.target : this.defaultTarget, this.text = typeof t.text == 'function' ? t.text : this.defaultText, this.container = r(t.container) === 'object' ? t.container : document.body
        },
      }, {
        key: 'listenClick',
        value(t) {
          const e = this
          this.listener = (0, u.default)(t, 'click', (t) => {
            return e.onClick(t)
          })
        },
      }, {
        key: 'onClick',
        value(t) {
          const e = t.delegateTarget || t.currentTarget
          this.clipboardAction && (this.clipboardAction = null), this.clipboardAction = new a.default({
            action: this.action(e),
            target: this.target(e),
            text: this.text(e),
            container: this.container,
            trigger: e,
            emitter: this,
          })
        },
      }, {
        key: 'defaultAction',
        value(t) {
          return s('action', t) || 'copy'
        },
      }, {
        key: 'defaultTarget',
        value(t) {
          const e = s('target', t)
          if (e)
            return document.querySelector(e)
        },
      }, {
        key: 'defaultText',
        value(t) {
          return s('text', t) || this.text
        },
      }, {
        key: 'destroy',
        value() {
          this.listener.destroy(), this.clipboardAction && (this.clipboardAction.destroy(), this.clipboardAction = null)
        },
      }], [{
        key: 'isSupported',
        value() {
          const t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : ['copy', 'cut']
          const e = typeof t == 'string' ? [t] : t; let n = !!document.queryCommandSupported
          return e.forEach((t) => {
            n = n && !!document.queryCommandSupported(t)
          }), n
        },
      }]), o
    }())

    function s(t, e) {
      const n = `data-clipboard-${t}`
      const isFun = e && typeof e.hasAttribute === 'function'
      if (isFun && e.hasAttribute(n))
        return e.getAttribute(n)
    }

    t.exports = l
  }, function (t, e, n) {
    'use strict'
    let o; const r = typeof Symbol == 'function' && typeof Symbol.iterator == 'symbol'
      ? function (t) {
        return typeof t
      }
      : function (t) {
        return t && typeof Symbol == 'function' && t.constructor === Symbol && t !== Symbol.prototype ? 'symbol' : typeof t
      }; const i = (function () {
      function o(t, e) {
        for (let n = 0; n < e.length; n++) {
          const o = e[n]
          o.enumerable = o.enumerable || !1, o.configurable = !0, 'value' in o && (o.writable = !0), Object.defineProperty(t, o.key, o)
        }
      }

      return function (t, e, n) {
        return e && o(t.prototype, e), n && o(t, n), t
      }
    }()); const a = n(2); const c = (o = a) && o.__esModule ? o : { default: o }
    const u = (function () {
      function e(t) {
        !(function (t, e) {
          if (!(t instanceof e))
            throw new TypeError('Cannot call a class as a function')
        }(this, e)), this.resolveOptions(t), this.initSelection()
      }

      return i(e, [{
        key: 'resolveOptions',
        value() {
          const t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}
          this.action = t.action, this.container = t.container, this.emitter = t.emitter, this.target = t.target, this.text = t.text, this.trigger = t.trigger, this.selectedText = ''
        },
      }, {
        key: 'initSelection',
        value() {
          this.text ? this.selectFake() : this.target && this.selectTarget()
        },
      }, {
        key: 'selectFake',
        value() {
          const t = this; const e = document.documentElement.getAttribute('dir') == 'rtl'
          this.removeFake(), this.fakeHandlerCallback = function () {
            return t.removeFake()
          }, this.fakeHandler = this.container.addEventListener('click', this.fakeHandlerCallback) || !0, this.fakeElem = document.createElement('textarea'), this.fakeElem.style.fontSize = '12pt', this.fakeElem.style.border = '0', this.fakeElem.style.padding = '0', this.fakeElem.style.margin = '0', this.fakeElem.style.position = 'absolute', this.fakeElem.style[e ? 'right' : 'left'] = '-9999px'
          const n = window.pageYOffset || document.documentElement.scrollTop
          this.fakeElem.style.top = `${n}px`, this.fakeElem.setAttribute('readonly', ''), this.fakeElem.value = this.text, this.container.appendChild(this.fakeElem), this.selectedText = (0, c.default)(this.fakeElem), this.copyText()
        },
      }, {
        key: 'removeFake',
        value() {
          this.fakeHandler && (this.container.removeEventListener('click', this.fakeHandlerCallback), this.fakeHandler = null, this.fakeHandlerCallback = null), this.fakeElem && (this.container.removeChild(this.fakeElem), this.fakeElem = null)
        },
      }, {
        key: 'selectTarget',
        value() {
          this.selectedText = (0, c.default)(this.target), this.copyText()
        },
      }, {
        key: 'copyText',
        value() {
          let e = void 0
          try {
            e = document.execCommand(this.action)
          }
          catch (t) {
            e = !1
          }
          this.handleResult(e)
        },
      }, {
        key: 'handleResult',
        value(t) {
          this.emitter.emit(t ? 'success' : 'error', {
            action: this.action,
            text: this.selectedText,
            trigger: this.trigger,
            clearSelection: this.clearSelection.bind(this),
          })
        },
      }, {
        key: 'clearSelection',
        value() {
          this.trigger && this.trigger.focus(), window.getSelection().removeAllRanges()
        },
      }, {
        key: 'destroy',
        value() {
          this.removeFake()
        },
      }, {
        key: 'action',
        set() {
          const t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 'copy'
          if (this._action = t, this._action !== 'copy' && this._action !== 'cut')
            throw new Error('Invalid "action" value, use either "copy" or "cut"')
        },
        get() {
          return this._action
        },
      }, {
        key: 'target',
        set(t) {
          if (void 0 !== t) {
            if (!t || (void 0 === t ? 'undefined' : r(t)) !== 'object' || t.nodeType !== 1)
              throw new Error('Invalid "target" value, use a valid Element')
            if (this.action === 'copy' && t.hasAttribute('disabled'))
              throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute')
            if (this.action === 'cut' && (t.hasAttribute('readonly') || t.hasAttribute('disabled')))
              throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')
            this._target = t
          }
        },
        get() {
          return this._target
        },
      }]), e
    }())
    t.exports = u
  }, function (t, e) {
    t.exports = function (t) {
      let e
      if (t.nodeName === 'SELECT') { t.focus(), e = t.value }
      else if (t.nodeName === 'INPUT' || t.nodeName === 'TEXTAREA') {
        const n = t.hasAttribute('readonly')
        n || t.setAttribute('readonly', ''), t.select(), t.setSelectionRange(0, t.value.length), n || t.removeAttribute('readonly'), e = t.value
      }
      else {
        t.hasAttribute('contenteditable') && t.focus()
        const o = window.getSelection(); const r = document.createRange()
        r.selectNodeContents(t), o.removeAllRanges(), o.addRange(r), e = o.toString()
      }
      return e
    }
  }, function (t, e) {
    function n() {
    }

    n.prototype = {
      on(t, e, n) {
        const o = this.e || (this.e = {})
        return (o[t] || (o[t] = [])).push({ fn: e, ctx: n }), this
      },
      once(t, e, n) {
        const o = this

        function r() {
          o.off(t, r), e.apply(n, arguments)
        }

        return r._ = e, this.on(t, r, n)
      },
      emit(t) {
        for (let e = [].slice.call(arguments, 1), n = ((this.e || (this.e = {}))[t] || []).slice(), o = 0, r = n.length; o < r; o++) n[o].fn.apply(n[o].ctx, e)
        return this
      },
      off(t, e) {
        const n = this.e || (this.e = {}); const o = n[t]; const r = []
        if (o && e)
          for (let i = 0, a = o.length; i < a; i++) o[i].fn !== e && o[i].fn._ !== e && r.push(o[i])
        return r.length ? n[t] = r : delete n[t], this
      },
    }, t.exports = n
  }, function (t, e, n) {
    const d = n(5); const h = n(6)
    t.exports = function (t, e, n) {
      if (!t && !e && !n)
        throw new Error('Missing required arguments')
      if (!d.string(e))
        throw new TypeError('Second argument must be a String')
      if (!d.fn(n))
        throw new TypeError('Third argument must be a Function')
      if (d.node(t)) {
        return s = e, f = n, (l = t).addEventListener(s, f), {
          destroy() {
            l.removeEventListener(s, f)
          },
        }
      }
      if (d.nodeList(t)) {
        return a = t, c = e, u = n, Array.prototype.forEach.call(a, (t) => {
          t.addEventListener(c, u)
        }), {
          destroy() {
            Array.prototype.forEach.call(a, (t) => {
              t.removeEventListener(c, u)
            })
          },
        }
      }
      if (d.string(t))
        return o = t, r = e, i = n, h(document.body, o, r, i)
      throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList')
      let o, r, i, a, c, u, l, s, f
    }
  }, function (t, n) {
    n.node = function (t) {
      return void 0 !== t && t instanceof HTMLElement && t.nodeType === 1
    }, n.nodeList = function (t) {
      const e = Object.prototype.toString.call(t)
      return void 0 !== t && (e === '[object NodeList]' || e === '[object HTMLCollection]') && 'length' in t && (t.length === 0 || n.node(t[0]))
    }, n.string = function (t) {
      return typeof t == 'string' || t instanceof String
    }, n.fn = function (t) {
      return Object.prototype.toString.call(t) === '[object Function]'
    }
  }, function (t, e, n) {
    const a = n(7)

    function i(t, e, n, o, r) {
      const i = (function (e, n, t, o) {
        return function (t) {
          t.delegateTarget = a(t.target, n), t.delegateTarget && o.call(e, t)
        }
      }.apply(this, arguments))
      return t.addEventListener(n, i, r), {
        destroy() {
          t.removeEventListener(n, i, r)
        },
      }
    }

    t.exports = function (t, e, n, o, r) {
      return typeof t.addEventListener == 'function'
        ? i.apply(null, arguments)
        : typeof n == 'function'
          ? i.bind(null, document).apply(null, arguments)
          : (typeof t == 'string' && (t = document.querySelectorAll(t)), Array.prototype.map.call(t, (t) => {
              return i(t, e, n, o, r)
            }))
    }
  }, function (t, e) {
    if (typeof Element != 'undefined' && !Element.prototype.matches) {
      const n = Element.prototype
      n.matches = n.matchesSelector || n.mozMatchesSelector || n.msMatchesSelector || n.oMatchesSelector || n.webkitMatchesSelector
    }
    t.exports = function (t, e) {
      for (; t && t.nodeType !== 9;) {
        if (typeof t.matches == 'function' && t.matches(e))
          return t
        t = t.parentNode
      }
    }
  }]))
}))
const Types = {
  isFunction(obj) {
    const type = Object.prototype.toString.call(obj)
    return type == '[object Function]'
  },
  isObject(obj) {
    const type = Object.prototype.toString.call(obj)
    return type == '[object Object]'
  },
  isString(obj) {
    const type = Object.prototype.toString.call(obj)
    return type == '[object String]'
  },
}
uni.setClipboardData = function (options) {
  const emptyFun = function () {
  }
  let config = { data: null, event: null, success: emptyFun, fail: emptyFun, complete: emptyFun }
  if (options && Types.isObject(options))
    config = Object.assign({}, config, options)

  if (options && Types.isString(options))
    config = Object.assign({}, config, { data: options })

  const data = config.data
  const success = config.success || emptyFun
  const fail = config.fail || emptyFun
  const complete = config.complete || emptyFun
  const e = config.event || window.event || {}
  const cb = new ClipboardJS('.null', { text: () => data })
  cb.on('success', (res) => {
    window.__clipboard__ = data
    success && Types.isFunction(success) && success({ data: res.text })
    complete && Types.isFunction(complete) && complete()
    cb.off('error')
    cb.off('success')
    cb.destroy()
  })
  cb.on('error', (err) => {
    fail && Types.isFunction(fail) && fail(err)
    complete && Types.isFunction(complete) && complete()
    cb.off('error')
    cb.off('success')
    cb.destroy()
  })
  cb.onClick(e)
}
uni.getClipboardData = function (options) {
  const emptyFun = function () {
  }
  let config = { data: null, event: null, success: emptyFun, fail: emptyFun, complete: emptyFun }
  if (options && Types.isObject(options))
    config = Object.assign({}, config, options)

  const success = config.success || emptyFun
  const fail = config.fail || emptyFun
  const complete = config.complete || emptyFun
  if (window.__clipboard__ !== undefined)
    success && Types.isFunction(success) && success({ data: window.__clipboard__ })
  else
    fail && Types.isFunction(fail) && fail({ data: null })

  complete && Types.isFunction(complete) && complete()
}

function fileDownLoad(data) {
  const linkElement = document.createElement('a')
  linkElement.setAttribute('href', data.blob)
  linkElement.setAttribute('downLoad', data.name)
  linkElement.click()
}

uni.saveImageToPhotosAlbum = uni.saveVideoToPhotosAlbum = function (options) {
  const emptyFun = function () {
  }
  let config = { filePath: null, success: emptyFun, fail: emptyFun, complete: emptyFun }
  if (options && Types.isObject(options))
    config = Object.assign({}, config, options)

  if (options && Types.isString(options))
    config = Object.assign({}, config, { filePath: options })

  const filePath = config.filePath
  const success = config.success || emptyFun
  const fail = config.fail || emptyFun
  const complete = config.complete || emptyFun
  if (!filePath) {
    fail && Types.isFunction(fail) && fail({ msg: 'no File' })
    complete && Types.isFunction(complete) && complete()
    return
  }
  const names = filePath.split('/')
  const name = names[names.length - 1]
  uni.downloadFile({
    url: filePath,
    success(res) {
      const tempFilePath = res.tempFilePath
      fileDownLoad({ name, blob: tempFilePath })
      success && Types.isFunction(success) && success({ filePath })
    },
    fail(err) {
      fail && Types.isFunction(fail) && fail({ msg: err })
    },
    complete() {
      complete && Types.isFunction(complete) && complete()
    },
  })
}
// #endif
