// #ifdef H5
!(function (e, n) {
  typeof exports == 'object' && typeof module == 'object'
    ? module.exports = n(e)
    : typeof define == 'function' && (define.amd || define.cmd)
      ? define(() => {
        return n(e)
      })
      : n(e, !0)
}(window, (o, e) => {
  function c(n, e, i) {
    o.WeixinJSBridge
      ? WeixinJSBridge.invoke(n, r(e), (e) => {
        a(n, e, i)
      })
      : d(n, i)
  }

  function i(n, i, t) {
    o.<PERSON>xin<PERSON>SBridge
      ? WeixinJSBridge.on(n, (e) => {
        t && t.trigger && t.trigger(e), a(n, e, i)
      })
      : d(n, t || i)
  }

  function r(e) {
    return (e = e || {}).appId = M.appId, e.verifyAppId = M.appId, e.verifySignType = 'sha1', e.verifyTimestamp = `${M.timestamp}`, e.verifyNonceStr = M.nonceStr, e.verifySignature = M.signature, e
  }

  function n(e) {
    return {
      timeStamp: `${e.timestamp}`,
      nonceStr: e.nonceStr,
      package: e.package,
      paySign: e.paySign,
      signType: e.signType || 'SHA1',
    }
  }

  function a(e, n, i) {
    e == 'openEnterpriseChat' && (n.errCode = n.err_code), delete n.err_code, delete n.err_desc, delete n.err_detail
    let t = n.errMsg
    t || (t = n.err_msg, delete n.err_msg, t = (function (e, n) {
      let i = e; const t = f[i]
      t && (i = t)
      let o = 'ok'
      if (n) {
        const r = n.indexOf(':')
        (o = n.substring(r + 1)) == 'confirm' && (o = 'ok'), o == 'failed' && (o = 'fail'), o.includes('failed_') && (o = o.substring(7)), o.includes('fail_') && (o = o.substring(5)), (o = (o = o.replace(/_/g, ' ')).toLowerCase()) != 'access denied' && o != 'no permission to execute' || (o = 'permission denied'), i == 'config' && o == 'function not exist' && (o = 'ok'), o == '' && (o = 'fail')
      }
      return n = `${i}:${o}`
    }(e, t)), n.errMsg = t), (i = i || {})._complete && (i._complete(n), delete i._complete), t = n.errMsg || '', M.debug && !i.isInnerInvoke && alert(JSON.stringify(n))
    const o = t.indexOf(':')
    switch (t.substring(o + 1)) {
      case 'ok':
        i.success && i.success()
        break
      case 'cancel':
        i.cancel && i.cancel(n)
        break
      default:
        i.fail && i.fail(n)
    }
    i.complete && i.complete(n)
  }

  function s(e) {
    if (e) {
      for (let n = 0, i = e.length; n < i; ++n) {
        const t = e[n]; const o = p[t]
        o && (e[n] = o)
      }
      return e
    }
  }

  function d(e, n) {
    if (!(!M.debug || n && n.isInnerInvoke)) {
      const i = f[e]
      i && (e = i), n && n._complete && delete n._complete, console.log(`"${e}",`, n || '')
    }
  }

  function l() {
    return (new Date()).getTime()
  }

  function u(e) {
    I && (o.WeixinJSBridge ? e() : t.addEventListener && t.addEventListener('WeixinJSBridgeReady', e, !1))
  }

  if (!o.jWeixin) {
    var p = {
      config: 'preVerifyJSAPI',
      onMenuShareTimeline: 'menu:share:timeline',
      onMenuShareAppMessage: 'menu:share:appmessage',
      onMenuShareQQ: 'menu:share:qq',
      onMenuShareWeibo: 'menu:share:weiboApp',
      onMenuShareQZone: 'menu:share:QZone',
      previewImage: 'imagePreview',
      getLocation: 'geoLocation',
      openProductSpecificView: 'openProductViewWithPid',
      addCard: 'batchAddCard',
      openCard: 'batchViewCard',
      chooseWXPay: 'getBrandWCPayRequest',
      openEnterpriseRedPacket: 'getRecevieBizHongBaoRequest',
      startSearchBeacons: 'startMonitoringBeacons',
      stopSearchBeacons: 'stopMonitoringBeacons',
      onSearchBeacons: 'onBeaconsInRange',
      consumeAndShareCard: 'consumedShareCard',
      openAddress: 'editAddress',
    }; var f = (function () {
      const e = {}
      for (const n in p) e[p[n]] = n
      return e
    }()); var t = o.document; const m = t.title; const g = navigator.userAgent.toLowerCase(); const h = navigator.platform.toLowerCase()
    const v = !(!h.match('mac') && !h.match('win')); const S = g.includes('wxdebugger');
    var I = g.includes('micromessenger'); const y = g.includes('android');
    const _ = g.includes('iphone') || g.includes('ipad');
    const w = (O = g.match(/micromessenger\/(\d+\.\d+\.\d+)/) || g.match(/micromessenger\/(\d+\.\d+)/)) ? O[1] : ''
    const k = { initStartTime: l(), initEndTime: 0, preVerifyStartTime: 0, preVerifyEndTime: 0 }; const T = {
      version: 1,
      appId: '',
      initTime: 0,
      preVerifyTime: 0,
      networkType: '',
      isPreVerifyOk: 1,
      systemType: _ ? 1 : y ? 2 : -1,
      clientVersion: w,
      url: encodeURIComponent(location.href),
    }; var M = {}; const P = { _completes: [] }; const x = { state: 0, data: {} }
    u(() => {
      k.initEndTime = l()
    })
    let A = !1; const V = []; var C = {
      config(e) {
        d('config', M = e)
        let t = !1 !== M.check
        u(() => {
          if (t)
            {c(p.config, { verifyJsApiList: s(M.jsApiList) }, (function () {
              P._complete = function (e) {
                k.preVerifyEndTime = l(), x.state = 1, x.data = e
              }, P.success = function (e) {
                T.isPreVerifyOk = 0
              }, P.fail = function (e) {
                P._fail ? P._fail(e) : x.state = -1
              };
              var t = P._completes
                        return t.push(() => {
                !(function (e) {
                                if (!(v || S || M.debug || w < "6.0.2" || T.systemType < 0)) {
                                    var i = new Image;
                                    T.appId = M.appId, T.initTime = k.initEndTime - k.initStartTime, T.preVerifyTime = k.preVerifyEndTime - k.preVerifyStartTime, C.getNetworkType({
                                        isInnerInvoke: !0,
                                        success: function (e) {
                                            T.networkType = e.networkType;
                                            var n = "https://open.weixin.qq.com/sdk/report?v=" + T.version + "&o=" + T.isPreVerifyOk + "&s=" + T.systemType + "&c=" + T.clientVersion + "&a=" + T.appId + "&n=" + T.networkType + "&i=" + T.initTime + "&p=" + T.preVerifyTime + "&u=" + T.url;
                                            i.src = n
                                        }
                                    })
                                }
                            }())
              }), P.complete = function (e) {
                for (let n = 0, i = t.length; n < i; ++n) t[n]()
                            P._completes = []
              }, P
            }())), k.preVerifyStartTime = l();} else {
            x.state = 1
            for (let e = P._completes, n = 0, i = e.length; n < i; ++n) e[n]()
            P._completes = []
          }
        }), C.invoke || (C.invoke = function (e, n, i) {
          o.WeixinJSBridge && WeixinJSBridge.invoke(e, r(n), i)
        }, C.on = function (e, n) {
          o.WeixinJSBridge && WeixinJSBridge.on(e, n)
        })
      },
      ready(e) {
        x.state != 0 ? e() : (P._completes.push(e), !I && M.debug && e())
      },
      error(e) {
        w < '6.0.2' || (x.state == -1 ? e(x.data) : P._fail = e)
      },
      checkJsApi(e) {
        c('checkJsApi', { jsApiList: s(e.jsApiList) }, (e._complete = function (e) {
          if (y) {
            const n = e.checkResult
            n && (e.checkResult = JSON.parse(n))
          }
          e = (function (e) {
            let n = e.checkResult
            for (const i in n) {
              let t = f[i]
              t && (n[t] = n[i], delete n[i])
            }
            return e
          }(e))
        }, e))
      },
      onMenuShareTimeline(e) {
        i(p.onMenuShareTimeline, {
          complete() {
            c('shareTimeline', {
              title: e.title || m,
              desc: e.title || m,
              img_url: e.imgUrl || '',
              link: e.link || location.href,
              type: e.type || 'link',
              data_url: e.dataUrl || '',
            }, e)
          },
        }, e)
      },
      onMenuShareAppMessage(n) {
        i(p.onMenuShareAppMessage, {
          complete(e) {
            'favorite' === e.scene
              ? c('sendAppMessage', {
                title: n.title || m,
                desc: n.desc || '',
                link: n.link || location.href,
                img_url: n.imgUrl || '',
                type: n.type || 'link',
                data_url: n.dataUrl || '',
            })
              : c('sendAppMessage', {
                title: n.title || m,
                desc: n.desc || '',
                link: n.link || location.href,
                img_url: n.imgUrl || '',
                type: n.type || 'link',
                data_url: n.dataUrl || '',
            }, n)
          },
        }, n)
      },
      onMenuShareQQ(e) {
        i(p.onMenuShareQQ, {
          complete() {
            c('shareQQ', {
              title: e.title || m,
              desc: e.desc || '',
              img_url: e.imgUrl || '',
              link: e.link || location.href,
            }, e)
          },
        }, e)
      },
      onMenuShareWeibo(e) {
        i(p.onMenuShareWeibo, {
          complete() {
            c('shareWeiboApp', {
              title: e.title || m,
              desc: e.desc || '',
              img_url: e.imgUrl || '',
              link: e.link || location.href,
            }, e)
          },
        }, e)
      },
      onMenuShareQZone(e) {
        i(p.onMenuShareQZone, {
          complete() {
            c('shareQZone', {
              title: e.title || m,
              desc: e.desc || '',
              img_url: e.imgUrl || '',
              link: e.link || location.href,
            }, e)
          },
        }, e)
      },
      updateTimelineShareData(e) {
        c('updateTimelineShareData', { title: e.title, link: e.link, imgUrl: e.imgUrl }, e)
      },
      updateAppMessageShareData(e) {
        c('updateAppMessageShareData', { title: e.title, desc: e.desc, link: e.link, imgUrl: e.imgUrl }, e)
      },
      startRecord(e) {
        c('startRecord', {}, e)
      },
      stopRecord(e) {
        c('stopRecord', {}, e)
      },
      onVoiceRecordEnd(e) {
        i('onVoiceRecordEnd', e)
      },
      playVoice(e) {
        c('playVoice', { localId: e.localId }, e)
      },
      pauseVoice(e) {
        c('pauseVoice', { localId: e.localId }, e)
      },
      stopVoice(e) {
        c('stopVoice', { localId: e.localId }, e)
      },
      onVoicePlayEnd(e) {
        i('onVoicePlayEnd', e)
      },
      uploadVoice(e) {
        c('uploadVoice', { localId: e.localId, isShowProgressTips: e.isShowProgressTips == 0 ? 0 : 1 }, e)
      },
      downloadVoice(e) {
        c('downloadVoice', { serverId: e.serverId, isShowProgressTips: e.isShowProgressTips == 0 ? 0 : 1 }, e)
      },
      translateVoice(e) {
        c('translateVoice', { localId: e.localId, isShowProgressTips: e.isShowProgressTips == 0 ? 0 : 1 }, e)
      },
      chooseImage(e) {
        c('chooseImage', {
          scene: '1|2',
          count: e.count || 9,
          sizeType: e.sizeType || ['original', 'compressed'],
          sourceType: e.sourceType || ['album', 'camera'],
        }, (e._complete = function (e) {
          if (y) {
            const n = e.localIds
            try {
              n && (e.localIds = JSON.parse(n))
            }
            catch (e) {
            }
          }
        }, e))
      },
      getLocation(e) {
      },
      previewImage(e) {
        c(p.previewImage, { current: e.current, urls: e.urls }, e)
      },
      uploadImage(e) {
        c('uploadImage', { localId: e.localId, isShowProgressTips: e.isShowProgressTips == 0 ? 0 : 1 }, e)
      },
      downloadImage(e) {
        c('downloadImage', { serverId: e.serverId, isShowProgressTips: e.isShowProgressTips == 0 ? 0 : 1 }, e)
      },
      getLocalImgData(e) {
        !1 === A
          ? (A = !0, c('getLocalImgData', { localId: e.localId }, (e._complete = function (e) {
              if (A = !1, V.length > 0) {
                let n = V.shift()
                wx.getLocalImgData(n)
              }
            }, e)))
          : V.push(e)
      },
      getNetworkType(e) {
        c('getNetworkType', {}, (e._complete = function (e) {
          e = (function (e) {
            let n = e.errMsg
            e.errMsg = 'getNetworkType:ok'
            var i = e.subtype
            if (delete e.subtype, i)
              e.networkType = i; else {
              let t = n.indexOf(':'); let o = n.substring(t + 1)
              switch (o) {
                case 'wifi':
                case 'edge':
                case 'wwan':
                  e.networkType = o
                  break;
                default:
                  e.errMsg = 'getNetworkType:fail'
              }
            }
            return e
          }(e))
        }, e))
      },
      openLocation(e) {
        c('openLocation', {
          latitude: e.latitude,
          longitude: e.longitude,
          name: e.name || '',
          address: e.address || '',
          scale: e.scale || 28,
          infoUrl: e.infoUrl || '',
        }, e)
      },
      getLocation(e) {
        c(p.getLocation, { type: (e = e || {}).type || 'wgs84' }, (e._complete = function (e) {
          delete e.type
        }, e))
      },
      hideOptionMenu(e) {
        c('hideOptionMenu', {}, e)
      },
      showOptionMenu(e) {
        c('showOptionMenu', {}, e)
      },
      closeWindow(e) {
        c('closeWindow', {}, e = e || {})
      },
      hideMenuItems(e) {
        c('hideMenuItems', { menuList: e.menuList }, e)
      },
      showMenuItems(e) {
        c('showMenuItems', { menuList: e.menuList }, e)
      },
      hideAllNonBaseMenuItem(e) {
        c('hideAllNonBaseMenuItem', {}, e)
      },
      showAllNonBaseMenuItem(e) {
        c('showAllNonBaseMenuItem', {}, e)
      },
      scanQRCode(e) {
        c('scanQRCode', {
          needResult: (e = e || {}).needResult || 0,
          scanType: e.scanType || ['qrCode', 'barCode'],
        }, (e._complete = function (e) {
          if (_) {
            const n = e.resultStr
            if (n) {
              const i = JSON.parse(n)
              e.resultStr = i && i.scan_code && i.scan_code.scan_result
            }
          }
        }, e))
      },
      openAddress(e) {
        c(p.openAddress, {}, (e._complete = function (e) {
          let n;
          (n = e).postalCode = n.addressPostalCode, delete n.addressPostalCode, n.provinceName = n.proviceFirstStageName, delete n.proviceFirstStageName, n.cityName = n.addressCitySecondStageName, delete n.addressCitySecondStageName, n.countryName = n.addressCountiesThirdStageName, delete n.addressCountiesThirdStageName, n.detailInfo = n.addressDetailInfo, delete n.addressDetailInfo, e = n
        }, e))
      },
      openProductSpecificView(e) {
        c(p.openProductSpecificView, { pid: e.productId, view_type: e.viewType || 0, ext_info: e.extInfo }, e)
      },
      addCard(e) {
        for (var n = e.cardList, i = [], t = 0, o = n.length; t < o; ++t) {
          const r = n[t]; const a = { card_id: r.cardId, card_ext: r.cardExt }
          i.push(a)
        }
        c(p.addCard, { card_list: i }, (e._complete = function (e) {
          let n = e.card_list
          if (n) {
            for (let i = 0, t = (n = JSON.parse(n)).length; i < t; ++i) {
              const o = n[i]
              o.cardId = o.card_id, o.cardExt = o.card_ext, o.isSuccess = !!o.is_succ, delete o.card_id, delete o.card_ext, delete o.is_succ
            }
            e.cardList = n, delete e.card_list
          }
        }, e))
      },
      chooseCard(e) {
        c('chooseCard', {
          app_id: M.appId,
          location_id: e.shopId || '',
          sign_type: e.signType || 'SHA1',
          card_id: e.cardId || '',
          card_type: e.cardType || '',
          card_sign: e.cardSign,
          time_stamp: `${e.timestamp}`,
          nonce_str: e.nonceStr,
        }, (e._complete = function (e) {
          e.cardList = e.choose_card_info, delete e.choose_card_info
        }, e))
      },
      openCard(e) {
        for (var n = e.cardList, i = [], t = 0, o = n.length; t < o; ++t) {
          const r = n[t]; const a = { card_id: r.cardId, code: r.code }
          i.push(a)
        }
        c(p.openCard, { card_list: i }, e)
      },
      consumeAndShareCard(e) {
        c(p.consumeAndShareCard, { consumedCardId: e.cardId, consumedCode: e.code }, e)
      },
      chooseWXPay(e) {
        c(p.chooseWXPay, n(e), e)
      },
      openEnterpriseRedPacket(e) {
        c(p.openEnterpriseRedPacket, n(e), e)
      },
      startSearchBeacons(e) {
        c(p.startSearchBeacons, { ticket: e.ticket }, e)
      },
      stopSearchBeacons(e) {
        c(p.stopSearchBeacons, {}, e)
      },
      onSearchBeacons(e) {
        i(p.onSearchBeacons, e)
      },
      openEnterpriseChat(e) {
        c('openEnterpriseChat', { useridlist: e.userIds, chatname: e.groupName }, e)
      },
      launchMiniProgram(e) {
        c('launchMiniProgram', {
          targetAppId: e.targetAppId,
          path: (function (e) {
            if (typeof e == 'string' && e.length > 0) {
              let n = e.split('?')[0]; let i = e.split('?')[1]
              return n += '.html', void 0 !== i ? `${n }?${ i}` : n
            }
          }(e.path)),
          envVersion: e.envVersion,
        }, e)
      },
      miniProgram: {
        navigateBack(e) {
          e = e || {}, u(() => {
            c('invokeMiniProgramAPI', { name: 'navigateBack', arg: { delta: e.delta || 1 } }, e)
          })
        },
        navigateTo(e) {
          u(() => {
            c('invokeMiniProgramAPI', { name: 'navigateTo', arg: { url: e.url } }, e)
          })
        },
        redirectTo(e) {
          u(() => {
            c('invokeMiniProgramAPI', { name: 'redirectTo', arg: { url: e.url } }, e)
          })
        },
        switchTab(e) {
          u(() => {
            c('invokeMiniProgramAPI', { name: 'switchTab', arg: { url: e.url } }, e)
          })
        },
        reLaunch(e) {
          u(() => {
            c('invokeMiniProgramAPI', { name: 'reLaunch', arg: { url: e.url } }, e)
          })
        },
        postMessage(e) {
          u(() => {
            c('invokeMiniProgramAPI', { name: 'postMessage', arg: e.data || {} }, e)
          })
        },
        getEnv(e) {
          u(() => {
            e({ miniprogram: o.__wxjs_environment === 'miniprogram' })
          })
        },
      },
    }; let L = 1; const B = {}
    return t.addEventListener('error', (e) => {
      if (!y) {
        const n = e.target; const i = n.tagName; const t = n.src
        if ((i == 'IMG' || i == 'VIDEO' || i == 'AUDIO' || i == 'SOURCE') && t.includes('wxlocalresource://')) {
          e.preventDefault(), e.stopPropagation()
          let o = n['wx-id']
          if (o || (o = L++, n['wx-id'] = o), B[o])
            return
          B[o] = !0, wx.ready(() => {
            wx.getLocalImgData({
              localId: t,
              success(e) {
                n.src = e.localData
              },
            })
          })
        }
      }
    }, !0), t.addEventListener('load', (e) => {
      if (!y) {
        const n = e.target; const i = n.tagName
        if (n.src, i == 'IMG' || i == 'VIDEO' || i == 'AUDIO' || i == 'SOURCE') {
          const t = n['wx-id']
          t && (B[t] = !1)
        }
      }
    }, !0), e && (o.wx = o.jWeixin = C), C
  }
  let O
}))
// #endif
