import { useAuthStore } from '@/store/user'
import { appInfo } from '@/common'
// 不需要任何token的页面
const whiteList = [
  '/pages/login/index',
  '/pages/iframe',
]
// 需要真实token，而不是默认token的页面
const needList = [
  '/pages/search/index',
  '/pages/personal/earningsInfo/order/index',
  '/pages/personal/userProfile/withdrawalRecord/index',
  '/pages/personal/earningsInfo/fans/index',
  '/pages/personal/earningsInfo/favorites/index',
  '/pages/personal/earningsInfo/share/index',
  '/pages/search/turnChain',
  '/pages/personal/commonFunctions/suggestionFeedback/index',
  '/pages/setting/index',
]
const { userToken } = useAuthStore()
const routeFnList = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
/**
 * 判断用户是否有权限访问指定url
 *
 * @param url 需要判断的url
 * @returns 如果在白名单中或有token，则返回true，否则返回false
 */
function hasPermission(url: string) {
  // 截取url?前面的
  url = url.split('?')[0]
  // 在白名单中，直接跳转
  if (whiteList.includes(url)) {
    return true
  }
  // 无token
  if (!userToken.value) {
    return false
  }
  // 有token，需要判断是否默认token
  if (userToken.value === appInfo.defaultTk) {
    return !needList.includes(url)
  }
  return true
}
/**
 * 页面权限校验
 */
export function initPermission() {
  routeFnList.forEach((item) => {
    uni.addInterceptor(item, {
      invoke(e) {
        if (!hasPermission(e.url)) {
          uni.setStorageSync('URL', e.url)
          console.log('[need login page]', e.url)
          uni.navigateTo({
            url: '/pages/login/index',
          })
          return false
        }
        return true
      },
      success(res) {
        console.log('invoke-success', res)
      },
      fail(err) {
        console.log('err', err)
      },
    })
  })
}
